#!/bin/bash

# Service Monitor for Proxmox Infrastructure
echo "🔍 Proxmox Service Monitor"
echo "=========================="
echo ""

# Function to check service
check_service() {
    local name="$1"
    local url="$2"
    local expected="$3"
    
    echo -n "$name: "
    code=$(curl -k -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
    
    if [[ "$code" == "$expected" ]]; then
        echo "✅ OK ($code)"
    elif [[ "$code" == "000" ]]; then
        echo "❌ UNREACHABLE"
    else
        echo "⚠️  Unexpected ($code)"
    fi
}

# Monitor loop
while true; do
    clear
    echo "🔍 Proxmox Service Monitor - $(date)"
    echo "=========================================="
    echo ""
    
    echo "Main Services:"
    check_service "Proxmox Web UI  " "https://*************:8006" "200"
    check_service "Backup Server   " "https://************:8007" "200"
    check_service "Dokploy         " "http://************:3000" "200"
    check_service "Browser-MCP     " "http://************:8000" "200"
    check_service "Home Assistant  " "http://************:8123" "200"
    
    echo ""
    echo "Network Status:"
    for ip in 100 17 19 22 23 29; do
        echo -n "192.168.1.$ip: "
        if ping -c 1 -W 1 192.168.1.$ip >/dev/null 2>&1; then
            echo "✅ Online"
        else
            echo "❌ Offline"
        fi
    done
    
    echo ""
    echo "Press Ctrl+C to exit, refreshing in 10 seconds..."
    sleep 10
done