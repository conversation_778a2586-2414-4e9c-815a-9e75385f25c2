# IT Assistant MCP Server

A containerized MCP (Model Context Protocol) server for comprehensive IT infrastructure management, designed to work seamlessly with Ace and the Apple ecosystem.

## Features

### 🖥️ System Management
- **Inventory Management**: Track servers, workstations, network devices, storage, VMs, containers, IoT, and security devices
- **Real-time Status Monitoring**: Check system availability with ping and SSH
- **Remote Command Execution**: Execute commands on remote systems via SSH
- **Natural Language Search**: Find systems using intuitive queries

### 📊 Monitoring & Metrics
- **Performance Metrics**: CPU, memory, disk, network, and process monitoring
- **Historical Data**: Store and query historical metrics
- **Bulk Operations**: Check status of multiple systems simultaneously
- **Alert Management**: Track and manage system alerts

### 🔒 Security
- **Containerized Deployment**: Runs in Apple's secure container framework
- **Read-only Filesystem**: Enhanced security with minimal attack surface
- **SSH Key Management**: Secure credential handling with mounted SSH keys
- **Capability Restrictions**: Minimal Linux capabilities for security

## Quick Start

### Prerequisites
- Apple Silicon Mac (M1/M2/M3) with macOS 15.0+
- Node.js 18+ installed
- Apple container runtime or Docker Desktop

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/it-assistant-mcp.git
   cd it-assistant-mcp
   ```

2. **Run the setup tool**:
   ```bash
   ./setup-it-assistant
   ```

3. **Choose "Full setup" and follow the prompts**

The setup tool will:
- Check prerequisites
- Create configuration files
- Build the container image
- Start the MCP server
- Configure Ace integration

## Manual Installation

If you prefer manual setup:

```bash
# Install dependencies
npm install

# Build container
./container/build.sh

# Run container
./container/run.sh

# Test the server
npm test
```

## Configuration

Configuration file is created at `~/.it-assistant-mcp/config.env`:

```env
# Database location
IT_ASSISTANT_DB_PATH=/app/data/systems.db

# Node environment
NODE_ENV=production

# Optional integrations
PROMETHEUS_ENDPOINT=http://prometheus:9090
GRAFANA_ENDPOINT=http://grafana:3000
```

## Usage with Ace

Add to your `ace.yml` configuration:

```yaml
mcpServers:
    it-assistant:
        type: stdio
        command: container
        args:
            - exec
            - -i
            - it-assistant-mcp
            - node
            - /app/src/index.js
```

### Example Commands in Ace

- **Add a system**: "Add server web-01 at 192.168.1.10 running Ubuntu 22.04"
- **Check status**: "Check status of all servers"
- **Search systems**: "Show me all offline systems in datacenter A"
- **Execute command**: "Run uptime on web-01"
- **Get metrics**: "Show CPU and memory usage for database servers"

## Available Tools

### System Management
- `list_systems` - List all systems with optional filtering
- `get_system` - Get detailed information about a specific system
- `add_system` - Add a new system to inventory
- `update_system` - Update system information
- `delete_system` - Remove a system from inventory

### Monitoring
- `check_system_status` - Check current status of a system
- `get_system_metrics` - Get performance metrics
- `bulk_status_check` - Check status of multiple systems

### Operations
- `execute_command` - Execute commands via SSH
- `search_systems` - Natural language system search

## Data Storage

Data is stored in SQLite database at `~/.it-assistant-mcp/data/systems.db`

### Database Schema

**Systems Table**:
- Basic info: ID, name, type, IP address, OS
- Location and description
- SSH configuration
- Hardware specs (CPU, memory, storage)
- Timestamps and status

**Metrics Table**:
- System performance data
- Historical tracking
- Multiple metric types

**Alerts Table**:
- System alerts and issues
- Severity levels
- Resolution tracking

## Container Management

```bash
# View logs
container logs -f it-assistant-mcp

# Stop server
container stop it-assistant-mcp

# Start server
container start it-assistant-mcp

# Access shell
container exec -it it-assistant-mcp /bin/sh

# Remove container
container rm -f it-assistant-mcp
```

## Development

### Running locally (without container):

```bash
# Install dependencies
npm install

# Set environment variable
export IT_ASSISTANT_DB_PATH=./data/systems.db

# Run server
npm start

# Run with file watching
npm run dev

# Test with MCP inspector
npm run inspector
```

### Adding New Features

1. Add tool definition in `src/index.js`
2. Implement logic in appropriate manager class
3. Update database schema if needed
4. Add tests in `src/test.js`

## Security Considerations

- SSH keys are mounted read-only from `~/.ssh`
- Container runs as non-root user
- Minimal capabilities (only NET_RAW for ping)
- Read-only root filesystem
- No new privileges flag set

## Troubleshooting

### Container won't start
- Check if port is already in use
- Verify container runtime is installed
- Check logs: `container logs it-assistant-mcp`

### SSH connections failing
- Ensure SSH keys are properly configured
- Check target system allows key-based auth
- Verify network connectivity

### Database errors
- Check data directory permissions
- Ensure sufficient disk space
- Review logs for specific errors

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

## License

MIT License - See LICENSE file for details

## Acknowledgments

- Built for integration with Ace AI assistant
- Inspired by AI-IT-assistant SwiftUI project
- Uses MCP SDK for protocol implementation
- Optimized for Apple's container framework