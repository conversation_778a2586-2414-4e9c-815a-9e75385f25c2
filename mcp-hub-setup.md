# MCP Hub - Centralized MCP Server Catalog & Toolkit

## Overview
A centralized MCP server hub that hosts all your MCP servers, provides a catalog interface, and enables remote access from any client.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     MCP Hub (Container 109)                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌──────────────┐  ┌───────────────┐ │
│  │   Web Catalog   │  │  MCP Proxy   │  │  MCP Registry │ │
│  │   (Port 3100)   │  │ (Port 3101)  │  │   (Storage)   │ │
│  └─────────────────┘  └──────────────┘  └───────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Hosted MCP Servers                      │   │
│  │  ┌──────────┐  ┌──────────┐  ┌─────────────────┐  │   │
│  │  │    IT    │  │ Browser  │  │    Proxmox      │  │   │
│  │  │Assistant │  │   Use    │  │   Production    │  │   │
│  │  └──────────┘  └──────────┘  └─────────────────┘  │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              ↕
                    Remote MCP Clients
```

## Components

### 1. MCP Hub Server
- **Web Catalog**: Browse and search all available MCP servers
- **MCP Proxy**: Route requests to appropriate MCP servers
- **Registry**: Store MCP server configurations and metadata
- **API**: RESTful API for managing MCP servers

### 2. Features
- Web-based catalog interface
- Remote MCP server access via HTTP/WebSocket
- Authentication and authorization
- Server health monitoring
- Auto-discovery of MCP servers
- Docker and native MCP server support

## Implementation Plan

### Phase 1: Container Setup
```bash
# Create LXC container for MCP Hub
pct create 109 /var/lib/vz/template/cache/debian-12-standard_12.7-1_amd64.tar.zst \
  --hostname mcp-hub \
  --memory 4096 \
  --cores 2 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --storage local \
  --rootfs local:16 \
  --unprivileged 1 \
  --features nesting=1
```

### Phase 2: MCP Hub Application

#### Core Server (Node.js/TypeScript)
```typescript
// mcp-hub-server/src/index.ts
import express from 'express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { MCPRegistry } from './registry';
import { MCPProxy } from './proxy';
import { MCPCatalog } from './catalog';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Initialize components
const registry = new MCPRegistry();
const proxy = new MCPProxy(registry);
const catalog = new MCPCatalog(registry);

// Web UI
app.use('/', express.static('public'));
app.use('/api/catalog', catalog.router);
app.use('/api/registry', registry.router);

// MCP Proxy endpoint
app.use('/mcp/:serverId', proxy.handler);

// WebSocket for real-time MCP communication
wss.on('connection', (ws, req) => {
  const serverId = req.url?.split('/')[2];
  if (serverId) {
    proxy.handleWebSocket(serverId, ws);
  }
});

server.listen(3100, () => {
  console.log('MCP Hub running on port 3100');
});
```

#### Registry Schema
```typescript
interface MCPServer {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'docker' | 'native' | 'remote';
  transport: 'stdio' | 'http' | 'websocket';
  connection: {
    command?: string;
    dockerImage?: string;
    url?: string;
    env?: Record<string, string>;
  };
  capabilities: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    sampling?: boolean;
  };
  metadata: {
    author?: string;
    homepage?: string;
    tags?: string[];
    icon?: string;
  };
  health: {
    status: 'online' | 'offline' | 'error';
    lastCheck: Date;
    uptime?: number;
  };
}
```

### Phase 3: Docker Compose Setup

```yaml
# docker-compose.yml
version: '3.8'

services:
  mcp-hub:
    build: .
    container_name: mcp-hub
    restart: unless-stopped
    ports:
      - "3100:3100"  # Web UI & API
      - "3101:3101"  # MCP Proxy
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/mcphub
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - db
      - redis
    networks:
      - mcp-network

  db:
    image: postgres:15
    container_name: mcp-hub-db
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mcp-network

  redis:
    image: redis:7-alpine
    container_name: mcp-hub-redis
    volumes:
      - redis-data:/data
    networks:
      - mcp-network

  # Example MCP servers running in containers
  it-assistant:
    image: it-assistant-mcp:latest
    container_name: mcp-it-assistant
    restart: unless-stopped
    environment:
      - MCP_MODE=server
    networks:
      - mcp-network

  browser-use:
    image: browser-use-mcp:latest
    container_name: mcp-browser-use
    restart: unless-stopped
    environment:
      - MCP_MODE=server
    networks:
      - mcp-network

volumes:
  postgres-data:
  redis-data:

networks:
  mcp-network:
    driver: bridge
```

### Phase 4: Web UI

```html
<!-- public/index.html -->
<!DOCTYPE html>
<html>
<head>
  <title>MCP Hub - Server Catalog</title>
  <link rel="stylesheet" href="/css/style.css">
</head>
<body>
  <div id="app">
    <header>
      <h1>MCP Server Hub</h1>
      <nav>
        <a href="#catalog">Catalog</a>
        <a href="#installed">Installed</a>
        <a href="#monitor">Monitor</a>
        <a href="#settings">Settings</a>
      </nav>
    </header>
    
    <main>
      <section id="catalog">
        <h2>Available MCP Servers</h2>
        <div class="search-bar">
          <input type="text" placeholder="Search servers..." />
          <select>
            <option>All Categories</option>
            <option>Development</option>
            <option>Infrastructure</option>
            <option>AI/ML</option>
            <option>Automation</option>
          </select>
        </div>
        
        <div class="server-grid">
          <!-- Server cards will be dynamically loaded -->
        </div>
      </section>
    </main>
  </div>
  
  <script src="/js/app.js"></script>
</body>
</html>
```

### Phase 5: MCP Proxy Implementation

```typescript
// mcp-hub-server/src/proxy.ts
export class MCPProxy {
  private connections: Map<string, MCPConnection> = new Map();
  
  async handleRequest(serverId: string, request: any): Promise<any> {
    const server = await this.registry.getServer(serverId);
    if (!server) {
      throw new Error('Server not found');
    }
    
    let connection = this.connections.get(serverId);
    if (!connection) {
      connection = await this.createConnection(server);
      this.connections.set(serverId, connection);
    }
    
    return connection.sendRequest(request);
  }
  
  private async createConnection(server: MCPServer): Promise<MCPConnection> {
    switch (server.type) {
      case 'docker':
        return new DockerMCPConnection(server);
      case 'native':
        return new NativeMCPConnection(server);
      case 'remote':
        return new RemoteMCPConnection(server);
      default:
        throw new Error(`Unknown server type: ${server.type}`);
    }
  }
}
```

### Phase 6: Auto-Discovery

```typescript
// mcp-hub-server/src/discovery.ts
export class MCPDiscovery {
  async scanDocker(): Promise<MCPServer[]> {
    const containers = await docker.listContainers({
      filters: { label: ['mcp.server=true'] }
    });
    
    return containers.map(container => ({
      id: container.Names[0],
      name: container.Labels['mcp.name'],
      type: 'docker',
      // ... extract other metadata
    }));
  }
  
  async scanLocal(): Promise<MCPServer[]> {
    const configPaths = [
      '/etc/mcp/servers',
      '/usr/local/mcp/servers',
      '~/.mcp/servers'
    ];
    
    const servers = [];
    for (const path of configPaths) {
      if (fs.existsSync(path)) {
        const configs = await fs.readdir(path);
        // Load and parse configs
      }
    }
    return servers;
  }
}
```

## Client Configuration

### Claude Desktop
```json
{
  "mcpServers": {
    "mcp-hub": {
      "command": "npx",
      "args": ["mcp-remote-client", "http://192.168.1.X:3100/mcp"]
    }
  }
}
```

### Remote Access Script
```bash
#!/bin/bash
# mcp-remote-connect.sh
MCP_HUB_URL="http://your-domain.com:3100"
MCP_TOKEN="your-auth-token"

npx mcp-remote-client \
  --hub-url "$MCP_HUB_URL" \
  --token "$MCP_TOKEN" \
  --server "$1"
```

## Security Considerations

1. **Authentication**
   - JWT tokens for API access
   - API key management
   - OAuth2 integration (optional)

2. **Authorization**
   - Role-based access control
   - Per-server permissions
   - Resource quotas

3. **Network Security**
   - HTTPS/WSS for external access
   - VPN integration option
   - IP whitelisting

4. **Container Isolation**
   - Separate network for MCP servers
   - Resource limits per container
   - Read-only filesystem where possible

## Deployment Steps

1. **Create Container**
   ```bash
   # On Proxmox host
   pct create 109 debian-12-standard_12.7-1_amd64.tar.zst \
     --hostname mcp-hub \
     --memory 4096 \
     --cores 2 \
     --net0 name=eth0,bridge=vmbr0,ip=dhcp \
     --storage local \
     --rootfs local:16
   ```

2. **Install Dependencies**
   ```bash
   # In container
   apt update && apt install -y docker.io docker-compose nodejs npm git
   ```

3. **Deploy MCP Hub**
   ```bash
   git clone https://github.com/your-repo/mcp-hub.git
   cd mcp-hub
   docker-compose up -d
   ```

4. **Add MCP Servers**
   - Upload server packages
   - Configure via web UI
   - Or use API for automation

## Integration Examples

### Adding IT Assistant MCP
```bash
curl -X POST http://mcp-hub:3100/api/registry/servers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "it-assistant",
    "description": "Enterprise IT management assistant",
    "type": "docker",
    "connection": {
      "dockerImage": "it-assistant-mcp:latest"
    },
    "capabilities": {
      "tools": true,
      "resources": true,
      "prompts": true
    }
  }'
```

### Accessing from Claude
```bash
# Add to Claude Desktop config
claude mcp add mcp-hub "npx mcp-remote http://192.168.1.X:3100" -s user
```

## Benefits

1. **Centralized Management**
   - Single place for all MCP servers
   - Easy updates and maintenance
   - Consistent configuration

2. **Remote Access**
   - Access MCP servers from anywhere
   - No local installation required
   - Works across different clients

3. **Scalability**
   - Add new servers easily
   - Load balancing capability
   - Horizontal scaling option

4. **Monitoring**
   - Health checks
   - Usage statistics
   - Performance metrics

Would you like me to start implementing this MCP Hub on your Proxmox server?