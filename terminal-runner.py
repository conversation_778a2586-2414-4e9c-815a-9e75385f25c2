#!/usr/bin/env python3

import subprocess
import tempfile
import time
import os
import sys
import json
from datetime import datetime

class TerminalRunner:
    def __init__(self):
        self.sessions = {}
        
    def launch_terminal(self, command, title="Terminal Session"):
        """Launch a command in a new terminal and return session info"""
        session_id = f"session_{int(time.time() * 1000)}"
        output_file = f"/tmp/terminal_session_{session_id}.txt"
        status_file = f"/tmp/terminal_status_{session_id}.txt"
        
        # Wrap command to capture output and status
        wrapped_cmd = f"""
        echo 'STARTED' > {status_file}
        echo '🚀 {title}' | tee {output_file}
        echo '============================' | tee -a {output_file}
        ({command}) 2>&1 | tee -a {output_file}
        echo "EXIT_CODE: $?" >> {output_file}
        echo 'COMPLETED' > {status_file}
        """
        
        # AppleScript to launch terminal
        applescript = f'''
        tell application "Terminal"
            activate
            set newTab to do script "{wrapped_cmd}"
            set window_id to id of window 1
            return window_id
        end tell
        '''
        
        # Launch the terminal
        result = subprocess.run(['osascript', '-e', applescript], 
                               capture_output=True, text=True)
        
        window_id = result.stdout.strip()
        
        self.sessions[session_id] = {
            'window_id': window_id,
            'output_file': output_file,
            'status_file': status_file,
            'command': command,
            'title': title,
            'started': datetime.now().isoformat()
        }
        
        return session_id
    
    def is_running(self, session_id):
        """Check if a session is still running"""
        if session_id not in self.sessions:
            return False
            
        status_file = self.sessions[session_id]['status_file']
        if os.path.exists(status_file):
            with open(status_file, 'r') as f:
                status = f.read().strip()
                return status != 'COMPLETED'
        return False
    
    def get_output(self, session_id, tail_lines=None):
        """Get output from a session"""
        if session_id not in self.sessions:
            return "Session not found"
            
        output_file = self.sessions[session_id]['output_file']
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                lines = f.readlines()
                if tail_lines:
                    return ''.join(lines[-tail_lines:])
                return ''.join(lines)
        return "No output yet"
    
    def wait_for_completion(self, session_id, timeout=300):
        """Wait for a session to complete"""
        start_time = time.time()
        while self.is_running(session_id):
            if time.time() - start_time > timeout:
                return False
            time.sleep(0.5)
        return True
    
    def run_parallel_commands(self, commands):
        """Run multiple commands in parallel terminals"""
        sessions = []
        for cmd_info in commands:
            if isinstance(cmd_info, str):
                session_id = self.launch_terminal(cmd_info)
            else:
                session_id = self.launch_terminal(cmd_info['command'], 
                                                 cmd_info.get('title', 'Command'))
            sessions.append(session_id)
            time.sleep(0.5)  # Small delay between launches
        return sessions
    
    def monitor_sessions(self, sessions, update_interval=1):
        """Monitor multiple sessions and show progress"""
        print("Monitoring sessions...")
        all_complete = False
        
        while not all_complete:
            os.system('clear')
            print(f"Session Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            all_complete = True
            for session_id in sessions:
                session = self.sessions.get(session_id, {})
                is_running = self.is_running(session_id)
                if is_running:
                    all_complete = False
                
                status = "🟢 Running" if is_running else "✅ Complete"
                print(f"\n{status} {session.get('title', 'Unknown')}")
                print(f"  Session: {session_id}")
                
                # Show last few lines of output
                output = self.get_output(session_id, tail_lines=3)
                for line in output.strip().split('\n')[-3:]:
                    print(f"  > {line[:70]}")
            
            if not all_complete:
                time.sleep(update_interval)
        
        print("\n\nAll sessions completed!")
        return True

# Command-line interface
if __name__ == "__main__":
    runner = TerminalRunner()
    
    if len(sys.argv) < 2:
        print("Usage: terminal-runner.py <command> [args...]")
        print("\nCommands:")
        print("  run <command>         - Run a single command")
        print("  parallel              - Run multiple commands in parallel")
        print("  service-check         - Check all Proxmox services")
        print("  container-status      - Check container status")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "run":
        if len(sys.argv) < 3:
            print("Usage: terminal-runner.py run <command>")
            sys.exit(1)
        
        cmd = ' '.join(sys.argv[2:])
        session_id = runner.launch_terminal(cmd, "Custom Command")
        print(f"Launched session: {session_id}")
        
        if runner.wait_for_completion(session_id, timeout=60):
            print("\nOutput:")
            print(runner.get_output(session_id))
    
    elif command == "parallel":
        # Example parallel commands
        commands = [
            {"command": "ping -c 5 *************", "title": "Ping Proxmox"},
            {"command": "ping -c 5 ************", "title": "Ping Dokploy"},
            {"command": "ping -c 5 ************", "title": "Ping Browser-MCP"},
        ]
        
        sessions = runner.run_parallel_commands(commands)
        runner.monitor_sessions(sessions)
        
    elif command == "service-check":
        # Check all services
        session_id = runner.launch_terminal("""
            echo 'Checking all Proxmox services...'
            echo ''
            
            # Check Proxmox host
            echo '=== Proxmox Host (*************) ==='
            curl -k -s -o /dev/null -w 'HTTPS (8006): %{http_code}\\n' https://*************:8006
            
            # Check containers
            echo ''
            echo '=== Container Services ==='
            
            echo 'PBS (************):'
            curl -k -s -o /dev/null -w '  HTTPS (8007): %{http_code}\\n' https://************:8007
            
            echo 'Dokploy (************):'
            curl -s -o /dev/null -w '  HTTP (3000): %{http_code}\\n' http://************:3000
            
            echo 'Browser-MCP (************):'
            curl -s -o /dev/null -w '  HTTP (8000): %{http_code}\\n' http://************:8000
            
            echo ''
            echo 'Service check complete!'
        """, "Service Status Check")
        
        print(f"Checking services in terminal...")
        runner.wait_for_completion(session_id)
        print(runner.get_output(session_id))
        
    elif command == "container-status":
        # Check container status via SSH
        session_id = runner.launch_terminal("""
            ssh root@************* '
                echo "Container Status Check"
                echo "===================="
                
                for id in 100 101 105 106 107; do
                    echo ""
                    echo "Container $id:"
                    pct status $id 2>/dev/null || echo "  Not found"
                    
                    if pct status $id 2>/dev/null | grep -q running; then
                        IP=$(pct exec $id -- ip -4 addr show eth0 2>/dev/null | grep inet | awk "{print \\$2}" | cut -d/ -f1)
                        echo "  IP: ${IP:-Unknown}"
                    fi
                done
            '
        """, "Container Status")
        
        print("Checking container status...")
        runner.wait_for_completion(session_id)
        print(runner.get_output(session_id))