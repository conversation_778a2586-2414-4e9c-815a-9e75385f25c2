#!/bin/bash

# Build script for IT Assistant MCP Server container
# Uses Apple's containerization framework

set -e

echo "🔨 Building IT Assistant MCP Server container..."

# Check if running on Apple Silicon Mac
if [[ $(uname -m) != "arm64" ]] || [[ $(uname -s) != "Darwin" ]]; then
    echo "❌ Error: This script requires an Apple Silicon Mac"
    exit 1
fi

# Check for Apple's container runtime
if ! command -v container &> /dev/null; then
    echo "❌ Error: Apple container runtime not found"
    echo "Please ensure you have macOS 15.0+ with container support"
    exit 1
fi

# Build container
cd "$(dirname "$0")/.."

echo "📦 Building container image..."
container build \
    --tag it-assistant-mcp:latest \
    --tag it-assistant-mcp:1.0.0 \
    --file Containerfile \
    .

echo "✅ Container built successfully!"
echo ""
echo "📋 Image details:"
container images | grep it-assistant-mcp

echo ""
echo "🚀 To run the container, use: ./container/run.sh"