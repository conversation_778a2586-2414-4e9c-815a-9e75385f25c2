#!/bin/bash

# Run script for IT Assistant MCP Server container
# Configures volumes and environment for Apple's container framework

set -e

# Default configuration
CONTAINER_NAME="it-assistant-mcp"
DATA_DIR="${IT_ASSISTANT_DATA_DIR:-$HOME/.it-assistant-mcp/data}"
SSH_DIR="${HOME}/.ssh"
CONFIG_FILE="${IT_ASSISTANT_CONFIG:-$HOME/.it-assistant-mcp/config.env}"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 Starting IT Assistant MCP Server container...${NC}"

# Check if running on Apple Silicon Mac
if [[ $(uname -m) != "arm64" ]] || [[ $(uname -s) != "Darwin" ]]; then
    echo -e "${RED}❌ Error: This script requires an Apple Silicon Mac${NC}"
    exit 1
fi

# Create necessary directories
echo "📁 Setting up directories..."
mkdir -p "$DATA_DIR"
mkdir -p "$(dirname "$CONFIG_FILE")"

# Create config file if it doesn't exist
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${YELLOW}📝 Creating default configuration...${NC}"
    cat > "$CONFIG_FILE" << EOF
# IT Assistant MCP Server Configuration
# Edit these values as needed

# Database location (inside container)
IT_ASSISTANT_DB_PATH=/app/data/systems.db

# Node environment
NODE_ENV=production

# Optional: External monitoring endpoints
# PROMETHEUS_ENDPOINT=http://prometheus:9090
# GRAFANA_ENDPOINT=http://grafana:3000

# Optional: SSH key paths (mounted read-only)
# SSH_PRIVATE_KEY=/home/<USER>/.ssh/id_rsa
EOF
fi

# Check if container is already running
if container ps | grep -q "$CONTAINER_NAME"; then
    echo -e "${YELLOW}⚠️  Container is already running${NC}"
    echo "Stop it with: container stop $CONTAINER_NAME"
    exit 1
fi

# Remove old container if exists
if container ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🧹 Removing old container..."
    container rm "$CONTAINER_NAME" > /dev/null 2>&1 || true
fi

# Run the container
echo -e "${GREEN}🏃 Running container...${NC}"
container run \
    --name "$CONTAINER_NAME" \
    --detach \
    --restart unless-stopped \
    --volume "$DATA_DIR:/app/data:rw" \
    --volume "$SSH_DIR:/home/<USER>/.ssh:ro" \
    --env-file "$CONFIG_FILE" \
    --cap-drop ALL \
    --cap-add NET_RAW \
    --security-opt no-new-privileges:true \
    --read-only \
    --tmpfs /tmp:noexec,nosuid,size=10M \
    it-assistant-mcp:latest

# Wait for container to start
echo "⏳ Waiting for container to be healthy..."
for i in {1..30}; do
    if container exec "$CONTAINER_NAME" echo '{"jsonrpc":"2.0","method":"tools/list","id":1}' | \
       node /app/src/index.js 2>/dev/null | grep -q "list_systems"; then
        echo -e "${GREEN}✅ Container is healthy!${NC}"
        break
    fi
    sleep 1
done

# Show container status
echo ""
echo "📊 Container status:"
container ps | grep "$CONTAINER_NAME"

echo ""
echo -e "${GREEN}🎉 IT Assistant MCP Server is running!${NC}"
echo ""
echo "📚 Useful commands:"
echo "  View logs:     container logs -f $CONTAINER_NAME"
echo "  Stop server:   container stop $CONTAINER_NAME"
echo "  Start server:  container start $CONTAINER_NAME"
echo "  Remove server: container rm -f $CONTAINER_NAME"
echo "  Shell access:  container exec -it $CONTAINER_NAME /bin/sh"
echo ""
echo "🔧 To use with Ace:"
echo "  1. Add to ace.yml configuration:"
echo "     it-assistant:"
echo "         type: stdio"
echo "         command: container"
echo "         args:"
echo "             - exec"
echo "             - -i"
echo "             - $CONTAINER_NAME"
echo "             - node"
echo "             - /app/src/index.js"
echo ""
echo "  2. Or use the setup tool: ./setup-it-assistant"