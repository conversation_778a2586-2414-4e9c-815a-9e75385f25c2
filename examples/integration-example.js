#!/usr/bin/env node
/**
 * IT Assistant MCP Integration Example
 * 
 * This example demonstrates how to use the IT Assistant MCP server
 * with your existing Proxmox and ElevenLabs setup.
 */

// Example 1: Daily Health Check with Voice Report
async function dailyHealthCheck() {
  console.log('Running daily health check...');
  
  // Check overall system health
  const health = await it_assistant.check_system_health({
    include_services: true,
    include_resources: true,
    include_vms: true,
    nodes: ['pve'] // Your Proxmox node
  });

  // Generate voice status report
  if (health.alerts.length > 0) {
    await it_assistant.send_voice_alert({
      severity: 'warning',
      message: `System health check found ${health.alerts.length} issues requiring attention`,
      recipients: ['ops-team']
    });
  }

  // Generate detailed report
  await it_assistant.generate_report({
    type: 'daily',
    include_metrics: true,
    include_incidents: true,
    format: 'pdf',
    send_voice_summary: true,
    recipients: ['<EMAIL>']
  });
}

// Example 2: Automated VM Backup
async function scheduleVMBackups() {
  console.log('Setting up automated VM backups...');
  
  // Schedule nightly backups
  await it_assistant.schedule_task({
    name: 'Nightly VM Backups',
    type: 'backup',
    schedule: '0 2 * * *', // 2 AM every day
    targets: ['vm-100', 'vm-101', 'vm-102'], // Your VM IDs
    enabled: true
  });
}

// Example 3: Incident Response Workflow
async function handleHighCPU() {
  console.log('Investigating high CPU usage...');
  
  // Diagnose the issue
  const diagnosis = await it_assistant.diagnose_issue({
    symptom: 'High CPU usage on web server',
    system: 'web-01',
    autoFix: false // Manual review first
  });

  // If critical, send voice alert
  if (diagnosis.possibleCauses.includes('high_cpu')) {
    await it_assistant.send_voice_alert({
      severity: 'critical',
      message: 'Web server experiencing high CPU usage. Immediate attention required.',
      recipients: ['on-call-team'],
      repeat: 3 // Repeat 3 times
    });
  }

  // Apply safe fixes
  if (diagnosis.autoFixAvailable) {
    await it_assistant.apply_fix({
      fixId: 'high_cpu',
      target: 'web-01',
      confirm: true
    });
  }
}

// Example 4: VM Lifecycle Management
async function createDevelopmentVM() {
  console.log('Creating new development VM...');
  
  // Create VM from template
  const result = await it_assistant.create_vm({
    name: 'dev-test-01',
    template: '9000', // Your template ID
    cores: 2,
    memory: 4096,
    disk: 50,
    network: 'vmbr0',
    node: 'pve'
  });

  // Schedule automatic shutdown at 6 PM
  await it_assistant.schedule_task({
    name: 'Shutdown dev VM',
    type: 'custom',
    schedule: '0 18 * * 1-5', // Weekdays at 6 PM
    targets: [result.vmid],
    script: `qm shutdown ${result.vmid}`,
    enabled: true
  });
}

// Example 5: Batch Operations
async function updateAllServers() {
  console.log('Running security updates on all servers...');
  
  // Get all running VMs
  const vms = await it_assistant.list_all_vms({
    filter: {
      status: 'running',
      tags: ['production']
    }
  });

  // Run security updates
  await it_assistant.run_playbook({
    playbook: 'security_patching',
    targets: vms.vms.map(vm => vm.name),
    variables: {
      reboot_if_required: false,
      notify_users: true
    },
    dryRun: true // Test first
  });
}

// Example 6: Custom Monitoring Alert
async function setupDiskSpaceMonitoring() {
  console.log('Setting up disk space monitoring...');
  
  // Schedule hourly disk space check
  await it_assistant.schedule_task({
    name: 'Disk Space Monitor',
    type: 'custom',
    schedule: '0 * * * *', // Every hour
    targets: ['all'],
    script: `
      # Check disk usage
      USAGE=$(df -h / | tail -1 | awk '{print $5}' | sed 's/%//')
      if [ $USAGE -gt 80 ]; then
        # Trigger voice alert
        echo "ALERT: Disk usage at $USAGE%"
      fi
    `,
    enabled: true
  });
}

// Example 7: Generate Executive Report
async function generateExecutiveReport() {
  console.log('Generating monthly executive report...');
  
  await it_assistant.generate_report({
    type: 'monthly',
    include_metrics: true,
    include_incidents: true,
    include_compliance: true,
    format: 'pdf',
    send_voice_summary: true,
    recipients: [
      '<EMAIL>',
      '<EMAIL>',
      'team:executives'
    ]
  });
}

// Example usage in Claude Code:
/*
To use these examples in Claude Code after installing the IT Assistant MCP:

1. Check system health:
   await check_system_health({ include_vms: true })

2. Send voice alert:
   await send_voice_alert({
     severity: "critical",
     message: "Database server is down",
     recipients: ["ops-team"]
   })

3. Create VM snapshot:
   await snapshot_vm({
     vmid: "100",
     action: "create",
     name: "before-update",
     description: "Snapshot before system update"
   })

4. Schedule backup:
   await schedule_task({
     name: "Weekly Backup",
     type: "backup",
     schedule: "0 3 * * 0",
     targets: ["vm-100", "vm-101"]
   })
*/