version: '3.8'

services:
  it-assistant:
    image: ${REGISTRY:-docker.io}/it-assistant-mcp:${VERSION:-latest}
    networks:
      - it-assistant-network
    environment:
      # Proxmox Configuration
      PROXMOX_HOST: ${PROXMOX_HOST:-*************}
      PROXMOX_PORT: ${PROXMOX_PORT:-8006}
      PROXMOX_TOKEN_FILE: /run/secrets/proxmox_token
      
      # ElevenLabs Configuration
      ELEVENLABS_API_KEY_FILE: /run/secrets/elevenlabs_key
      ELEVENLABS_VOICE_ID: ${ELEVENLABS_VOICE_ID:-cgSgspJ2msm6clMCkdW9}
      
      # Monitoring Configuration
      MONITOR_INTERVAL: ${MONITOR_INTERVAL:-60}
      ALERT_THRESHOLD_CPU: ${ALERT_THRESHOLD_CPU:-80}
      ALERT_THRESHOLD_MEMORY: ${ALERT_THRESHOLD_MEMORY:-90}
      ALERT_THRESHOLD_DISK: ${ALERT_THRESHOLD_DISK:-85}
      
      # System Configuration
      NODE_ENV: production
      LOG_LEVEL: ${LOG_LEVEL:-info}
      TZ: ${TZ:-America/New_York}
    
    secrets:
      - proxmox_token
      - elevenlabs_key
      - ssh_key
    
    volumes:
      - reports:/app/reports
      - playbooks:/app/playbooks
      - templates:/app/templates
    
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: unless-stopped
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 30s
        max_failure_ratio: 0.1
      rollback_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 30s
        max_failure_ratio: 0.1
    
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('healthy')"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 40s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=it-assistant"

secrets:
  proxmox_token:
    external: true
  elevenlabs_key:
    external: true
  ssh_key:
    external: true

volumes:
  reports:
    driver: local
    driver_opts:
      type: nfs
      o: addr=${NFS_SERVER:-nfs.local},nolock,soft,rw
      device: ":/exports/it-assistant/reports"
  playbooks:
    driver: local
  templates:
    driver: local

networks:
  it-assistant-network:
    driver: overlay
    attachable: true
    driver_opts:
      encrypted: "true"