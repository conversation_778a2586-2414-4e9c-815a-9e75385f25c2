apiVersion: apps/v1
kind: Deployment
metadata:
  name: it-assistant-mcp
  namespace: it-operations
  labels:
    app: it-assistant
    component: mcp-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: it-assistant
      component: mcp-server
  template:
    metadata:
      labels:
        app: it-assistant
        component: mcp-server
    spec:
      serviceAccountName: it-assistant
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: it-assistant
        image: your-registry/it-assistant-mcp:latest
        imagePullPolicy: Always
        env:
        - name: NODE_ENV
          value: "production"
        - name: PROXMOX_HOST
          valueFrom:
            configMapKeyRef:
              name: it-assistant-config
              key: proxmox.host
        - name: PROXMOX_PORT
          valueFrom:
            configMapKeyRef:
              name: it-assistant-config
              key: proxmox.port
        - name: PROXMOX_TOKEN
          valueFrom:
            secretKeyRef:
              name: it-assistant-secrets
              key: proxmox-token
        - name: ELEVENLABS_API_KEY
          valueFrom:
            secretKeyRef:
              name: it-assistant-secrets
              key: elevenlabs-api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "1000m"
        volumeMounts:
        - name: reports
          mountPath: /app/reports
        - name: playbooks
          mountPath: /app/playbooks
        - name: ssh-keys
          mountPath: /home/<USER>/.ssh
          readOnly: true
        livenessProbe:
          exec:
            command:
            - node
            - -e
            - "console.log('alive')"
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - node
            - -e
            - "console.log('ready')"
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: reports
        persistentVolumeClaim:
          claimName: it-assistant-reports
      - name: playbooks
        configMap:
          name: it-assistant-playbooks
      - name: ssh-keys
        secret:
          secretName: it-assistant-ssh-keys
          defaultMode: 0400
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: it-assistant-config
  namespace: it-operations
data:
  proxmox.host: "*************"
  proxmox.port: "8006"
  monitor.interval: "60"
  alert.threshold.cpu: "80"
  alert.threshold.memory: "90"
  alert.threshold.disk: "85"
---
apiVersion: v1
kind: Secret
metadata:
  name: it-assistant-secrets
  namespace: it-operations
type: Opaque
stringData:
  proxmox-token: "your-proxmox-token-here"
  elevenlabs-api-key: "your-elevenlabs-key-here"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: it-assistant-reports
  namespace: it-operations
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard