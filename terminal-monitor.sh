#!/bin/bash

# Terminal Monitor - Launch and monitor external terminal sessions

# Function to launch a command in a new terminal and capture output
launch_and_monitor() {
    local cmd="$1"
    local desc="$2"
    local output_file="/tmp/terminal-monitor-$$-$(date +%s).txt"
    
    # Create AppleScript to launch terminal and redirect output
    osascript << EOF
tell application "Terminal"
    set newTab to do script "echo '🚀 $desc' && echo '============================' && $cmd 2>&1 | tee $output_file && echo 'COMPLETED' >> $output_file"
    set window_id to id of window 1
    return window_id
end tell
EOF
    
    echo "$output_file"
}

# Function to check if command completed
check_completion() {
    local output_file="$1"
    if [[ -f "$output_file" ]] && grep -q "COMPLETED" "$output_file"; then
        return 0
    else
        return 1
    fi
}

# Function to get output from terminal
get_output() {
    local output_file="$1"
    if [[ -f "$output_file" ]]; then
        cat "$output_file" | sed '/COMPLETED/d'
    fi
}

# Example usage
case "$1" in
    "test")
        echo "Launching test command..."
        output_file=$(launch_and_monitor "ls -la && sleep 3 && echo 'Test complete'" "Test Command")
        echo "Monitoring output at: $output_file"
        
        # Monitor for completion
        while ! check_completion "$output_file"; do
            echo -n "."
            sleep 1
        done
        echo ""
        echo "Command completed! Output:"
        get_output "$output_file"
        ;;
        
    "service-check")
        echo "Checking all services in external terminal..."
        output_file=$(launch_and_monitor "
            echo 'Checking Proxmox Services...'
            for port in 8006 3000 8000 8007 8123; do
                echo -n \"Port \$port: \"
                nc -zv ************* \$port 2>&1 | grep -o 'succeeded\\|refused\\|timed' || echo 'no response'
            done
            echo ''
            echo 'Checking Container Services...'
            for ip in 17 19 22 23; do
                echo \"Checking 192.168.1.\$ip...\"
                ping -c 1 192.168.1.\$ip > /dev/null 2>&1 && echo \"  ✓ Host is up\" || echo \"  ✗ Host is down\"
            done
        " "Service Status Check")
        
        echo "Output file: $output_file"
        echo "Waiting for completion..."
        
        while ! check_completion "$output_file"; do
            sleep 0.5
        done
        
        echo "Results:"
        get_output "$output_file"
        ;;
        
    "monitor-logs")
        echo "Starting log monitor in external terminal..."
        launch_and_monitor "ssh root@************* 'tail -f /var/log/pveproxy/access.log'" "Proxmox Access Log Monitor"
        echo "Log monitor started in new terminal window"
        ;;
        
    *)
        echo "Usage: $0 {test|service-check|monitor-logs}"
        echo ""
        echo "Commands:"
        echo "  test         - Run a simple test"
        echo "  service-check - Check all Proxmox services"
        echo "  monitor-logs  - Start log monitoring in new terminal"
        ;;
esac