#!/bin/bash

# Convert existing MCP servers to include Inspector support
# This script can upgrade both local and Docker-based MCP servers

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default Inspector port
DEFAULT_INSPECTOR_PORT=8001

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║         MCP Server to Inspector Converter v1.0               ║${NC}"
echo -e "${BLUE}║     Adds official MCP Inspector to existing servers          ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] <server-path-or-name>"
    echo
    echo "Options:"
    echo "  -t, --type <type>       Server type: local, docker, or auto (default: auto)"
    echo "  -p, --port <port>       Inspector port (default: 8001)"
    echo "  -n, --name <name>       Server name for Docker conversion"
    echo "  -o, --output <dir>      Output directory for converted server"
    echo "  -h, --help              Show this help message"
    echo
    echo "Examples:"
    echo "  $0 /path/to/mcp-server                    # Convert local server"
    echo "  $0 -t docker -n my-mcp my-mcp-image       # Convert Docker image"
    echo "  $0 -p 8002 ./my-server                    # Use custom Inspector port"
}

# Parse command line arguments
SERVER_TYPE="auto"
INSPECTOR_PORT=$DEFAULT_INSPECTOR_PORT
SERVER_NAME=""
OUTPUT_DIR=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            SERVER_TYPE="$2"
            shift 2
            ;;
        -p|--port)
            INSPECTOR_PORT="$2"
            shift 2
            ;;
        -n|--name)
            SERVER_NAME="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            SERVER_PATH="$1"
            shift
            ;;
    esac
done

# Validate input
if [ -z "$SERVER_PATH" ]; then
    echo -e "${RED}Error: Server path or name required${NC}"
    show_usage
    exit 1
fi

# Auto-detect server type if needed
detect_server_type() {
    if [ -f "$SERVER_PATH/package.json" ]; then
        echo "local"
    elif [ -f "$SERVER_PATH/Dockerfile" ]; then
        echo "docker"
    elif docker inspect "$SERVER_PATH" &>/dev/null; then
        echo "docker"
    else
        echo "unknown"
    fi
}

if [ "$SERVER_TYPE" = "auto" ]; then
    SERVER_TYPE=$(detect_server_type)
    if [ "$SERVER_TYPE" = "unknown" ]; then
        echo -e "${RED}Error: Could not detect server type${NC}"
        echo "Please specify with -t option: local or docker"
        exit 1
    fi
    echo -e "${GREEN}Detected server type: $SERVER_TYPE${NC}"
fi

# Convert local MCP server
convert_local_server() {
    local src_path="$1"
    local out_path="$2"
    
    echo -e "${YELLOW}Converting local MCP server...${NC}"
    
    # Validate source
    if [ ! -f "$src_path/package.json" ]; then
        echo -e "${RED}Error: No package.json found in $src_path${NC}"
        exit 1
    fi
    
    # Set output directory
    if [ -z "$out_path" ]; then
        out_path="${src_path}-inspector"
    fi
    
    # Create output directory
    mkdir -p "$out_path"
    
    # Copy server files
    echo "Copying server files..."
    cp -r "$src_path"/* "$out_path/"
    
    # Update package.json to include Inspector
    echo "Updating package.json..."
    cd "$out_path"
    
    # Add Inspector dependency
    if command -v jq &>/dev/null; then
        jq '.dependencies["@modelcontextprotocol/inspector"] = "latest"' package.json > package.json.tmp
        mv package.json.tmp package.json
    else
        # Fallback if jq not available
        npm install --save @modelcontextprotocol/inspector
    fi
    
    # Create Inspector wrapper script
    echo "Creating Inspector wrapper..."
    cat > start-with-inspector.sh << 'EOF'
#!/bin/bash

# MCP Server with Inspector wrapper
INSPECTOR_PORT="${INSPECTOR_PORT:-8001}"
INSPECTOR_ENABLED="${INSPECTOR_ENABLED:-true}"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Get the main server command
get_server_command() {
    if [ -f "package.json" ]; then
        # Try to extract start script from package.json
        local start_cmd=$(node -e "console.log(require('./package.json').scripts?.start || '')")
        if [ -n "$start_cmd" ]; then
            echo "$start_cmd"
        elif [ -f "dist/index.js" ]; then
            echo "node dist/index.js"
        elif [ -f "index.js" ]; then
            echo "node index.js"
        else
            echo "npm start"
        fi
    else
        echo "node index.js"
    fi
}

SERVER_CMD=$(get_server_command)

if [ "$INSPECTOR_ENABLED" = "true" ]; then
    echo -e "${GREEN}Starting MCP Server with Inspector on port $INSPECTOR_PORT${NC}"
    echo -e "${YELLOW}Server command: $SERVER_CMD${NC}"
    echo -e "${YELLOW}Inspector URL: http://localhost:$INSPECTOR_PORT${NC}"
    
    # Start Inspector with the server
    npx @modelcontextprotocol/inspector \
        --port "$INSPECTOR_PORT" \
        --server "$SERVER_CMD"
else
    echo -e "${YELLOW}Starting MCP Server without Inspector${NC}"
    eval "$SERVER_CMD"
fi
EOF
    
    chmod +x start-with-inspector.sh
    
    # Create README for Inspector usage
    cat > INSPECTOR.md << EOF
# MCP Inspector Integration

This MCP server has been enhanced with the official MCP Inspector for debugging and testing.

## Starting the Server with Inspector

\`\`\`bash
# Default (Inspector on port $INSPECTOR_PORT)
./start-with-inspector.sh

# Custom Inspector port
INSPECTOR_PORT=8002 ./start-with-inspector.sh

# Disable Inspector
INSPECTOR_ENABLED=false ./start-with-inspector.sh
\`\`\`

## Accessing Inspector

Once the server is running, access the Inspector at:
http://localhost:$INSPECTOR_PORT

## Features

- Browse all available tools, resources, and prompts
- Execute tools and see real-time responses
- Monitor MCP protocol messages
- Debug server implementations
- Test server capabilities

## Environment Variables

- \`INSPECTOR_PORT\`: Port for Inspector UI (default: $INSPECTOR_PORT)
- \`INSPECTOR_ENABLED\`: Enable/disable Inspector (default: true)
EOF
    
    echo -e "${GREEN}✅ Local server converted successfully!${NC}"
    echo -e "Output: $out_path"
    echo -e "To start: cd $out_path && ./start-with-inspector.sh"
}

# Convert Docker MCP server
convert_docker_server() {
    local image_name="$1"
    local server_name="$2"
    local out_path="$3"
    
    echo -e "${YELLOW}Converting Docker MCP server...${NC}"
    
    # Set defaults
    if [ -z "$server_name" ]; then
        server_name="${image_name%:*}-inspector"
    fi
    
    if [ -z "$out_path" ]; then
        out_path="./${server_name}"
    fi
    
    # Create output directory
    mkdir -p "$out_path"
    cd "$out_path"
    
    # Create Dockerfile with Inspector
    echo "Creating Dockerfile with Inspector..."
    cat > Dockerfile << EOF
# MCP Server with Inspector
FROM $image_name AS base

# Install Inspector globally
RUN npm install -g @modelcontextprotocol/inspector

# Create Inspector wrapper
RUN cat > /app/start-with-inspector.sh << 'SCRIPT'
#!/bin/sh

# Start Inspector if enabled
if [ "\$INSPECTOR_ENABLED" = "true" ]; then
    echo "Starting MCP Inspector on port \${INSPECTOR_PORT:-8001}..."
    
    # Find the server command
    if [ -f "/app/package.json" ]; then
        SERVER_CMD=\$(node -e "console.log(require('/app/package.json').scripts?.start || 'node index.js')")
    else
        SERVER_CMD="node /app/index.js"
    fi
    
    # Start Inspector with server
    npx @modelcontextprotocol/inspector \\
        --port \${INSPECTOR_PORT:-8001} \\
        --server "\$SERVER_CMD" &
    INSPECTOR_PID=\$!
fi

# Start the original entrypoint
exec \$@
SCRIPT

RUN chmod +x /app/start-with-inspector.sh

# Expose Inspector port
EXPOSE 8000 ${INSPECTOR_PORT}

# Override entrypoint to use wrapper
ENTRYPOINT ["/app/start-with-inspector.sh"]

# Use original CMD
CMD ["node", "/app/index.js"]
EOF
    
    # Create docker-compose.yml
    echo "Creating docker-compose.yml..."
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  $server_name:
    build: .
    container_name: $server_name
    restart: unless-stopped
    ports:
      - "8000:8000"  # MCP Server port
      - "$INSPECTOR_PORT:$INSPECTOR_PORT"  # Inspector port
    environment:
      - INSPECTOR_ENABLED=true
      - INSPECTOR_PORT=$INSPECTOR_PORT
    volumes:
      - ./config:/app/config
      - ./data:/app/data
EOF
    
    # Create build script
    cat > build.sh << 'EOF'
#!/bin/bash
docker-compose build
echo "Build complete! Run 'docker-compose up' to start."
EOF
    chmod +x build.sh
    
    # Create run script
    cat > run.sh << 'EOF'
#!/bin/bash
docker-compose up -d
echo "Server started!"
echo "Inspector available at: http://localhost:${INSPECTOR_PORT:-8001}"
EOF
    chmod +x run.sh
    
    # Create README
    cat > README.md << EOF
# $server_name - MCP Server with Inspector

This Docker-based MCP server includes the official MCP Inspector for debugging.

## Quick Start

\`\`\`bash
# Build the image
./build.sh

# Run the server
./run.sh

# Or manually
docker-compose up -d
\`\`\`

## Accessing Services

- MCP Server: http://localhost:8000
- Inspector UI: http://localhost:$INSPECTOR_PORT

## Configuration

Edit \`docker-compose.yml\` to modify:
- Port mappings
- Environment variables
- Volume mounts

## Disabling Inspector

Set \`INSPECTOR_ENABLED=false\` in docker-compose.yml
EOF
    
    echo -e "${GREEN}✅ Docker server converted successfully!${NC}"
    echo -e "Output: $out_path"
    echo -e "To build: cd $out_path && ./build.sh"
    echo -e "To run: ./run.sh"
}

# Create wrapper for existing servers
create_inspector_wrapper() {
    local server_cmd="$1"
    local output_file="$2"
    
    cat > "$output_file" << EOF
#!/bin/bash
# MCP Inspector wrapper for: $server_cmd

INSPECTOR_PORT="\${INSPECTOR_PORT:-$INSPECTOR_PORT}"
INSPECTOR_ENABLED="\${INSPECTOR_ENABLED:-true}"

if [ "\$INSPECTOR_ENABLED" = "true" ]; then
    echo "Starting with Inspector on port \$INSPECTOR_PORT"
    npx @modelcontextprotocol/inspector \\
        --port "\$INSPECTOR_PORT" \\
        --server "$server_cmd"
else
    $server_cmd
fi
EOF
    chmod +x "$output_file"
}

# Main conversion logic
case "$SERVER_TYPE" in
    local)
        convert_local_server "$SERVER_PATH" "$OUTPUT_DIR"
        ;;
    docker)
        convert_docker_server "$SERVER_PATH" "$SERVER_NAME" "$OUTPUT_DIR"
        ;;
    *)
        echo -e "${RED}Error: Unknown server type: $SERVER_TYPE${NC}"
        exit 1
        ;;
esac

echo
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                   Conversion Complete!                       ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo
echo -e "${GREEN}Inspector Port: $INSPECTOR_PORT${NC}"
echo -e "${GREEN}Inspector URL: http://localhost:$INSPECTOR_PORT${NC}"
echo
echo "Next steps:"
echo "1. Test the converted server with Inspector"
echo "2. Update your MCP client configuration if needed"
echo "3. Deploy to your MCP Hub when ready"