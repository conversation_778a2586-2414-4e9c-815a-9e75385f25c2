version: '3.8'

services:
  it-assistant:
    build: .
    image: it-assistant-mcp:latest
    container_name: it-assistant-mcp
    restart: unless-stopped
    environment:
      # Proxmox Configuration
      - PROXMOX_HOST=${PROXMOX_HOST:-*************}
      - PROXMOX_PORT=${PROXMOX_PORT:-8006}
      - PROXMOX_TOKEN=${PROXMOX_TOKEN}
      
      # ElevenLabs Configuration
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - ELEVENLABS_VOICE_ID=${ELEVENLABS_VOICE_ID:-cgSgspJ2msm6clMCkdW9}
      
      # Monitoring Configuration
      - MONITOR_INTERVAL=${MONITOR_INTERVAL:-60}
      - ALERT_THRESHOLD_CPU=${ALERT_THRESHOLD_CPU:-80}
      - ALERT_THRESHOLD_MEMORY=${ALERT_THRESHOLD_MEMORY:-90}
      - ALERT_THRESHOLD_DISK=${ALERT_THRESHOLD_DISK:-85}
      
      # System Configuration
      - NODE_ENV=production
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - TZ=${TZ:-America/New_York}
    
    volumes:
      # Persistent storage for reports and data
      - ./data/reports:/app/reports
      - ./data/playbooks:/app/playbooks
      - ./data/templates:/app/templates
      
      # SSH keys for remote access (read-only)
      - ~/.ssh:/home/<USER>/.ssh:ro
      
      # Custom playbooks and templates
      - ./custom/playbooks:/app/custom/playbooks:ro
      - ./custom/templates:/app/custom/templates:ro
    
    # For MCP stdio communication
    stdin_open: true
    tty: true
    
    # Security options
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_RAW  # For ping functionality
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    networks:
      - it-assistant-network

  # Optional: Add a web interface container
  it-assistant-web:
    image: nginx:alpine
    container_name: it-assistant-web
    ports:
      - "8080:80"
    volumes:
      - ./web:/usr/share/nginx/html:ro
    depends_on:
      - it-assistant
    networks:
      - it-assistant-network
    profiles:
      - web

networks:
  it-assistant-network:
    driver: bridge