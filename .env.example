# Proxmox Configuration
PROXMOX_HOST=*************
PROXMOX_PORT=8006
PROXMOX_TOKEN=root@pam!claude-mcp=your-api-token-here

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_VOICE_ID=cgSgspJ2msm6clMCkdW9
ELEVENLABS_CRITICAL_VOICE=cgSgspJ2msm6clMCkdW9
ELEVENLABS_WARNING_VOICE=cgSgspJ2msm6clMCkdW9
ELEVENLABS_INFO_VOICE=cgSgspJ2msm6clMCkdW9

# Monitoring Configuration
MONITOR_INTERVAL=60
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=90
ALERT_THRESHOLD_DISK=85

# Notification Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ALERT_EMAIL=<EMAIL>

# SSH Configuration (for remote execution)
SSH_KEY_PATH=/Users/<USER>/.ssh/id_rsa
SSH_KNOWN_HOSTS=/Users/<USER>/.ssh/known_hosts

# Report Settings
REPORT_OUTPUT_DIR=/Users/<USER>/it-assistant-reports
REPORT_RETENTION_DAYS=90

# System Settings
LOG_LEVEL=info
TIMEZONE=America/New_York
MAX_PARALLEL_TASKS=5