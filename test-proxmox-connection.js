#!/usr/bin/env node
import https from 'https';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const PROXMOX_HOST = process.env.PROXMOX_HOST || '*************';
const PROXMOX_PORT = process.env.PROXMOX_PORT || '8006';
const PROXMOX_TOKEN = process.env.PROXMOX_TOKEN || '';

console.log('Testing Proxmox Connection...');
console.log(`Host: ${PROXMOX_HOST}:${PROXMOX_PORT}`);
console.log(`Token: ${PROXMOX_TOKEN.substring(0, 20)}...`);
console.log('');

// Create HTTPS agent to handle self-signed certificates
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

async function testConnection() {
  try {
    // Test 1: Get version
    console.log('Test 1: Getting Proxmox version...');
    const versionUrl = `https://${PROXMOX_HOST}:${PROXMOX_PORT}/api2/json/version`;
    const versionResponse = await fetch(versionUrl, {
      headers: {
        'Authorization': `PVEAPIToken=${PROXMOX_TOKEN}`
      },
      agent: httpsAgent
    });
    
    if (versionResponse.ok) {
      const version = await versionResponse.json();
      console.log('✓ Connected successfully!');
      console.log('  Version:', version.data.version);
      console.log('  Release:', version.data.release);
    } else {
      console.log('✗ Failed to get version:', versionResponse.status, versionResponse.statusText);
    }
    
    // Test 2: Get nodes
    console.log('\nTest 2: Getting nodes...');
    const nodesUrl = `https://${PROXMOX_HOST}:${PROXMOX_PORT}/api2/json/nodes`;
    const nodesResponse = await fetch(nodesUrl, {
      headers: {
        'Authorization': `PVEAPIToken=${PROXMOX_TOKEN}`,
        'Content-Type': 'application/json'
      },
      agent: httpsAgent
    });
    
    if (nodesResponse.ok) {
      const nodes = await nodesResponse.json();
      console.log('✓ Retrieved nodes successfully!');
      nodes.data.forEach(node => {
        console.log(`  - ${node.node}: ${node.status} (CPU: ${Math.round(node.cpu * 100)}%, Memory: ${Math.round(node.mem / node.maxmem * 100)}%)`);
      });
    } else {
      console.log('✗ Failed to get nodes:', nodesResponse.status, nodesResponse.statusText);
      const errorText = await nodesResponse.text();
      console.log('  Error:', errorText);
    }
    
    // Test 3: Get VMs
    console.log('\nTest 3: Getting VMs...');
    const vmsUrl = `https://${PROXMOX_HOST}:${PROXMOX_PORT}/api2/json/cluster/resources?type=vm`;
    const vmsResponse = await fetch(vmsUrl, {
      headers: {
        'Authorization': `PVEAPIToken=${PROXMOX_TOKEN}`,
        'Content-Type': 'application/json'
      },
      agent: httpsAgent
    });
    
    if (vmsResponse.ok) {
      const vms = await vmsResponse.json();
      console.log('✓ Retrieved VMs successfully!');
      const vmList = vms.data.filter(vm => vm.type === 'qemu' || vm.type === 'lxc');
      vmList.forEach(vm => {
        console.log(`  - VM ${vm.vmid} (${vm.name}): ${vm.status} on ${vm.node}`);
      });
    } else {
      console.log('✗ Failed to get VMs:', vmsResponse.status, vmsResponse.statusText);
    }
    
  } catch (error) {
    console.error('Connection error:', error.message);
    if (error.cause) {
      console.error('Cause:', error.cause);
    }
  }
}

// Run the test
testConnection();