# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

IT Assistant MCP is a Model Context Protocol server for comprehensive IT infrastructure management. It provides monitoring, VM management, automation, incident response, and voice alerts through a containerized Node.js application designed for Apple's ecosystem and Proxmox VE integration.

## Development Commands

### Core Commands
```bash
# Start development server with file watching
npm run dev

# Start production server
npm start

# Run tests
npm test

# Test with MCP inspector
npm run inspector
```

### Container Commands
```bash
# Build container (Apple Silicon/macOS)
./container/build.sh

# Run container 
./container/run.sh

# Build Docker image (cross-platform)
./scripts/build.sh

# Docker Compose
docker-compose up -d
```

### Setup and Installation
```bash
# Interactive setup tool
./setup-it-assistant

# Quick start script
./quickstart.sh

# Deploy with specific configurations
./deploy-checkmate.sh
./deploy-mcp-hub.sh
```

## Architecture

### Core Components

**Main Server** (`src/index.js`)
- MCP server implementation using `@modelcontextprotocol/sdk`
- Tool and resource handlers for IT operations
- Integration with specialized manager classes

**Manager Classes** (`src/lib/`)
- `monitoring.js` - System health monitoring and metrics
- `vm-manager.js` - Proxmox VM lifecycle management  
- `incidents.js` - Incident detection and response
- `reporting.js` - Report generation and templates
- `automation.js` - Task scheduling and playbooks
- `voice-alerts.js` - ElevenLabs text-to-speech integration

### Tool Categories

**System Monitoring**
- `check_system_health` - Comprehensive health status
- `monitor_services` - Service status checking
- `get_resource_usage` - CPU/memory/disk metrics
- `analyze_logs` - Log search and analysis

**VM Management** 
- `list_all_vms` - List VMs across nodes
- `create_vm` - VM creation from templates
- `manage_vm` - Start/stop/restart operations
- `snapshot_vm` - Snapshot management

**Automation**
- `schedule_task` - Automated task scheduling
- `run_playbook` - Ansible-style playbook execution
- `batch_execute` - Multi-system command execution

**Incident Response**
- `diagnose_issue` - Automated troubleshooting
- `apply_fix` - Fix application
- `escalate_incident` - Human escalation

**Reporting & Alerts**
- `generate_report` - Infrastructure reports
- `send_voice_alert` - Critical voice notifications
- `status_report` - Voice status summaries

### Resource System

The server provides extensive documentation resources:
- IT Assistant documentation
- Automation playbooks
- Report templates
- Proxmox community scripts
- Deployment prompts
- ALIAS infrastructure ontology
- Emergency procedures
- Terraform configurations

## Configuration

### Environment Variables
```bash
# Proxmox Integration
PROXMOX_HOST=*************
PROXMOX_PORT=8006
PROXMOX_TOKEN=<api-token>

# Voice Alerts
ELEVENLABS_API_KEY=<api-key>
ELEVENLABS_VOICE_ID=<voice-id>

# Monitoring Thresholds
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=90
ALERT_THRESHOLD_DISK=85
```

### Database
SQLite database at `~/.it-assistant-mcp/data/systems.db` containing:
- Systems inventory
- Performance metrics
- Alert history

## Container Deployment

### Apple Container Framework
Optimized for macOS 15+ with Apple Silicon:
- Secure containerization
- Read-only filesystem
- Minimal capabilities (NET_RAW for ping)
- SSH key mounting for remote access

### Docker/Podman
Cross-platform deployment with resource limits and security options.

## Integration Points

### MCP Configuration
Add to Ace configuration:
```yaml
mcpServers:
  it-assistant:
    type: stdio
    command: container
    args:
      - exec
      - -i
      - it-assistant-mcp
      - node
      - /app/src/index.js
```

### Proxmox Integration
- VM management through PVE API
- Community scripts for rapid deployment
- Automated backup and snapshot management

### Voice Alerts
ElevenLabs integration for critical notifications and status reports.

## Development Patterns

### Error Handling
All tools return structured responses with error handling:
```javascript
return {
  content: [{ type: 'text', text: result }],
  isError: false
};
```

### Monitoring Integration
Built-in monitoring with configurable thresholds and automated health checks.

### Security
- No hardcoded credentials
- Containerized execution
- Read-only configurations
- Capability restrictions

## Testing

Run comprehensive tests covering:
- Tool functionality
- Resource access
- Error handling
- Integration points

Use `npm test` or test individual components with the MCP inspector via `npm run inspector`.