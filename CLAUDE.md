# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an IT Assistant MCP (Model Context Protocol) server that provides comprehensive IT infrastructure management and monitoring capabilities. The server integrates with Proxmox virtualization, ElevenLabs voice alerts, and various monitoring systems to provide AI-powered IT operations.

**Key Technologies:**
- Node.js 18+ with ES modules
- Model Context Protocol (MCP) SDK
- Zod for schema validation
- SQLite for data persistence
- Container-based deployment (Apple container framework + Docker)

## Development Commands

### Local Development
```bash
# Install dependencies
npm install

# Run server locally with file watching
npm run dev

# Run server in production mode
npm start

# Test MCP functionality
npm test

# Debug with MCP inspector
npm run inspector
```

### Container Development
```bash
# Build container image
./container/build.sh

# Run container (creates config if needed)
./container/run.sh

# View container logs
container logs -f it-assistant-mcp

# Access container shell
container exec -it it-assistant-mcp /bin/sh

# Stop container
container stop it-assistant-mcp
```

### MCP Scaffold CLI (TypeScript subproject)
```bash
cd mcp-scaffold-cli

# Build TypeScript
npm run build

# Run in development mode
npm run dev

# Run tests
npm test
```

### Docker Compose (Alternative deployment)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Architecture Overview

### MCP Server Structure
The main server (`src/index.js`) implements the Model Context Protocol and exposes tools for:
- **System Monitoring**: Real-time metrics collection and status checking
- **VM Management**: Proxmox integration for virtual machine operations
- **Incident Management**: Tracking and resolving system incidents
- **Automation**: Scheduled task execution with cron
- **Voice Alerts**: ElevenLabs TTS integration for critical notifications
- **Reporting**: Generate system reports and documentation

### Core Modules (`src/lib/`)
- `monitoring.js` - SystemMonitor class for metrics and health checks
- `vm-manager.js` - VMManager for Proxmox virtualization control
- `incidents.js` - IncidentManager for issue tracking
- `reporting.js` - ReportGenerator for documentation and reports
- `automation.js` - TaskScheduler for automated operations
- `voice-alerts.js` - VoiceAlerts for ElevenLabs TTS integration

### Data Storage
- SQLite database for persistent data (systems, metrics, incidents)
- File-based storage for reports and templates
- SSH key management for remote system access

## Configuration

### Environment Variables
Key configuration in `.env` or container environment:
```bash
# Proxmox Integration
PROXMOX_HOST=*************
PROXMOX_PORT=8006
PROXMOX_TOKEN=your-api-token

# ElevenLabs Voice Alerts
ELEVENLABS_API_KEY=your-api-key
ELEVENLABS_VOICE_ID=voice-id

# Monitoring Thresholds
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=90
ALERT_THRESHOLD_DISK=85
```

### Container Configuration
- Data persistence: `~/.it-assistant-mcp/data`
- SSH keys: `~/.ssh` (mounted read-only)
- Config file: `~/.it-assistant-mcp/config.env`

## MCP Integration Patterns

### Tool Implementation
All MCP tools follow this pattern in `src/index.js`:
1. Define tool schema with Zod validation
2. Implement handler function
3. Register with `this.server.setRequestHandler('tools/call', ...)`

### Resource Access
Resources provide access to:
- System documentation and ontology
- Infrastructure automation scripts
- Configuration templates

## Testing Approach

### MCP Protocol Testing
The test suite (`src/test.js`) validates:
- JSON-RPC 2.0 protocol compliance
- Tool registration and calling
- Resource access patterns
- Error handling

### Manual Testing Commands
```bash
# Test basic MCP functionality
echo '{"jsonrpc":"2.0","method":"tools/list","id":1}' | node src/index.js

# Test specific tool
echo '{"jsonrpc":"2.0","method":"tools/call","params":{"name":"list_systems"},"id":2}' | node src/index.js
```

## Security Considerations

### Container Security
- Non-root user execution (`nodejs` user)
- Read-only root filesystem
- Minimal capabilities (only NET_RAW for ping)
- No new privileges flag
- SSH keys mounted read-only

### API Security
- Proxmox token-based authentication
- ElevenLabs API key protection
- SSH key-based remote access
- Environment variable isolation

## Infrastructure Integration

### Proxmox Integration
- VM lifecycle management (create, start, stop, delete)
- Snapshot operations
- Resource monitoring
- Template deployment

### Voice Alert System
- ElevenLabs TTS for critical alerts
- Configurable voice models
- Threshold-based triggering

### Remote System Management
- SSH-based command execution
- Multi-system health monitoring
- Automated task scheduling

## Common Development Tasks

When adding new MCP tools:
1. Define Zod schema for input validation
2. Implement business logic in appropriate `lib/` module
3. Add tool definition to `setupHandlers()` in `src/index.js`
4. Update database schema if needed (`data/schema.sql`)
5. Add tests to `src/test.js`

When modifying container deployment:
1. Update `Dockerfile`/`Containerfile` for build changes
2. Modify `container/build.sh` and `container/run.sh` scripts
3. Update `docker-compose.yml` for service configuration
4. Test with both Apple container framework and Docker

## Integration with Claude Code

### MCP Server Registration
Add to your MCP client configuration:
```json
{
  "mcpServers": {
    "it-assistant": {
      "type": "stdio",
      "command": "container",
      "args": ["exec", "-i", "it-assistant-mcp", "node", "/app/src/index.js"]
    }
  }
}
```

### Available MCP Tools
The server exposes comprehensive IT management tools including system monitoring, VM management, incident tracking, automation scheduling, and voice alerting capabilities. Use `tools/list` to see all available tools with their schemas.