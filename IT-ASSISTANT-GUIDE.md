# IT Assistant MCP Server - Complete Guide

## Overview

The IT Assistant MCP Server is a comprehensive infrastructure management tool that provides AI-powered IT operations, monitoring, and automation capabilities. It integrates with Proxmox, Kubernetes, and includes the complete ALIAS Infrastructure Ontology.

## Key Features

### 🚀 Infrastructure Management
- **VM Lifecycle Management**: Create, manage, snapshot VMs through Proxmox
- **Container Orchestration**: Deploy and manage Kubernetes clusters
- **Infrastructure as Code**: Full Terraform modules for enterprise deployment
- **Automated Deployments**: One-command infrastructure provisioning

### 📚 Knowledge Base & Documentation
- **ALIAS Infrastructure Ontology**: Complete configuration and command reference
- **Emergency Procedures**: Step-by-step disaster recovery guides
- **Script Library**: Ready-to-use automation scripts
- **Command Reference**: Quick lookup for all infrastructure commands

### 🤖 AI-Powered Assistance
- **Natural Language Operations**: Execute complex tasks with simple prompts
- **Intelligent Troubleshooting**: Automated issue diagnosis and remediation
- **Voice Alerts**: Critical notifications via ElevenLabs integration
- **Smart Reporting**: AI-generated infrastructure reports

### 📊 Monitoring & Observability
- **Real-time Metrics**: CPU, memory, disk, network monitoring
- **Service Health Checks**: Automated status monitoring
- **Log Analysis**: Intelligent log parsing and alerting
- **Performance Analytics**: Historical data and trend analysis

### 🔐 Security & Compliance
- **Security Auditing**: Automated vulnerability scanning
- **Access Management**: SSH key and credential handling
- **Compliance Reporting**: Generate audit-ready reports
- **Incident Response**: Automated security incident handling

## Available Resources

### Infrastructure Ontology
Access the complete ALIAS infrastructure knowledge base:

```
Resource: alias://infrastructure/ontology
Description: Complete infrastructure hierarchy, configurations, and procedures
```

### Command Reference
Quick access to all infrastructure commands:

```
Resource: alias://infrastructure/commands
Description: Proxmox, Kubernetes, Docker, and service management commands
```

### Scripts Library
Pre-built automation scripts:

```
Resource: alias://infrastructure/scripts
Description: Network setup, health checks, backup automation scripts
```

### Emergency Procedures
Critical recovery procedures:

```
Resource: alias://infrastructure/emergency
Description: System recovery, network emergencies, disaster recovery
```

### Terraform Configuration
Infrastructure as Code modules:

```
Resource: alias://infrastructure/terraform
Description: Complete IaC for deploying enterprise infrastructure
```

### Management Prompts
AI-optimized prompts for common tasks:

```
Resource: alias://prompts/infrastructure
Description: Ready-to-use prompts for infrastructure management
```

## Example Usage Prompts

### System Health Check
```
"Check the health of all Proxmox nodes and VMs, including CPU, memory, and disk usage. Alert me to any issues above 80% utilization."
```

### Deploy Monitoring Stack
```
"Deploy a complete monitoring stack:
1. Create Prometheus container for metrics
2. Create Grafana container for visualization
3. Configure data sources and dashboards
4. Set up alerts for critical metrics
5. Test the entire monitoring pipeline"
```

### Infrastructure Deployment
```
"Using the Terraform modules, deploy:
1. Network segmentation with VLANs
2. Production Kubernetes cluster
3. Core services (GitLab, Harbor, Vault)
4. Monitoring stack
Show me the progress and any issues."
```

### Security Audit
```
"Run a security audit on the infrastructure. Check for:
- Failed login attempts in the last 24 hours
- Open ports that shouldn't be exposed
- VMs without recent security updates
- Expired SSL certificates"
```

### Disaster Recovery
```
"Initiate a disaster recovery test:
1. Create snapshots of critical VMs
2. Simulate a failure scenario
3. Test restore procedures
4. Verify service functionality
5. Document the results"
```

## Infrastructure Components

### Current Infrastructure (*************)
- **Hardware**: Intel i7-14700F, 128GB RAM, 62GB storage (expandable)
- **Existing VMs**: 
  - VM 102: Home Assistant
  - VM 103: Windows 11
  - LXC 100: Proxmox Backup Server
  - LXC 101: iVentoy

### Planned Infrastructure (via Terraform)
- **Kubernetes Clusters**: Production (HA), Staging, Development
- **Core Services**: GitLab CI/CD, Harbor Registry, Vault, Keycloak SSO
- **Monitoring**: Prometheus, Grafana, ELK Stack
- **Network**: 8 VLANs for proper segmentation

## Quick Commands

### Check Infrastructure Status
```
Tool: check_system_health
Parameters: {
  "include_services": true,
  "include_resources": true,
  "include_vms": true
}
```

### Deploy VM from Template
```
Tool: create_vm
Parameters: {
  "name": "web-server-01",
  "template": "ubuntu-2204-cloudinit",
  "cores": 4,
  "memory": 8192,
  "disk": 100
}
```

### Generate Infrastructure Report
```
Tool: generate_report
Parameters: {
  "type": "weekly",
  "include_metrics": true,
  "include_incidents": true,
  "format": "markdown",
  "send_voice_summary": true
}
```

## Integration with Other Services

### Proxmox Integration
- Direct API access to Proxmox at *************
- Full VM lifecycle management
- Snapshot and backup automation
- Resource monitoring and alerting

### Kubernetes Integration
- Multi-cluster management (prod/staging/dev)
- Automated deployments
- Service mesh configuration
- Pod and node monitoring

### Voice Integration (ElevenLabs)
- Critical alert notifications
- Status report summaries  
- Customizable voice selection (Lily - British)
- Automatic playback in Music app

## Best Practices

1. **Use Resources First**: Check the ontology resources before executing commands
2. **Plan Before Deploying**: Use the Terraform modules for consistent deployments
3. **Monitor Continuously**: Set up alerts for critical thresholds
4. **Document Changes**: Update the changelog after infrastructure modifications
5. **Test Recovery**: Regularly test backup and recovery procedures

## Troubleshooting

### Resource Not Found
If you get "Unknown resource" errors, ensure the IT Assistant MCP server is running with the latest version that includes the ontology resources.

### SSH Connection Issues
- Verify SSH keys are properly configured
- Check network connectivity to target systems
- Ensure firewall rules allow SSH access

### Terraform Deployment Errors
- Verify Proxmox API token is configured
- Check available resources before deployment
- Review Terraform plan before applying

## Next Steps

1. **Immediate Actions**:
   - Add storage to Proxmox (critical - 83% full)
   - Create Ubuntu cloud-init template
   - Configure Terraform variables

2. **Infrastructure Deployment**:
   - Deploy network VLANs
   - Create Kubernetes clusters
   - Deploy core services
   - Set up monitoring

3. **Operational Setup**:
   - Configure backup schedules
   - Set up monitoring alerts
   - Document runbooks
   - Train team on new tools

---

*For complete documentation, access the Infrastructure Ontology resource: `alias://infrastructure/ontology`*