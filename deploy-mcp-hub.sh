#!/bin/bash

# MCP Hub Deployment Script for Proxmox

set -e

echo "🚀 MCP Hub Deployment Script"
echo "============================"

# Configuration
CONTAINER_ID=109
CONTAINER_HOSTNAME="mcp-hub"
CONTAINER_MEMORY=4096
CONTAINER_CORES=2
CONTAINER_DISK=16
HUB_PORT=3100
PROXY_PORT=3101

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check if running on Proxmox
check_proxmox() {
    if [ ! -f /etc/pve/version ]; then
        echo -e "${RED}Error: This script must be run on a Proxmox host${NC}"
        exit 1
    fi
}

# Function to create container
create_container() {
    echo -e "${YELLOW}Creating LXC container for MCP Hub...${NC}"
    
    # Check if container already exists
    if pct status $CONTAINER_ID &>/dev/null; then
        echo -e "${YELLOW}Container $CONTAINER_ID already exists${NC}"
        read -p "Do you want to recreate it? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pct stop $CONTAINER_ID || true
            sleep 2
            pct destroy $CONTAINER_ID
        else
            echo "Using existing container"
            return
        fi
    fi
    
    # Create container
    pct create $CONTAINER_ID /var/lib/vz/template/cache/debian-12-standard_12.7-1_amd64.tar.zst \
        --hostname $CONTAINER_HOSTNAME \
        --memory $CONTAINER_MEMORY \
        --cores $CONTAINER_CORES \
        --net0 name=eth0,bridge=vmbr0,ip=dhcp \
        --storage local \
        --rootfs local:$CONTAINER_DISK \
        --unprivileged 1 \
        --features nesting=1
    
    # Start container
    pct start $CONTAINER_ID
    sleep 10
    
    echo -e "${GREEN}Container created successfully${NC}"
}

# Function to install dependencies
install_dependencies() {
    echo -e "${YELLOW}Installing dependencies...${NC}"
    
    # Update system
    pct exec $CONTAINER_ID -- apt update
    pct exec $CONTAINER_ID -- apt upgrade -y
    
    # Install Docker
    pct exec $CONTAINER_ID -- bash -c "curl -fsSL https://get.docker.com | sh"
    
    # Install Docker Compose
    pct exec $CONTAINER_ID -- apt install -y docker-compose-plugin
    
    # Install Node.js
    pct exec $CONTAINER_ID -- bash -c "curl -fsSL https://deb.nodesource.com/setup_20.x | bash -"
    pct exec $CONTAINER_ID -- apt install -y nodejs
    
    # Install additional tools
    pct exec $CONTAINER_ID -- apt install -y git nginx certbot python3-certbot-nginx
    
    echo -e "${GREEN}Dependencies installed successfully${NC}"
}

# Function to create MCP Hub application
create_mcp_hub() {
    echo -e "${YELLOW}Creating MCP Hub application...${NC}"
    
    # Create directory structure
    pct exec $CONTAINER_ID -- mkdir -p /opt/mcp-hub/{src,public,data,config}
    
    # Create package.json
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/package.json << "EOF"
{
  "name": "mcp-hub",
  "version": "1.0.0",
  "description": "Centralized MCP Server Hub",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "tsx watch src/index.ts"
  },
  "dependencies": {
    "@modelcontextprotocol/sdk": "latest",
    "express": "^4.18.2",
    "ws": "^8.14.2",
    "dockerode": "^4.0.0",
    "pg": "^8.11.3",
    "redis": "^4.6.10",
    "jsonwebtoken": "^9.0.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0"
  },
  "devDependencies": {
    "@types/node": "^20.10.0",
    "@types/express": "^4.17.21",
    "@types/ws": "^8.5.10",
    "typescript": "^5.3.2",
    "tsx": "^4.6.2"
  }
}
EOF'
    
    # Create TypeScript config
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/tsconfig.json << "EOF"
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOF'
    
    # Create main server file
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/src/index.ts << "EOF"
import express from "express";
import { createServer } from "http";
import { WebSocketServer } from "ws";
import cors from "cors";
import helmet from "helmet";
import { MCPRegistry } from "./registry";
import { MCPProxy } from "./proxy";
import { setupRoutes } from "./routes";

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// Initialize services
const registry = new MCPRegistry();
const proxy = new MCPProxy(registry);

// Setup routes
setupRoutes(app, registry, proxy);

// WebSocket handling
wss.on("connection", (ws, req) => {
  console.log("New WebSocket connection");
  proxy.handleWebSocket(ws, req);
});

// Start server
const PORT = process.env.PORT || 3100;
server.listen(PORT, () => {
  console.log(`MCP Hub running on port ${PORT}`);
  console.log(`Web UI: http://localhost:${PORT}`);
  console.log(`API: http://localhost:${PORT}/api`);
});
EOF'
    
    # Create Docker Compose file
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/docker-compose.yml << "EOF"
version: "3.8"

services:
  mcp-hub:
    build: .
    container_name: mcp-hub-server
    restart: unless-stopped
    ports:
      - "3100:3100"
      - "3101:3101"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/mcphub
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=change-this-secret-key
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - mcp-network

  postgres:
    image: postgres:15
    container_name: mcp-hub-postgres
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mcp-network

  redis:
    image: redis:7-alpine
    container_name: mcp-hub-redis
    volumes:
      - redis-data:/data
    networks:
      - mcp-network

  # Your MCP servers can be added here
  # Example:
  # it-assistant:
  #   image: it-assistant-mcp:latest
  #   container_name: mcp-it-assistant
  #   networks:
  #     - mcp-network

volumes:
  postgres-data:
  redis-data:

networks:
  mcp-network:
    driver: bridge
EOF'
    
    # Create Dockerfile
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/Dockerfile << "EOF"
FROM node:20-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY tsconfig.json ./
COPY src ./src
RUN npm run build

FROM node:20-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY --from=builder /app/dist ./dist
COPY public ./public

EXPOSE 3100 3101

CMD ["node", "dist/index.js"]
EOF'
    
    echo -e "${GREEN}MCP Hub application created${NC}"
}

# Function to create web UI
create_web_ui() {
    echo -e "${YELLOW}Creating web UI...${NC}"
    
    # Create index.html
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/public/index.html << "EOF"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Hub - Server Catalog</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #0A0E27;
            color: #fff;
        }
        header {
            background: linear-gradient(135deg, #00D4FF, #9D4EDD);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        nav {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
        }
        nav a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: background 0.3s;
        }
        nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        main {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .server-card {
            background: #1E2139;
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: all 0.3s;
        }
        .server-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
        }
        .server-card h3 {
            color: #00D4FF;
            margin-bottom: 0.5rem;
        }
        .server-card .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            margin-top: 1rem;
        }
        .status.online {
            background: #00FF88;
            color: #000;
        }
        .status.offline {
            background: #FF3366;
            color: #fff;
        }
        .search-bar {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        .search-bar input {
            flex: 1;
            padding: 0.75rem;
            background: #1E2139;
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 0.5rem;
            color: #fff;
        }
        button {
            background: linear-gradient(135deg, #00D4FF, #9D4EDD);
            color: #fff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        button:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <header>
        <h1>MCP Server Hub</h1>
        <nav>
            <a href="#catalog">Catalog</a>
            <a href="#installed">Installed</a>
            <a href="#monitor">Monitor</a>
            <a href="#settings">Settings</a>
        </nav>
    </header>
    
    <main>
        <section id="catalog">
            <h2>MCP Server Catalog</h2>
            <div class="search-bar">
                <input type="text" placeholder="Search servers..." id="searchInput">
                <button onclick="loadServers()">Refresh</button>
            </div>
            
            <div class="server-grid" id="serverGrid">
                <!-- Server cards will be loaded here -->
            </div>
        </section>
    </main>
    
    <script>
        async function loadServers() {
            try {
                const response = await fetch("/api/servers");
                const servers = await response.json();
                
                const grid = document.getElementById("serverGrid");
                grid.innerHTML = servers.map(server => `
                    <div class="server-card">
                        <h3>${server.name}</h3>
                        <p>${server.description}</p>
                        <div>Type: ${server.type}</div>
                        <div>Version: ${server.version}</div>
                        <span class="status ${server.health.status}">${server.health.status}</span>
                    </div>
                `).join("");
            } catch (error) {
                console.error("Failed to load servers:", error);
            }
        }
        
        // Load servers on page load
        loadServers();
        
        // Search functionality
        document.getElementById("searchInput").addEventListener("input", (e) => {
            const search = e.target.value.toLowerCase();
            document.querySelectorAll(".server-card").forEach(card => {
                const text = card.textContent.toLowerCase();
                card.style.display = text.includes(search) ? "block" : "none";
            });
        });
    </script>
</body>
</html>
EOF'
    
    echo -e "${GREEN}Web UI created${NC}"
}

# Function to add initial MCP servers
add_initial_servers() {
    echo -e "${YELLOW}Adding initial MCP servers...${NC}"
    
    # Create configuration for existing MCP servers
    pct exec $CONTAINER_ID -- bash -c 'cat > /opt/mcp-hub/config/servers.json << "EOF"
[
  {
    "id": "it-assistant",
    "name": "IT Assistant MCP",
    "description": "Enterprise IT management and monitoring",
    "version": "1.0.0",
    "type": "native",
    "transport": "stdio",
    "connection": {
      "command": "node",
      "args": ["/opt/it-assistant-mcp/dist/index.js"]
    },
    "capabilities": {
      "tools": true,
      "resources": true,
      "prompts": true
    },
    "metadata": {
      "author": "ALIAS",
      "tags": ["infrastructure", "monitoring", "automation"]
    }
  },
  {
    "id": "browser-use",
    "name": "Browser Use MCP",
    "description": "Browser automation and web scraping",
    "version": "1.0.0",
    "type": "docker",
    "transport": "http",
    "connection": {
      "dockerImage": "browser-use-mcp-server:latest",
      "port": 8000
    },
    "capabilities": {
      "tools": true,
      "resources": false
    },
    "metadata": {
      "author": "Browser Use Team",
      "tags": ["automation", "browser", "scraping"]
    }
  },
  {
    "id": "proxmox-production",
    "name": "Proxmox Production MCP",
    "description": "Enterprise-grade Proxmox management",
    "version": "1.0.0",
    "type": "remote",
    "transport": "http",
    "connection": {
      "url": "http://*************:8080/api/mcp"
    },
    "capabilities": {
      "tools": true,
      "resources": true
    },
    "metadata": {
      "author": "Cronus87",
      "tags": ["infrastructure", "virtualization", "proxmox"]
    }
  }
]
EOF'
    
    echo -e "${GREEN}Initial servers configured${NC}"
}

# Function to setup nginx reverse proxy
setup_nginx() {
    echo -e "${YELLOW}Setting up Nginx reverse proxy...${NC}"
    
    pct exec $CONTAINER_ID -- bash -c 'cat > /etc/nginx/sites-available/mcp-hub << "EOF"
server {
    listen 80;
    server_name mcp-hub.local;
    
    location / {
        proxy_pass http://localhost:3100;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /mcp/ {
        proxy_pass http://localhost:3101/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
EOF'
    
    pct exec $CONTAINER_ID -- ln -sf /etc/nginx/sites-available/mcp-hub /etc/nginx/sites-enabled/
    pct exec $CONTAINER_ID -- nginx -t
    pct exec $CONTAINER_ID -- systemctl restart nginx
    
    echo -e "${GREEN}Nginx configured${NC}"
}

# Function to finalize setup
finalize_setup() {
    echo -e "${YELLOW}Finalizing setup...${NC}"
    
    # Install npm packages
    pct exec $CONTAINER_ID -- bash -c "cd /opt/mcp-hub && npm install"
    
    # Build TypeScript
    pct exec $CONTAINER_ID -- bash -c "cd /opt/mcp-hub && npm run build"
    
    # Start services
    pct exec $CONTAINER_ID -- bash -c "cd /opt/mcp-hub && docker-compose up -d"
    
    # Get container IP
    CONTAINER_IP=$(pct exec $CONTAINER_ID -- ip -4 addr show eth0 | grep inet | awk '{print $2}' | cut -d/ -f1)
    
    echo -e "${GREEN}✅ MCP Hub deployed successfully!${NC}"
    echo -e "\n${YELLOW}Access Information:${NC}"
    echo -e "  Web UI: ${GREEN}http://$CONTAINER_IP:$HUB_PORT${NC}"
    echo -e "  API: ${GREEN}http://$CONTAINER_IP:$HUB_PORT/api${NC}"
    echo -e "  MCP Proxy: ${GREEN}http://$CONTAINER_IP:$PROXY_PORT${NC}"
    echo -e "\n${YELLOW}Next Steps:${NC}"
    echo -e "  1. Add to hosts file: ${GREEN}$CONTAINER_IP   mcp-hub.local${NC}"
    echo -e "  2. Access web UI to manage MCP servers"
    echo -e "  3. Configure Claude Desktop to use MCP Hub"
    echo -e "\n${YELLOW}Claude Desktop Configuration:${NC}"
    echo -e '  {
    "mcpServers": {
      "mcp-hub": {
        "command": "npx",
        "args": ["mcp-remote-client", "http://mcp-hub.local:3100/mcp"]
      }
    }
  }'
}

# Main execution
main() {
    check_proxmox
    
    echo -e "${YELLOW}This will deploy MCP Hub for centralized MCP server management${NC}"
    echo -e "Container ID: $CONTAINER_ID"
    echo -e "Memory: ${CONTAINER_MEMORY}MB"
    echo -e "Cores: $CONTAINER_CORES"
    echo -e "Disk: ${CONTAINER_DISK}GB"
    read -p "Continue? (y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    create_container
    install_dependencies
    create_mcp_hub
    create_web_ui
    add_initial_servers
    setup_nginx
    finalize_setup
}

# Run main function
main