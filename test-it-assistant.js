#!/usr/bin/env node
import { spawn } from 'child_process';

console.log('Testing IT Assistant with Proxmox integration...\n');

// Start the server
const server = spawn('node', ['src/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: { ...process.env }
});

// Handle server output
let buffer = '';
server.stdout.on('data', (data) => {
  buffer += data.toString();
  const lines = buffer.split('\n');
  buffer = lines.pop(); // Keep incomplete line in buffer
  
  lines.forEach(line => {
    if (line.trim()) {
      try {
        const json = JSON.parse(line);
        if (json.result) {
          console.log('\nResult:', JSON.stringify(json.result, null, 2));
        }
      } catch (e) {
        // Not JSON, ignore
      }
    }
  });
});

server.stderr.on('data', (data) => {
  const message = data.toString();
  if (message.includes('[IT Assistant]')) {
    console.log(message.trim());
  }
});

// Send test requests
async function sendRequest(method, params = {}) {
  const request = {
    jsonrpc: '2.0',
    method,
    params,
    id: Date.now()
  };
  
  console.log('\nRequest:', method);
  server.stdin.write(JSON.stringify(request) + '\n');
}

// Test sequence
async function runTests() {
  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 1: List all VMs
  console.log('\n=== Test 1: List all VMs on Proxmox ===');
  await sendRequest('tools/call', {
    name: 'list_all_vms',
    arguments: {}
  });
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test 2: Check system health
  console.log('\n=== Test 2: Check system health ===');
  await sendRequest('tools/call', {
    name: 'check_system_health',
    arguments: {
      include_services: true,
      include_resources: true,
      include_vms: true,
      nodes: ['pve']
    }
  });
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Test 3: Get resource usage
  console.log('\n=== Test 3: Get resource usage ===');
  await sendRequest('tools/call', {
    name: 'get_resource_usage',
    arguments: {
      node: 'localhost',
      timeRange: '1h'
    }
  });
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('\n\nTests completed. Press Ctrl+C to exit.');
}

// Handle exit
process.on('SIGINT', () => {
  console.log('\nShutting down...');
  server.kill();
  process.exit(0);
});

// Run tests
runTests().catch(console.error);