#!/bin/bash

# Checkmate Dashboard Deployment Script for Proxmox
# This script deploys Checkmate monitoring dashboard with Capture agent

set -e

echo "🚀 Checkmate Dashboard Deployment Script"
echo "======================================="

# Configuration
CONTAINER_ID=108
CONTAINER_HOSTNAME="checkmate"
CONTAINER_MEMORY=2048
CONTAINER_CORES=2
CONTAINER_DISK=8
CHECKMATE_PORT=3000

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check if running on Proxmox
check_proxmox() {
    if [ ! -f /etc/pve/version ]; then
        echo -e "${RED}Error: This script must be run on a Proxmox host${NC}"
        exit 1
    fi
}

# Function to create container
create_container() {
    echo -e "${YELLOW}Creating LXC container for Checkmate...${NC}"
    
    # Check if container already exists
    if pct status $CONTAINER_ID &>/dev/null; then
        echo -e "${RED}Container $CONTAINER_ID already exists${NC}"
        read -p "Do you want to destroy and recreate it? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pct stop $CONTAINER_ID || true
            pct destroy $CONTAINER_ID
        else
            exit 1
        fi
    fi
    
    # Create container
    pct create $CONTAINER_ID /var/lib/vz/template/cache/debian-12-standard_12.7-1_amd64.tar.zst \
        --hostname $CONTAINER_HOSTNAME \
        --memory $CONTAINER_MEMORY \
        --cores $CONTAINER_CORES \
        --net0 name=eth0,bridge=vmbr0,ip=dhcp \
        --storage local \
        --rootfs local:$CONTAINER_DISK \
        --unprivileged 1 \
        --features nesting=1
    
    # Start container
    pct start $CONTAINER_ID
    sleep 10
    
    echo -e "${GREEN}Container created successfully${NC}"
}

# Function to install Docker in container
install_docker() {
    echo -e "${YELLOW}Installing Docker in container...${NC}"
    
    # Update system
    pct exec $CONTAINER_ID -- apt update
    pct exec $CONTAINER_ID -- apt upgrade -y
    
    # Install prerequisites
    pct exec $CONTAINER_ID -- apt install -y curl ca-certificates gnupg
    
    # Install Docker
    pct exec $CONTAINER_ID -- bash -c "curl -fsSL https://get.docker.com | sh"
    
    # Install Docker Compose
    pct exec $CONTAINER_ID -- apt install -y docker-compose-plugin
    
    echo -e "${GREEN}Docker installed successfully${NC}"
}

# Function to deploy Checkmate
deploy_checkmate() {
    echo -e "${YELLOW}Deploying Checkmate...${NC}"
    
    # Create directory
    pct exec $CONTAINER_ID -- mkdir -p /opt/checkmate
    
    # Generate secrets
    JWT_SECRET=$(openssl rand -base64 32)
    
    # Create docker-compose.yml
    pct exec $CONTAINER_ID -- bash -c "cat > /opt/checkmate/docker-compose.yml << 'EOF'
version: '3.8'

services:
  checkmate:
    image: ghcr.io/bluewave-labs/checkmate:latest
    container_name: checkmate
    restart: unless-stopped
    ports:
      - \"$CHECKMATE_PORT:3000\"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/checkmate
      - JWT_SECRET=$JWT_SECRET
      - PORT=3000
    depends_on:
      - mongodb
    volumes:
      - ./uploads:/app/uploads
      - ./config:/app/config
    networks:
      - checkmate-network

  mongodb:
    image: mongo:7
    container_name: checkmate-mongodb
    restart: unless-stopped
    volumes:
      - mongodb-data:/data/db
      - mongodb-config:/data/configdb
    environment:
      - MONGO_INITDB_DATABASE=checkmate
    networks:
      - checkmate-network

volumes:
  mongodb-data:
  mongodb-config:

networks:
  checkmate-network:
    driver: bridge
EOF"
    
    # Start services
    pct exec $CONTAINER_ID -- bash -c "cd /opt/checkmate && docker compose up -d"
    
    echo -e "${GREEN}Checkmate deployed successfully${NC}"
}

# Function to install Capture agent on host
install_capture_host() {
    echo -e "${YELLOW}Installing Capture agent on Proxmox host...${NC}"
    
    # Download Capture
    wget -q https://github.com/bluewave-labs/capture/releases/latest/download/capture-linux-amd64 -O /usr/local/bin/capture
    chmod +x /usr/local/bin/capture
    
    # Get container IP
    CONTAINER_IP=$(pct exec $CONTAINER_ID -- ip -4 addr show eth0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    # Note: Token will be obtained after Checkmate setup
    echo -e "${YELLOW}Capture agent installed. Configure after Checkmate setup:${NC}"
    echo -e "  1. Access Checkmate at http://$CONTAINER_IP:$CHECKMATE_PORT"
    echo -e "  2. Create admin account and get API token"
    echo -e "  3. Run: /usr/local/bin/capture --server http://$CONTAINER_IP:$CHECKMATE_PORT --token YOUR_TOKEN"
    
    echo -e "${GREEN}Capture agent ready for configuration${NC}"
}

# Function to create monitoring targets
create_monitoring_config() {
    echo -e "${YELLOW}Creating monitoring configuration...${NC}"
    
    # Get container IP
    CONTAINER_IP=$(pct exec $CONTAINER_ID -- ip -4 addr show eth0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    cat > /tmp/checkmate-monitors.json << EOF
{
  "monitors": [
    {
      "name": "Proxmox Web UI",
      "url": "https://*************:8006",
      "type": "https",
      "interval": 60
    },
    {
      "name": "Dokploy",
      "url": "http://************:3000",
      "type": "http",
      "interval": 60
    },
    {
      "name": "Browser-Use MCP",
      "url": "http://************:8000",
      "type": "http",
      "interval": 60
    },
    {
      "name": "Home Assistant",
      "url": "http://************:8123",
      "type": "http",
      "interval": 60
    },
    {
      "name": "Proxmox Backup Server",
      "url": "https://************:8007",
      "type": "https",
      "interval": 60
    }
  ]
}
EOF
    
    echo -e "${GREEN}Monitoring configuration created at /tmp/checkmate-monitors.json${NC}"
}

# Function to show next steps
show_next_steps() {
    CONTAINER_IP=$(pct exec $CONTAINER_ID -- ip -4 addr show eth0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    echo -e "\n${GREEN}✅ Deployment Complete!${NC}"
    echo -e "\n${YELLOW}Next Steps:${NC}"
    echo -e "1. Access Checkmate at: ${GREEN}http://$CONTAINER_IP:$CHECKMATE_PORT${NC}"
    echo -e "2. Create admin account"
    echo -e "3. Import monitors from /tmp/checkmate-monitors.json"
    echo -e "4. Get API token for Capture agents"
    echo -e "5. Configure Capture on all nodes"
    echo -e "\n${YELLOW}Container Details:${NC}"
    echo -e "  Container ID: $CONTAINER_ID"
    echo -e "  IP Address: $CONTAINER_IP"
    echo -e "  Port: $CHECKMATE_PORT"
    echo -e "  Data: /opt/checkmate"
    echo -e "\n${YELLOW}To install Capture on other nodes:${NC}"
    echo -e "  wget https://github.com/bluewave-labs/capture/releases/latest/download/capture-linux-amd64"
    echo -e "  chmod +x capture-linux-amd64"
    echo -e "  ./capture-linux-amd64 --server http://$CONTAINER_IP:$CHECKMATE_PORT --token YOUR_TOKEN"
}

# Main execution
main() {
    check_proxmox
    
    echo -e "${YELLOW}This will deploy Checkmate monitoring dashboard${NC}"
    echo -e "Container ID: $CONTAINER_ID"
    echo -e "Memory: ${CONTAINER_MEMORY}MB"
    echo -e "Cores: $CONTAINER_CORES"
    echo -e "Disk: ${CONTAINER_DISK}GB"
    read -p "Continue? (y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    create_container
    install_docker
    deploy_checkmate
    install_capture_host
    create_monitoring_config
    show_next_steps
}

# Run main function
main