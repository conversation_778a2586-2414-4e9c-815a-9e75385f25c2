{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(node:*)", "mcp__proxmox-production__list_vms", "mcp__proxmox-server__proxmox_get_vms", "Bash(ping:*)", "Bash(./quickstart.sh:*)", "Bash(npm start)", "Bash(claude mcp add:*)", "Bash(ls:*)", "mcp__ElevenLabs__text_to_speech", "mcp__ElevenLabs__play_audio", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "mcp__proxmox-production__execute_command", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(./test-cli.sh:*)", "Bash(ssh:*)", "<PERSON><PERSON>(curl:*)", "mcp__proxmox-production__node_status", "mcp__proxmox-server__proxmox_get_nodes", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(python3:*)", "Bash(osascript:*)", "<PERSON><PERSON>(/Users/<USER>/it-assistant-mcp/terminal-manager.sh test-all)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(git clone:*)", "WebFetch(domain:mastra.ai)"], "deny": []}}