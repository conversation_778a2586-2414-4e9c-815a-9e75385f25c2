#!/bin/bash

# Build script for IT Assistant MCP Docker image

set -e

# Configuration
REGISTRY=${REGISTRY:-"docker.io/yourusername"}
IMAGE_NAME="it-assistant-mcp"
VERSION=${VERSION:-$(git describe --tags --always --dirty 2>/dev/null || echo "dev")}
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Building IT Assistant MCP Docker Image${NC}"
echo "Registry: $REGISTRY"
echo "Version: $VERSION"
echo "Commit: $GIT_COMMIT"
echo ""

# Function to build image
build_image() {
    local dockerfile=$1
    local tag=$2
    
    echo -e "${YELLOW}Building $tag...${NC}"
    
    docker build \
        --build-arg VERSION=$VERSION \
        --build-arg BUILD_DATE=$BUILD_DATE \
        --build-arg GIT_COMMIT=$GIT_COMMIT \
        --label "org.opencontainers.image.created=$BUILD_DATE" \
        --label "org.opencontainers.image.revision=$GIT_COMMIT" \
        --label "org.opencontainers.image.version=$VERSION" \
        -f $dockerfile \
        -t $tag \
        .
    
    echo -e "${GREEN}✓ Built $tag${NC}"
}

# Build development image
if [ "$1" == "dev" ] || [ "$1" == "all" ]; then
    build_image "Dockerfile" "$REGISTRY/$IMAGE_NAME:dev"
fi

# Build production image
if [ "$1" == "prod" ] || [ "$1" == "all" ] || [ -z "$1" ]; then
    build_image "Dockerfile.production" "$REGISTRY/$IMAGE_NAME:$VERSION"
    
    # Tag as latest if not a dev version
    if [[ ! $VERSION =~ -dirty$ ]] && [[ ! $VERSION =~ ^dev$ ]]; then
        docker tag "$REGISTRY/$IMAGE_NAME:$VERSION" "$REGISTRY/$IMAGE_NAME:latest"
        echo -e "${GREEN}✓ Tagged as latest${NC}"
    fi
fi

# Multi-arch build (optional)
if [ "$1" == "multi" ]; then
    echo -e "${YELLOW}Building multi-arch image...${NC}"
    
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --build-arg VERSION=$VERSION \
        --build-arg BUILD_DATE=$BUILD_DATE \
        --build-arg GIT_COMMIT=$GIT_COMMIT \
        -f Dockerfile.production \
        -t "$REGISTRY/$IMAGE_NAME:$VERSION" \
        -t "$REGISTRY/$IMAGE_NAME:latest" \
        --push \
        .
    
    echo -e "${GREEN}✓ Built and pushed multi-arch image${NC}"
fi

# Show image info
echo ""
echo -e "${GREEN}Build complete!${NC}"
echo ""
docker images | grep $IMAGE_NAME | head -5

# Scan for vulnerabilities (optional)
if command -v trivy &> /dev/null; then
    echo ""
    echo -e "${YELLOW}Running security scan...${NC}"
    trivy image --severity HIGH,CRITICAL "$REGISTRY/$IMAGE_NAME:$VERSION"
fi

echo ""
echo "To push the image:"
echo "  docker push $REGISTRY/$IMAGE_NAME:$VERSION"
echo "  docker push $REGISTRY/$IMAGE_NAME:latest"
echo ""
echo "To run locally:"
echo "  docker run -it --rm $REGISTRY/$IMAGE_NAME:$VERSION"