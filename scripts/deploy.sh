#!/bin/bash

# Deployment script for IT Assistant MCP

set -e

# Configuration
REGISTRY=${REGISTRY:-"docker.io/yourusername"}
IMAGE_NAME="it-assistant-mcp"
VERSION=${VERSION:-"latest"}
DEPLOY_ENV=${DEPLOY_ENV:-"production"}

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}IT Assistant MCP Deployment Script${NC}"
echo "Environment: $DEPLOY_ENV"
echo "Version: $VERSION"
echo ""

# Function to check prerequisites
check_prerequisites() {
    local missing=()
    
    if ! command -v docker &> /dev/null; then
        missing+=("docker")
    fi
    
    if [ "$1" == "kubernetes" ] && ! command -v kubectl &> /dev/null; then
        missing+=("kubectl")
    fi
    
    if [ "$1" == "swarm" ] && ! docker info 2>/dev/null | grep -q "Swarm: active"; then
        missing+=("Docker Swarm (not initialized)")
    fi
    
    if [ ${#missing[@]} -ne 0 ]; then
        echo -e "${RED}Missing prerequisites: ${missing[*]}${NC}"
        exit 1
    fi
}

# Docker Compose deployment
deploy_compose() {
    echo -e "${YELLOW}Deploying with Docker Compose...${NC}"
    
    # Create data directories
    mkdir -p data/{reports,playbooks,templates}
    mkdir -p custom/{playbooks,templates}
    
    # Pull latest image
    docker-compose pull
    
    # Deploy
    docker-compose up -d
    
    # Wait for health check
    echo "Waiting for service to be healthy..."
    sleep 5
    
    if docker-compose ps | grep -q "healthy"; then
        echo -e "${GREEN}✓ Deployment successful${NC}"
    else
        echo -e "${RED}✗ Service not healthy${NC}"
        docker-compose logs --tail=50
        exit 1
    fi
}

# Kubernetes deployment
deploy_kubernetes() {
    echo -e "${YELLOW}Deploying to Kubernetes...${NC}"
    
    NAMESPACE=${NAMESPACE:-"it-operations"}
    
    # Create namespace if it doesn't exist
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Update image in deployment
    sed -i.bak "s|image: .*|image: $REGISTRY/$IMAGE_NAME:$VERSION|g" deploy/kubernetes/deployment.yaml
    
    # Apply configurations
    kubectl apply -f deploy/kubernetes/ -n $NAMESPACE
    
    # Wait for rollout
    echo "Waiting for deployment to complete..."
    kubectl rollout status deployment/it-assistant-mcp -n $NAMESPACE --timeout=300s
    
    # Check pod status
    kubectl get pods -n $NAMESPACE -l app=it-assistant
    
    echo -e "${GREEN}✓ Kubernetes deployment successful${NC}"
}

# Docker Swarm deployment
deploy_swarm() {
    echo -e "${YELLOW}Deploying to Docker Swarm...${NC}"
    
    STACK_NAME=${STACK_NAME:-"it-assistant"}
    
    # Create secrets if they don't exist
    if ! docker secret ls | grep -q proxmox_token; then
        echo "Creating secrets..."
        echo -n "$PROXMOX_TOKEN" | docker secret create proxmox_token -
        echo -n "$ELEVENLABS_API_KEY" | docker secret create elevenlabs_key -
        
        if [ -f ~/.ssh/id_rsa ]; then
            docker secret create ssh_key ~/.ssh/id_rsa
        fi
    fi
    
    # Deploy stack
    REGISTRY=$REGISTRY VERSION=$VERSION docker stack deploy \
        -c deploy/docker-swarm/stack.yml \
        $STACK_NAME
    
    # Wait for service
    echo "Waiting for service to be ready..."
    sleep 10
    
    # Check service status
    docker service ls --filter "label=com.docker.stack.namespace=$STACK_NAME"
    
    echo -e "${GREEN}✓ Swarm deployment successful${NC}"
}

# Systemd deployment (for single server)
deploy_systemd() {
    echo -e "${YELLOW}Deploying with systemd...${NC}"
    
    # Create systemd service file
    sudo tee /etc/systemd/system/it-assistant-mcp.service > /dev/null <<EOF
[Unit]
Description=IT Assistant MCP Server
After=docker.service
Requires=docker.service

[Service]
Type=simple
Restart=always
RestartSec=10
ExecStartPre=-/usr/bin/docker stop it-assistant-mcp
ExecStartPre=-/usr/bin/docker rm it-assistant-mcp
ExecStart=/usr/bin/docker run --rm \\
    --name it-assistant-mcp \\
    --env-file /etc/it-assistant/env \\
    -v /var/lib/it-assistant/reports:/app/reports \\
    -v /etc/it-assistant/playbooks:/app/playbooks:ro \\
    -v /home/<USER>/.ssh:/home/<USER>/.ssh:ro \\
    $REGISTRY/$IMAGE_NAME:$VERSION
ExecStop=/usr/bin/docker stop it-assistant-mcp

[Install]
WantedBy=multi-user.target
EOF

    # Create directories
    sudo mkdir -p /etc/it-assistant /var/lib/it-assistant/{reports,playbooks}
    
    # Create env file if it doesn't exist
    if [ ! -f /etc/it-assistant/env ]; then
        sudo cp .env.example /etc/it-assistant/env
        echo -e "${YELLOW}Please edit /etc/it-assistant/env with your configuration${NC}"
    fi
    
    # Reload systemd and start service
    sudo systemctl daemon-reload
    sudo systemctl enable it-assistant-mcp
    sudo systemctl start it-assistant-mcp
    
    # Check status
    sudo systemctl status it-assistant-mcp --no-pager
    
    echo -e "${GREEN}✓ Systemd deployment successful${NC}"
}

# Main deployment logic
case "$1" in
    compose)
        check_prerequisites "compose"
        deploy_compose
        ;;
    kubernetes|k8s)
        check_prerequisites "kubernetes"
        deploy_kubernetes
        ;;
    swarm)
        check_prerequisites "swarm"
        deploy_swarm
        ;;
    systemd)
        deploy_systemd
        ;;
    *)
        echo "Usage: $0 {compose|kubernetes|swarm|systemd}"
        echo ""
        echo "Options:"
        echo "  compose    - Deploy using Docker Compose (local development)"
        echo "  kubernetes - Deploy to Kubernetes cluster"
        echo "  swarm      - Deploy to Docker Swarm"
        echo "  systemd    - Deploy as systemd service (single server)"
        echo ""
        echo "Environment variables:"
        echo "  REGISTRY   - Docker registry (default: docker.io/yourusername)"
        echo "  VERSION    - Image version (default: latest)"
        echo "  DEPLOY_ENV - Deployment environment (default: production)"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}Deployment complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Verify the deployment is healthy"
echo "2. Configure MCP clients to connect"
echo "3. Monitor logs for any issues"
echo ""

# Show connection info based on deployment type
case "$1" in
    compose)
        echo "View logs: docker-compose logs -f"
        ;;
    kubernetes)
        echo "View logs: kubectl logs -f -n $NAMESPACE -l app=it-assistant"
        ;;
    swarm)
        echo "View logs: docker service logs -f ${STACK_NAME}_it-assistant"
        ;;
    systemd)
        echo "View logs: sudo journalctl -u it-assistant-mcp -f"
        ;;
esac