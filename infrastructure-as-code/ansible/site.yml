---
# ALIAS Infrastructure Configuration Management
# Main Ansible Playbook for Proxmox Infrastructure

- name: Configure Proxmox Host
  hosts: proxmox
  become: yes
  vars:
    proxmox_host: *************
    storage_warning_threshold: 80
    backup_schedule: "02:00"
  
  tasks:
    - name: Ensure critical packages are installed
      apt:
        name:
          - pve-firewall
          - zfs-utils
          - nfs-common
          - monitoring-plugins
          - htop
          - iotop
          - ncdu
        state: present
        update_cache: yes

    - name: Configure sysctl for performance
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      loop:
        - { name: 'vm.swappiness', value: '10' }
        - { name: 'net.ipv4.ip_forward', value: '1' }
        - { name: 'net.ipv6.conf.all.forwarding', value: '1' }
        - { name: 'net.core.netdev_max_backlog', value: '5000' }
        - { name: 'net.ipv4.tcp_congestion_control', value: 'bbr' }

    - name: Configure storage monitoring alert
      cron:
        name: "Check storage usage"
        minute: "*/15"
        job: |
          USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
          if [ $USAGE -gt {{ storage_warning_threshold }} ]; then
            echo "CRITICAL: Root filesystem is $USAGE% full" | mail -s "Proxmox Storage Alert" <EMAIL>
          fi

- name: Configure Kubernetes Clusters
  hosts: kubernetes_masters
  become: yes
  roles:
    - kubernetes-master
    - monitoring-agent
    - security-hardening

- name: Configure Kubernetes Workers
  hosts: kubernetes_workers
  become: yes
  roles:
    - kubernetes-worker
    - monitoring-agent
    - security-hardening

- name: Deploy Core Services
  hosts: localhost
  tasks:
    - name: Deploy GitLab
      include_role:
        name: gitlab
      vars:
        gitlab_external_url: "https://gitlab.alias.local"
        gitlab_email_enabled: true
        gitlab_backup_schedule: "0 2 * * *"

    - name: Deploy Harbor Registry
      include_role:
        name: harbor
      vars:
        harbor_hostname: "registry.alias.local"
        harbor_admin_password: "{{ vault_harbor_admin_password }}"
        harbor_db_password: "{{ vault_harbor_db_password }}"

    - name: Deploy HashiCorp Vault
      include_role:
        name: vault
      vars:
        vault_cluster_name: "alias-vault"
        vault_ui: true
        vault_tls_disable: false

- name: Configure Monitoring Stack
  hosts: monitoring
  become: yes
  tasks:
    - name: Deploy Prometheus
      include_role:
        name: prometheus
      vars:
        prometheus_retention_time: "30d"
        prometheus_storage_size: "100GB"
        prometheus_targets:
          - job_name: 'proxmox'
            static_configs:
              - targets: ['*************:9100']
          - job_name: 'kubernetes'
            kubernetes_sd_configs:
              - role: node

    - name: Deploy Grafana
      include_role:
        name: grafana
      vars:
        grafana_admin_password: "{{ vault_grafana_admin_password }}"
        grafana_datasources:
          - name: Prometheus
            type: prometheus
            url: http://prometheus.alias.local:9090
            isDefault: true
          - name: Elasticsearch
            type: elasticsearch
            url: http://elasticsearch.alias.local:9200

    - name: Configure Grafana Dashboards
      grafana_dashboard:
        url: "http://grafana.alias.local:3000"
        url_username: admin
        url_password: "{{ vault_grafana_admin_password }}"
        state: present
        dashboard_id: "{{ item }}"
      loop:
        - 11074  # Node Exporter Full
        - 8588   # Kubernetes Cluster Overview
        - 9614   # Nginx Ingress Controller
        - 7362   # Container Registry

- name: Security Hardening
  hosts: all
  become: yes
  tasks:
    - name: Configure fail2ban
      include_role:
        name: fail2ban
      vars:
        fail2ban_services:
          - name: sshd
            maxretry: 3
            bantime: 3600
          - name: proxmox
            maxretry: 5
            bantime: 1800

    - name: Configure firewall rules
      ufw:
        rule: "{{ item.rule }}"
        port: "{{ item.port }}"
        proto: "{{ item.proto }}"
        src: "{{ item.src | default(omit) }}"
      loop:
        - { rule: 'allow', port: '22', proto: 'tcp', src: '**********/24' }
        - { rule: 'allow', port: '8006', proto: 'tcp', src: '**********/24' }
        - { rule: 'allow', port: '443', proto: 'tcp' }
        - { rule: 'allow', port: '80', proto: 'tcp' }

    - name: Configure auditd
      include_role:
        name: auditd
      vars:
        auditd_rules:
          - '-w /etc/passwd -p wa -k passwd_changes'
          - '-w /etc/group -p wa -k group_changes'
          - '-w /etc/sudoers -p wa -k sudoers_changes'
          - '-a exit,always -F arch=b64 -S execve -k exec'

- name: Backup Configuration
  hosts: proxmox
  become: yes
  tasks:
    - name: Configure backup jobs
      proxmox_backup_job:
        name: "{{ item.name }}"
        schedule: "{{ item.schedule }}"
        storage: pbs-backup
        vmid: "{{ item.vmid }}"
        mode: "{{ item.mode }}"
        compress: zstd
        mailto: <EMAIL>
      loop:
        - { name: 'production-daily', schedule: '0 2 * * *', vmid: '301-313', mode: 'snapshot' }
        - { name: 'staging-daily', schedule: '0 3 * * *', vmid: '401-410', mode: 'snapshot' }
        - { name: 'dev-weekly', schedule: '0 4 * * 0', vmid: '201-210', mode: 'snapshot' }
        - { name: 'full-weekly', schedule: '0 4 * * 6', vmid: 'all', mode: 'stop' }

    - name: Configure offsite backup sync
      cron:
        name: "Sync backups to offsite"
        hour: "5"
        minute: "0"
        weekday: "0"
        job: |
          proxmox-backup-client sync \
            --repository <EMAIL>:backup \
            --password-file /etc/pve/priv/pbs-offsite.pw \
            --rate-limit 50000