# Core Services Module - Essential platform services

terraform {
  required_providers {
    proxmox = {
      source = "Telmate/proxmox"
      version = ">=2.9.0"
    }
  }
}

# Variables
variable "services" {
  description = "Map of core service configurations"
  type = map(object({
    pool           = string
    cores          = number
    memory         = number
    disk           = number
    network_bridge = string
  }))
}

variable "proxmox_node" {
  description = "Proxmox node name"
  type        = string
  default     = "pve"
}

variable "template_name" {
  description = "Name of the VM template to clone"
  type        = string
  default     = "ubuntu-2204-cloudinit"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
  default     = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAEOYIc0SgIoBpSD9QNdKwRXCDmaAVdptNoixjZhxZ/x alias-dan@proxmox-access"
}

variable "domain" {
  description = "Base domain for services"
  type        = string
  default     = "alias.local"
}

# VM ID mapping
locals {
  vm_ids = {
    gitlab   = 320
    harbor   = 321
    vault    = 322
    keycloak = 323
  }
}

# GitLab VM
resource "proxmox_vm_qemu" "gitlab" {
  name        = "gitlab"
  vmid        = local.vm_ids.gitlab
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.gitlab.pool

  cores    = var.services.gitlab.cores
  memory   = var.services.gitlab.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.gitlab.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.gitlab.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y curl openssh-server ca-certificates tzdata perl",
      
      # Install GitLab
      "curl -sS https://packages.gitlab.com/install/repositories/gitlab/gitlab-ce/script.deb.sh | sudo bash",
      "sudo EXTERNAL_URL='https://gitlab.${var.domain}' apt-get install gitlab-ce",
      
      # Configure GitLab
      "sudo gitlab-ctl reconfigure",
      
      # Set up container registry
      "sudo sed -i \"s/# registry_external_url/registry_external_url 'https:\\/\\/registry.${var.domain}'/\" /etc/gitlab/gitlab.rb",
      "sudo gitlab-ctl reconfigure"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Harbor Registry VM
resource "proxmox_vm_qemu" "harbor" {
  name        = "harbor"
  vmid        = local.vm_ids.harbor
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.harbor.pool

  cores    = var.services.harbor.cores
  memory   = var.services.harbor.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.harbor.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.harbor.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y docker.io docker-compose",
      
      # Download Harbor installer
      "wget https://github.com/goharbor/harbor/releases/download/v2.10.0/harbor-online-installer-v2.10.0.tgz",
      "tar xzvf harbor-online-installer-v2.10.0.tgz",
      
      # Configure Harbor
      "cd harbor",
      "sudo sed -i 's/hostname: reg.mydomain.com/hostname: registry.${var.domain}/' harbor.yml",
      "sudo sed -i 's/port: 80/port: 443/' harbor.yml",
      "sudo sed -i 's/# https:/https:/' harbor.yml",
      "sudo sed -i 's/#   port: 443/  port: 443/' harbor.yml",
      
      # Install Harbor
      "sudo ./install.sh --with-trivy --with-chartmuseum"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# HashiCorp Vault VM
resource "proxmox_vm_qemu" "vault" {
  name        = "vault"
  vmid        = local.vm_ids.vault
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.vault.pool

  cores    = var.services.vault.cores
  memory   = var.services.vault.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.vault.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.vault.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y wget unzip",
      
      # Install Vault
      "wget https://releases.hashicorp.com/vault/1.15.0/vault_1.15.0_linux_amd64.zip",
      "unzip vault_1.15.0_linux_amd64.zip",
      "sudo mv vault /usr/local/bin/",
      "sudo chmod +x /usr/local/bin/vault",
      
      # Create Vault user and directories
      "sudo useradd --system --home /var/lib/vault --shell /bin/false vault",
      "sudo mkdir -p /etc/vault /var/lib/vault",
      "sudo chown -R vault:vault /etc/vault /var/lib/vault",
      
      # Create Vault configuration
      "cat <<EOF | sudo tee /etc/vault/vault.hcl",
      "ui = true",
      "disable_mlock = true",
      "",
      "storage \"file\" {",
      "  path = \"/var/lib/vault\"",
      "}",
      "",
      "listener \"tcp\" {",
      "  address = \"0.0.0.0:8200\"",
      "  tls_disable = 1",
      "}",
      "",
      "api_addr = \"http://vault.${var.domain}:8200\"",
      "cluster_addr = \"https://vault.${var.domain}:8201\"",
      "EOF",
      
      # Create systemd service
      "cat <<EOF | sudo tee /etc/systemd/system/vault.service",
      "[Unit]",
      "Description=Vault",
      "Documentation=https://www.vaultproject.io/docs/",
      "Requires=network-online.target",
      "After=network-online.target",
      "ConditionFileNotEmpty=/etc/vault/vault.hcl",
      "",
      "[Service]",
      "Type=notify",
      "User=vault",
      "Group=vault",
      "ExecStart=/usr/local/bin/vault server -config=/etc/vault/vault.hcl",
      "ExecReload=/bin/kill --signal HUP $MAINPID",
      "KillMode=process",
      "Restart=on-failure",
      "RestartSec=5",
      "TimeoutStopSec=30",
      "",
      "[Install]",
      "WantedBy=multi-user.target",
      "EOF",
      
      # Start Vault
      "sudo systemctl daemon-reload",
      "sudo systemctl enable vault",
      "sudo systemctl start vault",
      
      # Initialize Vault (save the output!)
      "sleep 5",
      "vault operator init -address=http://127.0.0.1:8200 | sudo tee /root/vault-init.txt"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Keycloak VM
resource "proxmox_vm_qemu" "keycloak" {
  name        = "keycloak"
  vmid        = local.vm_ids.keycloak
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.keycloak.pool

  cores    = var.services.keycloak.cores
  memory   = var.services.keycloak.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.keycloak.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.keycloak.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y openjdk-17-jdk wget",
      
      # Download Keycloak
      "wget https://github.com/keycloak/keycloak/releases/download/23.0.0/keycloak-23.0.0.tar.gz",
      "tar -xzf keycloak-23.0.0.tar.gz",
      "sudo mv keycloak-23.0.0 /opt/keycloak",
      
      # Create Keycloak user
      "sudo groupadd keycloak",
      "sudo useradd -r -g keycloak -d /opt/keycloak -s /sbin/nologin keycloak",
      "sudo chown -R keycloak:keycloak /opt/keycloak",
      
      # Configure Keycloak
      "sudo -u keycloak /opt/keycloak/bin/kc.sh build",
      
      # Create systemd service
      "cat <<EOF | sudo tee /etc/systemd/system/keycloak.service",
      "[Unit]",
      "Description=Keycloak",
      "After=network.target",
      "",
      "[Service]",
      "Type=idle",
      "User=keycloak",
      "Group=keycloak",
      "ExecStart=/opt/keycloak/bin/kc.sh start --optimized --hostname=keycloak.${var.domain}",
      "TimeoutStartSec=600",
      "TimeoutStopSec=600",
      "",
      "[Install]",
      "WantedBy=multi-user.target",
      "EOF",
      
      # Start Keycloak
      "sudo systemctl daemon-reload",
      "sudo systemctl enable keycloak",
      "sudo systemctl start keycloak"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Outputs
output "service_ips" {
  value = {
    gitlab   = proxmox_vm_qemu.gitlab.default_ipv4_address
    harbor   = proxmox_vm_qemu.harbor.default_ipv4_address
    vault    = proxmox_vm_qemu.vault.default_ipv4_address
    keycloak = proxmox_vm_qemu.keycloak.default_ipv4_address
  }
  description = "IP addresses of core services"
}

output "gitlab_url" {
  value       = "https://gitlab.${var.domain}"
  description = "GitLab URL"
}

output "harbor_url" {
  value       = "https://registry.${var.domain}"
  description = "Harbor Registry URL"
}

output "vault_url" {
  value       = "http://vault.${var.domain}:8200"
  description = "Vault URL"
}

output "keycloak_url" {
  value       = "https://keycloak.${var.domain}"
  description = "Keycloak URL"
}