# Monitoring Module - Observability stack deployment

terraform {
  required_providers {
    proxmox = {
      source = "Telmate/proxmox"
      version = ">=2.9.0"
    }
  }
}

# Variables
variable "services" {
  description = "Map of monitoring service configurations"
  type = map(object({
    pool           = string
    cores          = number
    memory         = number
    disk           = number
    network_bridge = string
  }))
}

variable "proxmox_node" {
  description = "Proxmox node name"
  type        = string
  default     = "pve"
}

variable "template_name" {
  description = "Name of the VM template to clone"
  type        = string
  default     = "ubuntu-2204-cloudinit"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
  default     = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAEOYIc0SgIoBpSD9QNdKwRXCDmaAVdptNoixjZhxZ/x alias-dan@proxmox-access"
}

variable "domain" {
  description = "Base domain for services"
  type        = string
  default     = "alias.local"
}

# VM ID mapping
locals {
  vm_ids = {
    prometheus    = 330
    grafana       = 331
    elasticsearch = 332
    kibana        = 333
  }
}

# Prometheus VM
resource "proxmox_vm_qemu" "prometheus" {
  name        = "prometheus"
  vmid        = local.vm_ids.prometheus
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.prometheus.pool

  cores    = var.services.prometheus.cores
  memory   = var.services.prometheus.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.prometheus.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.prometheus.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y wget",
      
      # Create prometheus user
      "sudo useradd --no-create-home --shell /bin/false prometheus",
      "sudo mkdir -p /etc/prometheus /var/lib/prometheus",
      "sudo chown prometheus:prometheus /etc/prometheus /var/lib/prometheus",
      
      # Download and install Prometheus
      "wget https://github.com/prometheus/prometheus/releases/download/v2.48.0/prometheus-2.48.0.linux-amd64.tar.gz",
      "tar xvf prometheus-2.48.0.linux-amd64.tar.gz",
      "sudo cp prometheus-2.48.0.linux-amd64/prometheus /usr/local/bin/",
      "sudo cp prometheus-2.48.0.linux-amd64/promtool /usr/local/bin/",
      "sudo cp -r prometheus-2.48.0.linux-amd64/consoles /etc/prometheus",
      "sudo cp -r prometheus-2.48.0.linux-amd64/console_libraries /etc/prometheus",
      "sudo chown -R prometheus:prometheus /etc/prometheus",
      
      # Create Prometheus configuration
      "cat <<EOF | sudo tee /etc/prometheus/prometheus.yml",
      "global:",
      "  scrape_interval: 15s",
      "  evaluation_interval: 15s",
      "",
      "alerting:",
      "  alertmanagers:",
      "    - static_configs:",
      "        - targets: []",
      "",
      "rule_files:",
      "  - /etc/prometheus/rules/*.yml",
      "",
      "scrape_configs:",
      "  - job_name: 'prometheus'",
      "    static_configs:",
      "      - targets: ['localhost:9090']",
      "",
      "  - job_name: 'node_exporter'",
      "    static_configs:",
      "      - targets:",
      "          - '*************:9100'",
      "",
      "  - job_name: 'kubernetes'",
      "    kubernetes_sd_configs:",
      "      - role: node",
      "    relabel_configs:",
      "      - source_labels: [__address__]",
      "        regex: '([^:]+)(?::[0-9]+)?'",
      "        replacement: '$1:9100'",
      "        target_label: __address__",
      "EOF",
      
      # Create systemd service
      "cat <<EOF | sudo tee /etc/systemd/system/prometheus.service",
      "[Unit]",
      "Description=Prometheus",
      "Wants=network-online.target",
      "After=network-online.target",
      "",
      "[Service]",
      "User=prometheus",
      "Group=prometheus",
      "Type=simple",
      "ExecStart=/usr/local/bin/prometheus \\\\",
      "    --config.file /etc/prometheus/prometheus.yml \\\\",
      "    --storage.tsdb.path /var/lib/prometheus/ \\\\",
      "    --web.console.templates=/etc/prometheus/consoles \\\\",
      "    --web.console.libraries=/etc/prometheus/console_libraries",
      "",
      "[Install]",
      "WantedBy=multi-user.target",
      "EOF",
      
      # Install Node Exporter on this VM
      "wget https://github.com/prometheus/node_exporter/releases/download/v1.7.0/node_exporter-1.7.0.linux-amd64.tar.gz",
      "tar xvf node_exporter-1.7.0.linux-amd64.tar.gz",
      "sudo cp node_exporter-1.7.0.linux-amd64/node_exporter /usr/local/bin/",
      "sudo useradd --no-create-home --shell /bin/false node_exporter",
      
      # Node Exporter service
      "cat <<EOF | sudo tee /etc/systemd/system/node_exporter.service",
      "[Unit]",
      "Description=Node Exporter",
      "Wants=network-online.target",
      "After=network-online.target",
      "",
      "[Service]",
      "User=node_exporter",
      "Group=node_exporter",
      "Type=simple",
      "ExecStart=/usr/local/bin/node_exporter",
      "",
      "[Install]",
      "WantedBy=multi-user.target",
      "EOF",
      
      # Start services
      "sudo systemctl daemon-reload",
      "sudo systemctl enable prometheus node_exporter",
      "sudo systemctl start prometheus node_exporter"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Grafana VM
resource "proxmox_vm_qemu" "grafana" {
  name        = "grafana"
  vmid        = local.vm_ids.grafana
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.grafana.pool

  cores    = var.services.grafana.cores
  memory   = var.services.grafana.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.grafana.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.grafana.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y software-properties-common wget",
      
      # Add Grafana repository
      "sudo mkdir -p /etc/apt/keyrings/",
      "wget -q -O - https://apt.grafana.com/gpg.key | gpg --dearmor | sudo tee /etc/apt/keyrings/grafana.gpg > /dev/null",
      "echo 'deb [signed-by=/etc/apt/keyrings/grafana.gpg] https://apt.grafana.com stable main' | sudo tee /etc/apt/sources.list.d/grafana.list",
      
      # Install Grafana
      "sudo apt-get update",
      "sudo apt-get install -y grafana",
      
      # Configure Grafana
      "sudo sed -i 's/;domain = localhost/domain = grafana.${var.domain}/' /etc/grafana/grafana.ini",
      "sudo sed -i 's/;root_url = %(protocol)s:\\/\\/%(domain)s:%(http_port)s\\//root_url = https:\\/\\/grafana.${var.domain}/' /etc/grafana/grafana.ini",
      
      # Start Grafana
      "sudo systemctl daemon-reload",
      "sudo systemctl enable grafana-server",
      "sudo systemctl start grafana-server",
      
      # Wait for Grafana to start
      "sleep 10",
      
      # Add Prometheus data source
      "curl -X POST -H 'Content-Type: application/json' -d '{",
      "  \"name\": \"Prometheus\",",
      "  \"type\": \"prometheus\",",
      "  \"url\": \"http://prometheus.${var.domain}:9090\",",
      "  \"access\": \"proxy\",",
      "  \"isDefault\": true",
      "}' *********************************/api/datasources"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Elasticsearch VM
resource "proxmox_vm_qemu" "elasticsearch" {
  name        = "elasticsearch"
  vmid        = local.vm_ids.elasticsearch
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.elasticsearch.pool

  cores    = var.services.elasticsearch.cores
  memory   = var.services.elasticsearch.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.elasticsearch.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.elasticsearch.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y wget apt-transport-https",
      
      # Add Elasticsearch repository
      "wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo gpg --dearmor -o /usr/share/keyrings/elasticsearch-keyring.gpg",
      "echo 'deb [signed-by=/usr/share/keyrings/elasticsearch-keyring.gpg] https://artifacts.elastic.co/packages/8.x/apt stable main' | sudo tee /etc/apt/sources.list.d/elastic-8.x.list",
      
      # Install Elasticsearch
      "sudo apt-get update",
      "sudo apt-get install -y elasticsearch",
      
      # Configure Elasticsearch
      "sudo sed -i 's/#network.host: ***********/network.host: 0.0.0.0/' /etc/elasticsearch/elasticsearch.yml",
      "sudo sed -i 's/#http.port: 9200/http.port: 9200/' /etc/elasticsearch/elasticsearch.yml",
      "echo 'discovery.type: single-node' | sudo tee -a /etc/elasticsearch/elasticsearch.yml",
      "echo 'xpack.security.enabled: false' | sudo tee -a /etc/elasticsearch/elasticsearch.yml",
      
      # Increase vm.max_map_count
      "echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf",
      "sudo sysctl -p",
      
      # Start Elasticsearch
      "sudo systemctl daemon-reload",
      "sudo systemctl enable elasticsearch",
      "sudo systemctl start elasticsearch"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Kibana VM
resource "proxmox_vm_qemu" "kibana" {
  name        = "kibana"
  vmid        = local.vm_ids.kibana
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = var.services.kibana.pool
  
  depends_on = [proxmox_vm_qemu.elasticsearch]

  cores    = var.services.kibana.cores
  memory   = var.services.kibana.memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "${var.services.kibana.disk}G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = var.services.kibana.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  sshkeys   = var.ssh_public_key

  provisioner "remote-exec" {
    inline = [
      "cloud-init status --wait",
      "sudo apt-get update",
      "sudo apt-get install -y wget apt-transport-https",
      
      # Add Elastic repository
      "wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo gpg --dearmor -o /usr/share/keyrings/elasticsearch-keyring.gpg",
      "echo 'deb [signed-by=/usr/share/keyrings/elasticsearch-keyring.gpg] https://artifacts.elastic.co/packages/8.x/apt stable main' | sudo tee /etc/apt/sources.list.d/elastic-8.x.list",
      
      # Install Kibana
      "sudo apt-get update",
      "sudo apt-get install -y kibana",
      
      # Configure Kibana
      "sudo sed -i 's/#server.port: 5601/server.port: 5601/' /etc/kibana/kibana.yml",
      "sudo sed -i 's/#server.host: \"localhost\"/server.host: \"0.0.0.0\"/' /etc/kibana/kibana.yml",
      "sudo sed -i 's/#elasticsearch.hosts:/elasticsearch.hosts:/' /etc/kibana/kibana.yml",
      "sudo sed -i '/elasticsearch.hosts:/a\\ \\ - \"http://elasticsearch.${var.domain}:9200\"' /etc/kibana/kibana.yml",
      
      # Start Kibana
      "sudo systemctl daemon-reload",
      "sudo systemctl enable kibana",
      "sudo systemctl start kibana"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Create Alert Manager configuration
resource "null_resource" "alertmanager_config" {
  depends_on = [proxmox_vm_qemu.prometheus]

  provisioner "remote-exec" {
    inline = [
      # Create alerts directory
      "sudo mkdir -p /etc/prometheus/rules",
      
      # Create infrastructure alerts
      "cat <<EOF | sudo tee /etc/prometheus/rules/infrastructure.yml",
      "groups:",
      "  - name: infrastructure",
      "    interval: 30s",
      "    rules:",
      "      - alert: HighCPUUsage",
      "        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100) > 80",
      "        for: 5m",
      "        labels:",
      "          severity: warning",
      "        annotations:",
      "          summary: \"High CPU usage on {{ $labels.instance }}\"",
      "          description: \"CPU usage is above 80% (current value: {{ $value }}%)\"",
      "",
      "      - alert: HighMemoryUsage",
      "        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85",
      "        for: 5m",
      "        labels:",
      "          severity: warning",
      "        annotations:",
      "          summary: \"High memory usage on {{ $labels.instance }}\"",
      "          description: \"Memory usage is above 85% (current value: {{ $value }}%)\"",
      "",
      "      - alert: DiskSpaceLow",
      "        expr: (node_filesystem_avail_bytes{fstype!~\"tmpfs|fuse.lxcfs|squashfs\"} / node_filesystem_size_bytes) * 100 < 15",
      "        for: 10m",
      "        labels:",
      "          severity: critical",
      "        annotations:",
      "          summary: \"Low disk space on {{ $labels.instance }}\"",
      "          description: \"Disk space is below 15% on {{ $labels.device }} (current value: {{ $value }}%)\"",
      "EOF",
      
      # Reload Prometheus configuration
      "curl -X POST http://localhost:9090/-/reload"
    ]
    
    connection {
      type        = "ssh"
      user        = "ubuntu"
      host        = proxmox_vm_qemu.prometheus.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }
}

# Outputs
output "monitoring_ips" {
  value = {
    prometheus    = proxmox_vm_qemu.prometheus.default_ipv4_address
    grafana       = proxmox_vm_qemu.grafana.default_ipv4_address
    elasticsearch = proxmox_vm_qemu.elasticsearch.default_ipv4_address
    kibana        = proxmox_vm_qemu.kibana.default_ipv4_address
  }
  description = "IP addresses of monitoring services"
}

output "prometheus_url" {
  value       = "http://prometheus.${var.domain}:9090"
  description = "Prometheus URL"
}

output "grafana_url" {
  value       = "http://grafana.${var.domain}:3000"
  description = "Grafana URL (default: admin/admin)"
}

output "elasticsearch_url" {
  value       = "http://elasticsearch.${var.domain}:9200"
  description = "Elasticsearch URL"
}

output "kibana_url" {
  value       = "http://kibana.${var.domain}:5601"
  description = "Kibana URL"
}