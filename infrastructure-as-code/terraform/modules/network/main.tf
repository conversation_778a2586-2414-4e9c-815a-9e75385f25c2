# Network Module - VLAN and Bridge Configuration for Proxmox

terraform {
  required_providers {
    proxmox = {
      source = "Telmate/proxmox"
      version = ">=2.9.0"
    }
  }
}

# Variables
variable "vlans" {
  description = "Map of VLAN configurations"
  type = map(object({
    id   = number
    cidr = string
  }))
}

variable "proxmox_node" {
  description = "Proxmox node name"
  type        = string
  default     = "pve"
}

# Local values for network configuration
locals {
  network_config = {
    for name, vlan in var.vlans : name => {
      vlan_id     = vlan.id
      cidr        = vlan.cidr
      gateway     = cidrhost(vlan.cidr, 1)
      netmask     = split("/", vlan.cidr)[1]
      bridge_name = "vmbr${vlan.id}"
    }
  }
}

# Resource for network configuration file
resource "null_resource" "network_config" {
  for_each = local.network_config

  provisioner "remote-exec" {
    inline = [
      # Create network configuration
      "cat >> /etc/network/interfaces.d/${each.key}.conf <<EOF",
      "# ${each.key} network - VLAN ${each.value.vlan_id}",
      "auto ${each.value.bridge_name}",
      "iface ${each.value.bridge_name} inet static",
      "    address ${each.value.gateway}/${each.value.netmask}",
      "    bridge-ports eno1.${each.value.vlan_id}",
      "    bridge-stp off",
      "    bridge-fd 0",
      "    bridge-vlan-aware yes",
      "    bridge-vids 2-4094",
      "EOF"
    ]
  }

  connection {
    type     = "ssh"
    user     = "root"
    host     = "*************"
    private_key = file("~/.ssh/id_ed25519")
  }
}

# Firewall rules for each network
resource "null_resource" "firewall_rules" {
  depends_on = [null_resource.network_config]

  provisioner "remote-exec" {
    inline = [
      # Enable Proxmox firewall
      "systemctl enable pve-firewall",
      "systemctl start pve-firewall",
      
      # Create security group for each VLAN
      "cat > /etc/pve/firewall/cluster.fw <<'EOF'",
      "[OPTIONS]",
      "enable: 1",
      "policy_in: DROP",
      "policy_out: ACCEPT",
      "",
      "# Management Network",
      "[IPSET management]",
      "${local.network_config["management"].cidr}",
      "",
      "# Production Network",
      "[IPSET production]",
      "${local.network_config["production"].cidr}",
      "",
      "# Staging Network",
      "[IPSET staging]",
      "${local.network_config["staging"].cidr}",
      "",
      "# Development Network",
      "[IPSET development]",
      "${local.network_config["development"].cidr}",
      "",
      "# DMZ Network",
      "[IPSET dmz]",
      "${local.network_config["dmz"].cidr}",
      "",
      "# Storage Network",
      "[IPSET storage]",
      "${local.network_config["storage"].cidr}",
      "",
      "# Monitoring Network",
      "[IPSET monitoring]",
      "${local.network_config["monitoring"].cidr}",
      "",
      "[RULES]",
      "# Allow management access to Proxmox",
      "IN ACCEPT -source +management -p tcp -dport 8006 -log nolog",
      "IN ACCEPT -source +management -p tcp -dport 22 -log nolog",
      "",
      "# Allow inter-VLAN routing where needed",
      "IN ACCEPT -source +production -dest +storage -p tcp -log nolog",
      "IN ACCEPT -source +staging -dest +storage -p tcp -log nolog",
      "IN ACCEPT -source +development -dest +storage -p tcp -log nolog",
      "",
      "# Allow monitoring access",
      "IN ACCEPT -source +monitoring -p tcp -log nolog",
      "",
      "# DMZ restrictions",
      "IN DROP -source +dmz -dest +production -log nolog",
      "IN DROP -source +dmz -dest +management -log nolog",
      "EOF"
    ]
  }

  connection {
    type     = "ssh"
    user     = "root"
    host     = "*************"
    private_key = file("~/.ssh/id_ed25519")
  }
}

# DNS configuration for each network
resource "null_resource" "dns_config" {
  depends_on = [null_resource.network_config]

  provisioner "remote-exec" {
    inline = [
      # Install dnsmasq for network-specific DNS
      "apt-get update && apt-get install -y dnsmasq",
      
      # Configure dnsmasq for each network
      "cat > /etc/dnsmasq.d/vlan-dns.conf <<'EOF'",
      "# DNS configuration for VLANs",
      "interface=${join(",", [for k, v in local.network_config : v.bridge_name])}",
      "bind-interfaces",
      "",
      "# Domain names per VLAN",
      "domain=management.alias.local,${local.network_config["management"].cidr}",
      "domain=production.alias.local,${local.network_config["production"].cidr}",
      "domain=staging.alias.local,${local.network_config["staging"].cidr}",
      "domain=development.alias.local,${local.network_config["development"].cidr}",
      "",
      "# DHCP ranges (optional)",
      "dhcp-range=${cidrhost(local.network_config["development"].cidr, 100)},${cidrhost(local.network_config["development"].cidr, 200)},12h",
      "dhcp-range=${cidrhost(local.network_config["guest"].cidr, 100)},${cidrhost(local.network_config["guest"].cidr, 200)},4h",
      "EOF",
      
      # Restart dnsmasq
      "systemctl restart dnsmasq"
    ]
  }

  connection {
    type     = "ssh"
    user     = "root"
    host     = "*************"
    private_key = file("~/.ssh/id_ed25519")
  }
}

# Outputs
output "network_bridges" {
  value = {
    for name, config in local.network_config : name => {
      bridge  = config.bridge_name
      gateway = config.gateway
      cidr    = config.cidr
      vlan_id = config.vlan_id
    }
  }
  description = "Network bridge configuration for each VLAN"
}

output "network_gateways" {
  value = {
    for name, config in local.network_config : name => config.gateway
  }
  description = "Gateway IP addresses for each network"
}