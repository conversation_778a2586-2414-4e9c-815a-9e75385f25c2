# Kubernetes Module - Deploy K8s/K3s clusters on Proxmox

terraform {
  required_providers {
    proxmox = {
      source = "Telmate/proxmox"
      version = ">=2.9.0"
    }
  }
}

# Variables
variable "clusters" {
  description = "Map of Kubernetes cluster configurations"
  type = map(object({
    pool           = string
    masters        = number
    workers        = number
    master_cores   = number
    master_memory  = number
    worker_cores   = number
    worker_memory  = number
    network_bridge = string
    storage        = string
  }))
}

variable "proxmox_node" {
  description = "Proxmox node name"
  type        = string
  default     = "pve"
}

variable "template_name" {
  description = "Name of the VM template to clone"
  type        = string
  default     = "ubuntu-2204-cloudinit"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
  default     = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAEOYIc0SgIoBpSD9QNdKwRXCDmaAVdptNoixjZhxZ/x alias-dan@proxmox-access"
}

# Cloud-init user data template
locals {
  master_userdata = templatefile("${path.module}/templates/master-cloud-init.yaml", {
    ssh_public_key = var.ssh_public_key
  })
  
  worker_userdata = templatefile("${path.module}/templates/worker-cloud-init.yaml", {
    ssh_public_key = var.ssh_public_key
  })
}

# Master nodes
resource "proxmox_vm_qemu" "k8s_masters" {
  for_each = {
    for item in flatten([
      for cluster_name, cluster in var.clusters : [
        for i in range(cluster.masters) : {
          cluster_name = cluster_name
          cluster      = cluster
          index        = i + 1
          vm_id        = cluster_name == "production" ? 300 + i + 1 : 
                        cluster_name == "staging" ? 400 + i + 1 : 200 + i + 1
        }
      ]
    ]) : "${item.cluster_name}-master-${item.index}" => item
  }

  name        = "k8s-${each.value.cluster_name}-master-${each.value.index}"
  vmid        = each.value.vm_id
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = each.value.cluster.pool

  cores    = each.value.cluster.master_cores
  memory   = each.value.cluster.master_memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "100G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = each.value.cluster.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  
  sshkeys = var.ssh_public_key
  
  # Custom cloud-init via cicustom
  cicustom = "user=local:snippets/k8s-${each.value.cluster_name}-master-${each.value.index}.yaml"

  provisioner "remote-exec" {
    inline = [
      # Wait for cloud-init to complete
      "cloud-init status --wait",
      
      # Install Kubernetes based on environment
      each.value.cluster_name == "development" ? 
        "curl -sfL https://get.k3s.io | sh -s - server --cluster-init" :
        "curl -sfL https://get.rke2.io | sh -s - server --cluster-init",
      
      # Enable and start service
      each.value.cluster_name == "development" ?
        "systemctl enable k3s && systemctl start k3s" :
        "systemctl enable rke2-server && systemctl start rke2-server",
      
      # Wait for service to be ready
      "sleep 30",
      
      # Get join token
      each.value.cluster_name == "development" ?
        "cat /var/lib/rancher/k3s/server/node-token" :
        "cat /var/lib/rancher/rke2/server/node-token"
    ]
    
    connection {
      type     = "ssh"
      user     = "ubuntu"
      host     = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }

  lifecycle {
    ignore_changes = [cicustom]
  }
}

# Worker nodes
resource "proxmox_vm_qemu" "k8s_workers" {
  for_each = {
    for item in flatten([
      for cluster_name, cluster in var.clusters : [
        for i in range(cluster.workers) : {
          cluster_name = cluster_name
          cluster      = cluster
          index        = i + 1
          vm_id        = cluster_name == "production" ? 310 + i + 1 : 
                        cluster_name == "staging" ? 410 + i + 1 : 210 + i + 1
        }
      ]
    ]) : "${item.cluster_name}-worker-${item.index}" => item
  }

  depends_on = [proxmox_vm_qemu.k8s_masters]

  name        = "k8s-${each.value.cluster_name}-worker-${each.value.index}"
  vmid        = each.value.vm_id
  target_node = var.proxmox_node
  clone       = var.template_name
  pool        = each.value.cluster.pool

  cores    = each.value.cluster.worker_cores
  memory   = each.value.cluster.worker_memory
  scsihw   = "virtio-scsi-pci"
  bootdisk = "scsi0"
  
  disk {
    slot    = 0
    size    = "200G"
    type    = "scsi"
    storage = "local-lvm"
    iothread = 1
  }

  network {
    model  = "virtio"
    bridge = each.value.cluster.network_bridge
  }

  # Cloud-init configuration
  os_type   = "cloud-init"
  ipconfig0 = "ip=dhcp"
  ciuser    = "ubuntu"
  
  sshkeys = var.ssh_public_key
  
  # Custom cloud-init via cicustom
  cicustom = "user=local:snippets/k8s-${each.value.cluster_name}-worker-${each.value.index}.yaml"

  provisioner "remote-exec" {
    inline = [
      # Wait for cloud-init to complete
      "cloud-init status --wait",
      
      # Get master IP (simplified - in production use proper service discovery)
      "MASTER_IP=$(getent hosts k8s-${each.value.cluster_name}-master-1 | awk '{ print $1 }')",
      
      # Join cluster based on environment
      each.value.cluster_name == "development" ? 
        "curl -sfL https://get.k3s.io | K3S_URL=https://$MASTER_IP:6443 K3S_TOKEN=$(ssh ubuntu@$MASTER_IP 'sudo cat /var/lib/rancher/k3s/server/node-token') sh -" :
        "curl -sfL https://get.rke2.io | INSTALL_RKE2_TYPE=agent sh -s - server https://$MASTER_IP:9345",
      
      # Enable and start service
      each.value.cluster_name == "development" ?
        "systemctl enable k3s-agent && systemctl start k3s-agent" :
        "systemctl enable rke2-agent && systemctl start rke2-agent"
    ]
    
    connection {
      type     = "ssh"
      user     = "ubuntu"
      host     = self.default_ipv4_address
      private_key = file("~/.ssh/id_ed25519")
    }
  }

  lifecycle {
    ignore_changes = [cicustom]
  }
}

# Create kubeconfig files
resource "null_resource" "kubeconfig" {
  for_each = var.clusters
  
  depends_on = [proxmox_vm_qemu.k8s_masters]

  provisioner "local-exec" {
    command = <<-EOT
      # Get kubeconfig from master
      MASTER_IP=$(ssh root@************* "qm guest cmd ${each.key == "production" ? 301 : each.key == "staging" ? 401 : 201} network-get-interfaces" | jq -r '.[] | select(.name == "eth0") | ."ip-addresses"[] | select(."ip-address-type" == "ipv4") | ."ip-address"')
      
      # Copy kubeconfig
      ssh ubuntu@$MASTER_IP "sudo cat /etc/rancher/${each.key == "development" ? "k3s/k3s" : "rke2/rke2"}.yaml" > ~/.kube/config-${each.key}
      
      # Update server address
      sed -i "s/127.0.0.1/$MASTER_IP/g" ~/.kube/config-${each.key}
    EOT
  }
}

# Outputs
output "cluster_ips" {
  value = {
    masters = {
      for k, v in proxmox_vm_qemu.k8s_masters : k => v.default_ipv4_address
    }
    workers = {
      for k, v in proxmox_vm_qemu.k8s_workers : k => v.default_ipv4_address
    }
  }
  description = "IP addresses of all Kubernetes nodes"
}

output "kubeconfig_paths" {
  value = {
    for cluster_name, _ in var.clusters : cluster_name => "~/.kube/config-${cluster_name}"
  }
  description = "Paths to kubeconfig files for each cluster"
}