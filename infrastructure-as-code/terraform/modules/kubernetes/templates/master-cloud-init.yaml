#cloud-config
users:
  - name: ubuntu
    groups: sudo
    shell: /bin/bash
    sudo: ALL=(ALL) NOPASSWD:ALL
    ssh_authorized_keys:
      - ${ssh_public_key}

packages:
  - curl
  - wget
  - apt-transport-https
  - ca-certificates
  - software-properties-common
  - gnupg
  - lsb-release
  - nfs-common
  - open-iscsi
  - jq

write_files:
  - path: /etc/sysctl.d/99-kubernetes.conf
    content: |
      net.bridge.bridge-nf-call-iptables = 1
      net.bridge.bridge-nf-call-ip6tables = 1
      net.ipv4.ip_forward = 1
      vm.swappiness = 0
      vm.overcommit_memory = 1
      kernel.panic = 10
      kernel.panic_on_oops = 1

  - path: /etc/modules-load.d/k8s.conf
    content: |
      overlay
      br_netfilter

runcmd:
  # Load kernel modules
  - modprobe overlay
  - modprobe br_netfilter
  - sysctl --system
  
  # Disable swap
  - swapoff -a
  - sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab
  
  # Set hostname
  - hostnamectl set-hostname $(hostname -s)
  
  # Update system
  - apt-get update
  - apt-get upgrade -y
  
  # Configure container runtime
  - mkdir -p /etc/containerd
  - containerd config default | tee /etc/containerd/config.toml
  - sed -i 's/SystemdCgroup = false/SystemdCgroup = true/g' /etc/containerd/config.toml
  - systemctl restart containerd
  
  # Wait for network
  - sleep 10

final_message: "Kubernetes master node initialization complete"