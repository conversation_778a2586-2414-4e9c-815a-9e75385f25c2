# ALIAS Infrastructure as Code - Proxmox Provider Configuration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    proxmox = {
      source = "Telmate/proxmox"
      version = ">=2.9.0"
    }
  }
  
  backend "s3" {
    bucket = "alias-terraform-state"
    key    = "infrastructure/proxmox/terraform.tfstate"
    region = "us-east-1"
    encrypt = true
  }
}

# Proxmox Provider Configuration
provider "proxmox" {
  pm_api_url      = "https://*************:8006/api2/json"
  pm_api_token_id = var.proxmox_token_id
  pm_api_token_secret = var.proxmox_token_secret
  pm_tls_insecure = true
  pm_parallel     = 10
  pm_timeout      = 600
}

# Variables
variable "proxmox_token_id" {
  description = "Proxmox API Token ID"
  type        = string
  default     = "root@pam!terraform"
}

variable "proxmox_token_secret" {
  description = "Proxmox API Token Secret"
  type        = string
  sensitive   = true
}

# Resource Pools
resource "proxmox_pool" "production" {
  poolid = "production"
  comment = "Production Environment Resources"
}

resource "proxmox_pool" "staging" {
  poolid = "staging"
  comment = "Staging Environment Resources"
}

resource "proxmox_pool" "development" {
  poolid = "development"
  comment = "Development Environment Resources"
}

# Network Configuration Module
module "network" {
  source = "./modules/network"
  
  vlans = {
    management = { id = 10, cidr = "**********/24" }
    production = { id = 20, cidr = "**********/24" }
    staging    = { id = 30, cidr = "**********/24" }
    development = { id = 40, cidr = "**********/24" }
    dmz        = { id = 50, cidr = "**********/24" }
    storage    = { id = 60, cidr = "**********/24" }
    monitoring = { id = 70, cidr = "**********/24" }
    guest      = { id = 90, cidr = "**********/24" }
  }
}

# Kubernetes Clusters Module
module "kubernetes" {
  source = "./modules/kubernetes"
  
  clusters = {
    production = {
      pool = proxmox_pool.production.poolid
      masters = 3
      workers = 3
      master_cores = 4
      master_memory = 16384
      worker_cores = 8
      worker_memory = 32768
      network_bridge = "vmbr20"
      storage = "production"
    }
    staging = {
      pool = proxmox_pool.staging.poolid
      masters = 1
      workers = 2
      master_cores = 4
      master_memory = 8192
      worker_cores = 4
      worker_memory = 16384
      network_bridge = "vmbr30"
      storage = "staging"
    }
    development = {
      pool = proxmox_pool.development.poolid
      masters = 1
      workers = 1
      master_cores = 2
      master_memory = 4096
      worker_cores = 4
      worker_memory = 8192
      network_bridge = "vmbr40"
      storage = "development"
    }
  }
}

# Core Services Module
module "core_services" {
  source = "./modules/core-services"
  
  services = {
    gitlab = {
      pool = proxmox_pool.production.poolid
      cores = 8
      memory = 16384
      disk = 500
      network_bridge = "vmbr20"
    }
    harbor = {
      pool = proxmox_pool.production.poolid
      cores = 4
      memory = 8192
      disk = 1000
      network_bridge = "vmbr20"
    }
    vault = {
      pool = proxmox_pool.production.poolid
      cores = 2
      memory = 4096
      disk = 50
      network_bridge = "vmbr20"
    }
    keycloak = {
      pool = proxmox_pool.production.poolid
      cores = 2
      memory = 4096
      disk = 50
      network_bridge = "vmbr20"
    }
  }
}

# Monitoring Stack Module
module "monitoring" {
  source = "./modules/monitoring"
  
  services = {
    prometheus = {
      pool = proxmox_pool.production.poolid
      cores = 2
      memory = 4096
      disk = 100
      network_bridge = "vmbr70"
    }
    grafana = {
      pool = proxmox_pool.production.poolid
      cores = 2
      memory = 4096
      disk = 50
      network_bridge = "vmbr70"
    }
    elasticsearch = {
      pool = proxmox_pool.production.poolid
      cores = 4
      memory = 16384
      disk = 500
      network_bridge = "vmbr70"
    }
    kibana = {
      pool = proxmox_pool.production.poolid
      cores = 2
      memory = 4096
      disk = 50
      network_bridge = "vmbr70"
    }
  }
}

# Outputs
output "kubernetes_clusters" {
  value = module.kubernetes.cluster_ips
  description = "IP addresses of Kubernetes cluster nodes"
}

output "service_endpoints" {
  value = {
    gitlab = module.core_services.gitlab_url
    harbor = module.core_services.harbor_url
    vault = module.core_services.vault_url
    keycloak = module.core_services.keycloak_url
    grafana = module.monitoring.grafana_url
    kibana = module.monitoring.kibana_url
  }
  description = "Service endpoint URLs"
}