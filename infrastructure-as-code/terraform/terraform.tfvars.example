# ALIAS Infrastructure Terraform Variables
# Copy this file to terraform.tfvars and update with your actual values

# Proxmox API credentials
proxmox_token_id     = "root@pam!terraform"
proxmox_token_secret = "your-actual-token-secret-here"

# Optional: Override default values
# domain = "alias.local"
# proxmox_node = "pve"
# template_name = "ubuntu-2204-cloudinit"

# SSH public key for VM access
# ssh_public_key = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAEOYIc0SgIoBpSD9QNdKwRXCDmaAVdptNoixjZhxZ/x alias-dan@proxmox-access"