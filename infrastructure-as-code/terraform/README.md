# ALIAS Infrastructure as Code - Terraform

This directory contains Terraform configuration for deploying the complete ALIAS infrastructure on Proxmox.

## Prerequisites

1. **Proxmox Server**
   - Proxmox VE 8.4+ installed at *************
   - Sufficient resources (128GB RAM, 28 CPU cores)
   - Network VLANs configured on physical switch

2. **Terraform**
   ```bash
   # Install Terraform
   wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
   unzip terraform_1.6.0_linux_amd64.zip
   sudo mv terraform /usr/local/bin/
   ```

3. **Proxmox API Token**
   ```bash
   # Create API token on Proxmox
   pveum user token add root@pam terraform -privsep 0
   # Save the token secret - it's only shown once!
   ```

4. **VM Template**
   ```bash
   # Download Ubuntu Cloud Image
   wget https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img
   
   # Create template VM
   qm create 9000 --name ubuntu-2204-cloudinit --memory 2048 --cores 2 --net0 virtio,bridge=vmbr0
   qm importdisk 9000 jammy-server-cloudimg-amd64.img local-lvm
   qm set 9000 --scsihw virtio-scsi-pci --scsi0 local-lvm:vm-9000-disk-0
   qm set 9000 --boot c --bootdisk scsi0
   qm set 9000 --ide2 local-lvm:cloudinit
   qm set 9000 --serial0 socket --vga serial0
   qm set 9000 --agent enabled=1
   qm template 9000
   ```

## Usage

### 1. Configure Variables

```bash
# Copy example variables file
cp terraform.tfvars.example terraform.tfvars

# Edit with your values
vim terraform.tfvars
```

### 2. Initialize Terraform

```bash
terraform init
```

### 3. Plan Deployment

```bash
# Review what will be created
terraform plan
```

### 4. Deploy Infrastructure

```bash
# Deploy everything
terraform apply

# Or deploy specific modules
terraform apply -target=module.network
terraform apply -target=module.kubernetes
terraform apply -target=module.core_services
terraform apply -target=module.monitoring
```

## Module Structure

### Network Module
- Creates VLANs and network bridges
- Configures firewall rules
- Sets up DNS for each network

### Kubernetes Module
- Deploys three Kubernetes clusters:
  - Production: 3 masters + 3 workers (RKE2)
  - Staging: 1 master + 2 workers (RKE2)
  - Development: 1 all-in-one node (K3s)
- Configures cloud-init for automated setup
- Generates kubeconfig files

### Core Services Module
- **GitLab**: Complete CI/CD platform
- **Harbor**: Enterprise container registry
- **Vault**: Secrets management
- **Keycloak**: Identity and access management

### Monitoring Module
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **Elasticsearch**: Log storage
- **Kibana**: Log analysis

## Resource Allocation

| Service | CPU | RAM | Disk | Purpose |
|---------|-----|-----|------|--------|
| **Production K8s** | | | | |
| - Masters (3x) | 4 | 8GB | 100GB | Control plane |
| - Workers (3x) | 8 | 16GB | 200GB | Workloads |
| **Staging K8s** | | | | |
| - Master | 4 | 8GB | 100GB | Control plane |
| - Workers (2x) | 6 | 12GB | 200GB | Workloads |
| **Dev K8s** | | | | |
| - All-in-one | 8 | 16GB | 200GB | Development |
| **Core Services** | | | | |
| - GitLab | 8 | 16GB | 500GB | CI/CD |
| - Harbor | 4 | 8GB | 1TB | Registry |
| - Vault | 2 | 4GB | 50GB | Secrets |
| - Keycloak | 2 | 4GB | 50GB | SSO |
| **Monitoring** | | | | |
| - Prometheus | 2 | 4GB | 100GB | Metrics |
| - Grafana | 2 | 4GB | 50GB | Dashboards |
| - Elasticsearch | 4 | 16GB | 500GB | Logs |
| - Kibana | 2 | 4GB | 50GB | Log UI |

**Total**: 88 vCPUs, 172GB RAM, ~4TB Storage

## Post-Deployment Steps

### 1. Configure DNS

Add these entries to your DNS server or `/etc/hosts`:

```
# Management
**************  proxmox.alias.local

# Production Services
192.168.20.x    gitlab.alias.local
192.168.20.x    registry.alias.local
192.168.20.x    vault.alias.local
192.168.20.x    keycloak.alias.local

# Monitoring
192.168.70.x    prometheus.alias.local
192.168.70.x    grafana.alias.local
192.168.70.x    elasticsearch.alias.local
192.168.70.x    kibana.alias.local
```

### 2. Access Services

- **GitLab**: https://gitlab.alias.local
  - Initial root password in: `sudo gitlab-rake "gitlab:password:reset[root]"`
- **Harbor**: https://registry.alias.local
  - Default: admin/Harbor12345
- **Vault**: http://vault.alias.local:8200
  - Init keys saved in: `/root/vault-init.txt`
- **Keycloak**: https://keycloak.alias.local
  - Create admin user: `/opt/keycloak/bin/kc.sh add-user`
- **Grafana**: http://grafana.alias.local:3000
  - Default: admin/admin
- **Kibana**: http://kibana.alias.local:5601

### 3. Install Kubernetes Tools

```bash
# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Configure kubeconfig
export KUBECONFIG=~/.kube/config-production:~/.kube/config-staging:~/.kube/config-development

# Test connection
kubectl get nodes --context production
kubectl get nodes --context staging
kubectl get nodes --context development
```

### 4. Deploy Ingress Controllers

```bash
# Production cluster
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.9.0/deploy/static/provider/baremetal/deploy.yaml --context production

# Staging cluster
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.9.0/deploy/static/provider/baremetal/deploy.yaml --context staging

# Development cluster (K3s has Traefik by default)
```

### 5. Configure Backup Jobs

```bash
# SSH to Proxmox
ssh root@*************

# Create backup job for all VMs
pvesh create /cluster/backup --storage pbs-backup \
  --schedule "daily 02:00" \
  --vmid "all" \
  --mode snapshot \
  --compress zstd \
  --mailto <EMAIL>
```

## Troubleshooting

### Network Issues
```bash
# Check VLAN configuration
ip link show
bridge vlan show

# Test connectivity between VLANs
ping -I vmbr20 **********
```

### Kubernetes Issues
```bash
# Check node status
ssh ubuntu@<node-ip>
sudo systemctl status k3s  # for dev
sudo systemctl status rke2-server  # for prod/staging masters
sudo systemctl status rke2-agent   # for prod/staging workers

# View logs
journalctl -u k3s -f
journalctl -u rke2-server -f
```

### Service Issues
```bash
# Check service status
ssh ubuntu@<service-ip>
sudo systemctl status <service-name>
journalctl -u <service-name> -f

# GitLab
sudo gitlab-ctl status
sudo gitlab-ctl tail

# Harbor
sudo docker-compose ps
sudo docker-compose logs -f
```

## Maintenance

### Updating Infrastructure
```bash
# Update Terraform modules
terraform get -update

# Plan changes
terraform plan

# Apply updates
terraform apply
```

### Scaling Resources
```bash
# Edit main.tf to adjust resources
vim main.tf

# Apply only the changes
terraform apply -target=module.kubernetes
```

### Destroying Resources
```bash
# Destroy specific module
terraform destroy -target=module.monitoring

# Destroy everything (CAUTION!)
terraform destroy
```

## Security Notes

1. **API Tokens**: Store Terraform API tokens securely
2. **SSL Certificates**: Replace self-signed certs with Let's Encrypt
3. **Firewall Rules**: Review and adjust based on your security policy
4. **Secrets**: Use Vault for all secrets after deployment
5. **Backups**: Test restore procedures regularly

## Next Steps

1. Configure SSL certificates for all services
2. Set up monitoring alerts
3. Configure backup retention policies
4. Implement GitOps with ArgoCD
5. Set up log retention policies
6. Configure RBAC in Kubernetes
7. Set up network policies
8. Implement admission controllers

---

**Note**: This is a complete infrastructure deployment. Ensure you have adequate resources and have tested in a non-production environment first.