# MCP Hub with Inspector Integration

## Overview
Enhanced MCP Hub that includes the official MCP Inspector for each server, providing debugging, monitoring, and inspection capabilities.

## MCP Inspector Integration

### What is MCP Inspector?
The official MCP Inspector is a web-based tool that allows you to:
- View all available tools, resources, and prompts
- Execute tools and see real-time responses
- Monitor MCP protocol messages
- Debug server implementations
- Test server capabilities

### Architecture with Inspector

```
┌─────────────────────────────────────────────────────────────┐
│                     MCP Hub (Container 109)                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌──────────────┐  ┌───────────────┐ │
│  │   Web Catalog   │  │  MCP Proxy   │  │  MCP Registry │ │
│  │   (Port 3100)   │  │ (Port 3101)  │  │   (Storage)   │ │
│  └─────────────────┘  └──────────────┘  └───────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              MCP Server Containers                    │   │
│  │  ┌────────────────────────────────────────────┐     │   │
│  │  │  IT Assistant MCP                          │     │   │
│  │  │  - MCP Server (Port 8000)                  │     │   │
│  │  │  - Inspector UI (Port 8001)                │     │   │
│  │  └────────────────────────────────────────────┘     │   │
│  │  ┌────────────────────────────────────────────┐     │   │
│  │  │  Browser Use MCP                           │     │   │
│  │  │  - MCP Server (Port 8100)                  │     │   │
│  │  │  - Inspector UI (Port 8101)                │     │   │
│  │  └────────────────────────────────────────────┘     │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Enhanced Docker Compose Configuration

```yaml
# docker-compose.yml with Inspector support
version: "3.8"

services:
  # Main MCP Hub
  mcp-hub:
    build: .
    container_name: mcp-hub-server
    restart: unless-stopped
    ports:
      - "3100:3100"  # Web Catalog
      - "3101:3101"  # MCP Proxy
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/mcphub
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=change-this-secret-key
      - INSPECTOR_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - mcp-network

  # IT Assistant MCP with Inspector
  it-assistant:
    build:
      context: ./servers/it-assistant
      dockerfile: Dockerfile.inspector
    container_name: mcp-it-assistant
    restart: unless-stopped
    ports:
      - "8000:8000"  # MCP Server
      - "8001:8001"  # Inspector UI
    environment:
      - MCP_MODE=server
      - INSPECTOR_PORT=8001
      - INSPECTOR_ENABLED=true
    volumes:
      - ./servers/it-assistant/config:/app/config
    networks:
      - mcp-network

  # Browser Use MCP with Inspector
  browser-use:
    build:
      context: ./servers/browser-use
      dockerfile: Dockerfile.inspector
    container_name: mcp-browser-use
    restart: unless-stopped
    ports:
      - "8100:8100"  # MCP Server
      - "8101:8101"  # Inspector UI
    environment:
      - MCP_MODE=server
      - INSPECTOR_PORT=8101
      - INSPECTOR_ENABLED=true
      - DISPLAY=:99
    volumes:
      - ./servers/browser-use/config:/app/config
    networks:
      - mcp-network

  # Add more MCP servers with Inspector...

  postgres:
    image: postgres:15
    container_name: mcp-hub-postgres
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mcp-network

  redis:
    image: redis:7-alpine
    container_name: mcp-hub-redis
    volumes:
      - redis-data:/data
    networks:
      - mcp-network

volumes:
  postgres-data:
  redis-data:

networks:
  mcp-network:
    driver: bridge
```

## Dockerfile with Inspector (Dockerfile.inspector)

```dockerfile
# Base Dockerfile for MCP servers with Inspector
FROM node:20-alpine AS builder

WORKDIR /app

# Install MCP SDK and Inspector
RUN npm install -g @modelcontextprotocol/sdk @modelcontextprotocol/inspector

# Copy server files
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:20-alpine

WORKDIR /app

# Install runtime dependencies
RUN npm install -g @modelcontextprotocol/sdk @modelcontextprotocol/inspector
COPY package*.json ./
RUN npm ci --only=production

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public

# Create Inspector wrapper script
RUN cat > /app/start-with-inspector.sh << 'EOF'
#!/bin/sh

# Start MCP Inspector in background
if [ "$INSPECTOR_ENABLED" = "true" ]; then
  echo "Starting MCP Inspector on port ${INSPECTOR_PORT:-8001}..."
  npx @modelcontextprotocol/inspector \
    --port ${INSPECTOR_PORT:-8001} \
    --server "node dist/index.js" &
  INSPECTOR_PID=$!
fi

# Start the MCP server
echo "Starting MCP Server..."
node dist/index.js &
SERVER_PID=$!

# Handle shutdown
trap "kill $SERVER_PID $INSPECTOR_PID" SIGTERM SIGINT

# Wait for processes
wait $SERVER_PID $INSPECTOR_PID
EOF

RUN chmod +x /app/start-with-inspector.sh

EXPOSE 8000 8001

CMD ["/app/start-with-inspector.sh"]
```

## Inspector Integration in MCP Hub

### Enhanced Registry with Inspector URLs

```typescript
// src/registry.ts - Enhanced server definition
export interface MCPServer {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'docker' | 'native' | 'remote';
  transport: 'stdio' | 'http' | 'websocket';
  connection: {
    command?: string;
    args?: string[];
    dockerImage?: string;
    port?: number;
    url?: string;
    env?: Record<string, string>;
  };
  inspector?: {
    enabled: boolean;
    port: number;
    url?: string;
  };
  capabilities: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    sampling?: boolean;
  };
  metadata: {
    author?: string;
    homepage?: string;
    tags?: string[];
    icon?: string;
  };
  health: {
    status: 'online' | 'offline' | 'error';
    lastCheck: Date;
    uptime?: number;
    inspectorStatus?: 'online' | 'offline';
  };
}
```

### Enhanced Web UI with Inspector Links

```html
<!-- public/index.html - Enhanced server cards -->
<div class="server-card">
  <h3>${server.name}</h3>
  <p>${server.description}</p>
  <div>Type: ${server.type}</div>
  <div>Version: ${server.version}</div>
  <span class="status ${server.health.status}">${server.health.status}</span>
  
  <!-- Inspector buttons -->
  <div class="actions">
    <button onclick="openInspector('${server.id}')" 
            class="inspector-btn ${server.inspector?.enabled ? '' : 'disabled'}">
      🔍 Open Inspector
    </button>
    <button onclick="testServer('${server.id}')" class="test-btn">
      🧪 Test Server
    </button>
  </div>
</div>

<script>
function openInspector(serverId) {
  const server = servers.find(s => s.id === serverId);
  if (server?.inspector?.enabled) {
    window.open(`http://${window.location.hostname}:${server.inspector.port}`, '_blank');
  } else {
    alert('Inspector not enabled for this server');
  }
}

function testServer(serverId) {
  // Quick test using the MCP Hub proxy
  fetch(`/mcp/${serverId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      jsonrpc: '2.0',
      method: 'tools/list',
      id: 1
    })
  })
  .then(res => res.json())
  .then(data => {
    console.log(`Server ${serverId} tools:`, data);
    alert(`Server has ${data.result?.tools?.length || 0} tools available`);
  })
  .catch(err => alert(`Error testing server: ${err.message}`));
}
</script>
```

## Server Configuration with Inspector

```json
// config/servers.json - Updated with Inspector configuration
[
  {
    "id": "it-assistant",
    "name": "IT Assistant MCP",
    "description": "Enterprise IT management and monitoring",
    "version": "1.0.0",
    "type": "docker",
    "transport": "http",
    "connection": {
      "dockerImage": "it-assistant-mcp:inspector",
      "port": 8000,
      "env": {
        "PROXMOX_HOST": "*************",
        "ELEVENLABS_API_KEY": "${ELEVENLABS_API_KEY}"
      }
    },
    "inspector": {
      "enabled": true,
      "port": 8001
    },
    "capabilities": {
      "tools": true,
      "resources": true,
      "prompts": true
    },
    "metadata": {
      "author": "ALIAS",
      "tags": ["infrastructure", "monitoring", "automation"]
    }
  },
  {
    "id": "browser-use",
    "name": "Browser Use MCP",
    "description": "Browser automation and web scraping",
    "version": "1.0.0",
    "type": "docker",
    "transport": "http",
    "connection": {
      "dockerImage": "browser-use-mcp:inspector",
      "port": 8100
    },
    "inspector": {
      "enabled": true,
      "port": 8101
    },
    "capabilities": {
      "tools": true,
      "resources": false
    },
    "metadata": {
      "author": "Browser Use Team",
      "tags": ["automation", "browser", "scraping"]
    }
  }
]
```

## Native MCP Server with Inspector

For native (non-Docker) MCP servers, use the Inspector wrapper:

```bash
#!/bin/bash
# start-mcp-with-inspector.sh

SERVER_NAME="$1"
SERVER_COMMAND="$2"
INSPECTOR_PORT="${3:-8001}"

if [ -z "$SERVER_NAME" ] || [ -z "$SERVER_COMMAND" ]; then
  echo "Usage: $0 <server-name> <server-command> [inspector-port]"
  exit 1
fi

echo "Starting $SERVER_NAME with Inspector on port $INSPECTOR_PORT..."

# Start the Inspector with the server command
npx @modelcontextprotocol/inspector \
  --port "$INSPECTOR_PORT" \
  --server "$SERVER_COMMAND"
```

## Nginx Configuration for Inspector Access

```nginx
# /etc/nginx/sites-available/mcp-hub
server {
    listen 80;
    server_name mcp-hub.local;
    
    # Main MCP Hub
    location / {
        proxy_pass http://localhost:3100;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    # MCP Proxy
    location /mcp/ {
        proxy_pass http://localhost:3101/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Inspector proxies for each server
    location /inspector/it-assistant/ {
        proxy_pass http://localhost:8001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /inspector/browser-use/ {
        proxy_pass http://localhost:8101/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## Health Check with Inspector Status

```typescript
// src/health-check.ts
export async function checkServerHealth(server: MCPServer): Promise<void> {
  try {
    // Check MCP server
    const serverResponse = await fetch(`http://localhost:${server.connection.port}/health`);
    server.health.status = serverResponse.ok ? 'online' : 'offline';
    
    // Check Inspector if enabled
    if (server.inspector?.enabled) {
      const inspectorResponse = await fetch(`http://localhost:${server.inspector.port}`);
      server.health.inspectorStatus = inspectorResponse.ok ? 'online' : 'offline';
    }
    
    server.health.lastCheck = new Date();
  } catch (error) {
    server.health.status = 'error';
    server.health.inspectorStatus = 'offline';
  }
}
```

## Benefits of Inspector Integration

1. **Debugging**: Real-time view of MCP protocol messages
2. **Testing**: Execute tools directly from web UI
3. **Documentation**: Auto-generated API documentation
4. **Monitoring**: See server capabilities and health
5. **Development**: Faster iteration when building MCP servers

## Quick Start Commands

```bash
# Build MCP server with Inspector
docker build -f Dockerfile.inspector -t my-mcp:inspector .

# Run with Inspector
docker run -d \
  -p 8000:8000 \
  -p 8001:8001 \
  -e INSPECTOR_ENABLED=true \
  -e INSPECTOR_PORT=8001 \
  --name my-mcp \
  my-mcp:inspector

# Access Inspector
open http://localhost:8001
```

## Inspector Features

- **Tools Explorer**: Browse and test all available tools
- **Resources Viewer**: See all resources and their contents
- **Prompts Tester**: Test prompt templates with variables
- **Message Log**: Real-time MCP protocol messages
- **Performance Metrics**: Response times and usage stats
- **Schema Validation**: Ensure proper tool implementations

This integration ensures every MCP server in your hub has professional debugging and inspection capabilities!