{"name": "it-assistant-mcp", "version": "1.0.0", "description": "MCP server for IT infrastructure management and monitoring", "type": "module", "main": "src/index.js", "bin": {"it-assistant-mcp": "src/index.js"}, "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node src/test.js", "inspector": "npx @modelcontextprotocol/inspector src/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "zod": "^3.23.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^22.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "it-management", "infrastructure", "monitoring", "automation", "proxmox", "voice-alerts"], "author": "IT Assistant MC<PERSON>", "license": "MIT", "directories": {"doc": "docs"}}