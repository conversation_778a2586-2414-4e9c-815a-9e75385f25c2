# Checkmate Dashboard Setup for Proxmox Infrastructure

## Overview
This guide sets up Check<PERSON> as a comprehensive monitoring dashboard for your Proxmox infrastructure, including the Capture agent for deep system monitoring.

## Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                     Checkmate Dashboard                      │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │   Frontend  │  │   Backend    │  │    MongoDB      │   │
│  │  (React.js) │  │  (Node.js)   │  │   Database      │   │
│  └──────┬──────┘  └──────┬───────┘  └────────┬────────┘   │
│         │                 │                    │            │
│         └─────────────────┴────────────────────┘            │
│                           │                                  │
│  ┌────────────────────────┴─────────────────────────────┐  │
│  │              Monitoring Targets                       │  │
│  │  ┌──────────┐  ┌──────────┐  ┌─────────────────┐   │  │
│  │  │ Websites │  │   APIs   │  │ Infrastructure  │   │  │
│  │  └──────────┘  └──────────┘  └─────────────────┘   │  │
│  └───────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘

Capture Agents (on each node):
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  Proxmox Host   │  │   Container 1   │  │      VM 1       │
│ (Capture Agent) │  │ (Capture Agent) │  │ (Capture Agent) │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

## Installation Options

### Option 1: Docker Deployment on Proxmox (Recommended)

#### Step 1: Create LXC Container for Checkmate
```bash
# Create container
pct create 108 /var/lib/vz/template/cache/debian-12-standard_12.7-1_amd64.tar.zst \
  --hostname checkmate \
  --memory 2048 \
  --cores 2 \
  --net0 name=eth0,bridge=vmbr0,ip=dhcp \
  --storage local \
  --rootfs local:8 \
  --unprivileged 1 \
  --features nesting=1

# Start container
pct start 108

# Get IP address
pct exec 108 -- ip addr show eth0
```

#### Step 2: Install Docker in Container
```bash
# Enter container
pct enter 108

# Update system
apt update && apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
apt install -y docker-compose-plugin
```

#### Step 3: Deploy Checkmate
```bash
# Create directory
mkdir -p /opt/checkmate
cd /opt/checkmate

# Create docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  checkmate:
    image: ghcr.io/bluewave-labs/checkmate:latest
    container_name: checkmate
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/checkmate
      - JWT_SECRET=$(openssl rand -base64 32)
      - PORT=3000
    depends_on:
      - mongodb
    volumes:
      - ./uploads:/app/uploads
    networks:
      - checkmate-network

  mongodb:
    image: mongo:7
    container_name: checkmate-mongodb
    restart: unless-stopped
    volumes:
      - mongodb-data:/data/db
      - mongodb-config:/data/configdb
    environment:
      - MONGO_INITDB_DATABASE=checkmate
    networks:
      - checkmate-network

volumes:
  mongodb-data:
  mongodb-config:

networks:
  checkmate-network:
    driver: bridge
EOF

# Start services
docker compose up -d

# Check status
docker compose ps
```

### Option 2: Using Dokploy (Already Installed)

Since you have Dokploy running on container 105 (************), you can deploy Checkmate through Dokploy's interface:

1. Access Dokploy at http://************:3000
2. Create new project "checkmate"
3. Add Docker Compose configuration from above
4. Deploy through Dokploy UI

## Capture Agent Installation

### Install on Proxmox Host
```bash
# Download Capture agent
wget https://github.com/bluewave-labs/capture/releases/latest/download/capture-linux-amd64
chmod +x capture-linux-amd64
mv capture-linux-amd64 /usr/local/bin/capture

# Create systemd service
cat > /etc/systemd/system/capture.service << 'EOF'
[Unit]
Description=Checkmate Capture Agent
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/capture --server http://192.168.1.XX:3000 --token YOUR_TOKEN
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start
systemctl enable capture
systemctl start capture
```

### Install on Containers/VMs
Deploy Capture agent on each container/VM you want to monitor:
```bash
# For Debian/Ubuntu containers
apt update && apt install -y wget
wget https://github.com/bluewave-labs/capture/releases/latest/download/capture-linux-amd64
chmod +x capture-linux-amd64
./capture-linux-amd64 --server http://checkmate-ip:3000 --token YOUR_TOKEN
```

## Configuration

### Initial Setup
1. Access Checkmate at http://[CONTAINER-IP]:3000
2. Create admin account
3. Configure monitoring settings

### Add Monitors
1. **Infrastructure Monitors** (via Capture):
   - CPU usage
   - Memory usage
   - Disk I/O
   - Network traffic
   - Process monitoring

2. **Service Monitors**:
   - Proxmox Web UI (https://*************:8006)
   - Dokploy (http://************:3000)
   - Browser-Use MCP (http://************:8000)
   - Home Assistant (http://************:8123)
   - Proxmox Backup Server (https://************:8007)

3. **SSL Certificate Monitoring**:
   - Proxmox SSL expiry
   - Any other HTTPS services

4. **Port Monitoring**:
   - SSH (22)
   - Proxmox API (8006)
   - Docker services

### Notification Setup
Configure alerts to integrate with your existing systems:

1. **Email Notifications**:
   ```yaml
   smtp:
     host: smtp.gmail.com
     port: 587
     secure: false
     auth:
       user: <EMAIL>
       pass: app-specific-password
   ```

2. **Webhook Integration**:
   - Connect to your IT Assistant MCP
   - Trigger automated responses

3. **Voice Alerts** (via IT Assistant):
   Create webhook endpoint in IT Assistant to receive Checkmate alerts and convert to voice

## Integration with Existing Infrastructure

### 1. Connect to Health Check Scripts
Modify `/usr/local/bin/proxmox-health-check.sh` to send data to Checkmate:
```bash
# Add to health check script
curl -X POST http://checkmate-ip:3000/api/metrics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -d "{
    \"metric\": \"disk_usage\",
    \"value\": $DISK_USAGE,
    \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
  }"
```

### 2. Status Page Creation
Create public status page for services:
- Configure at http://checkmate-ip:3000/status-pages
- Add all critical services
- Share link for team visibility

### 3. Incident Management
Integrate with existing procedures:
- Auto-create incidents on monitor failures
- Track resolution times
- Generate reports

## Advanced Features

### 1. Custom Dashboards
Create role-based dashboards:
- **Operations**: Real-time metrics, alerts
- **Management**: SLA compliance, trends
- **Development**: API performance, errors

### 2. JSON Query Monitoring
Monitor complex metrics:
```json
{
  "url": "http://*************:8080/api/mcp/stats",
  "method": "GET",
  "headers": {
    "Authorization": "Bearer TOKEN"
  },
  "jsonPath": "$.containers[?(@.status!='running')].length",
  "condition": "equals",
  "value": 0
}
```

### 3. Maintenance Windows
Schedule maintenance to prevent false alerts:
- Planned updates
- Backup windows
- Testing periods

## Security Considerations

1. **Network Isolation**:
   - Run Checkmate on internal network only
   - Use reverse proxy for external access

2. **Authentication**:
   - Enable 2FA for admin accounts
   - Use API tokens for agents
   - Rotate tokens regularly

3. **Data Protection**:
   - Regular MongoDB backups
   - Encrypt sensitive configuration
   - Limit metric retention

## Performance Optimization

1. **Resource Allocation**:
   - Start with 2GB RAM, 2 CPUs
   - Scale based on monitor count
   - Monitor Checkmate's own performance

2. **Database Tuning**:
   - Index frequently queried fields
   - Set appropriate retention policies
   - Regular maintenance

## Backup Strategy

Add to existing backup jobs:
```bash
# Backup Checkmate data
docker exec checkmate-mongodb mongodump --out /backup
tar -czf /mnt/storage-array/proxmox-backups/checkmate-$(date +%Y%m%d).tar.gz /opt/checkmate
```

## Next Steps

1. Deploy Checkmate using preferred method
2. Install Capture agents on all nodes
3. Configure monitors for all services
4. Set up notification channels
5. Create status pages
6. Document in Proxmox server documentation

## Support & Documentation

- GitHub: https://github.com/bluewave-labs/Checkmate
- Documentation: https://checkmate.bluewave.dev
- Community: Discord/Slack channels