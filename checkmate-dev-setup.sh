#!/bin/bash

# Checkmate Development Environment Setup
# This script sets up Checkmate for local development and customization

set -e

echo "🛠️  Checkmate Development Environment Setup"
echo "========================================="

# Configuration
PROJECT_DIR="$HOME/Projects/checkmate-custom"
GITHUB_REPO="https://github.com/bluewave-labs/Checkmate.git"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}Checking prerequisites...${NC}"
    
    # Check for Git
    if ! command -v git &> /dev/null; then
        echo -e "${RED}Git is not installed${NC}"
        exit 1
    fi
    
    # Check for Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}Node.js is not installed${NC}"
        echo "Install from: https://nodejs.org/"
        exit 1
    fi
    
    # Check for Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}Docker is not installed${NC}"
        echo "Install from: https://docker.com/"
        exit 1
    fi
    
    echo -e "${GREEN}✓ All prerequisites met${NC}"
}

# Function to setup project
setup_project() {
    echo -e "${YELLOW}Setting up project directory...${NC}"
    
    # Create project directory
    mkdir -p "$(dirname "$PROJECT_DIR")"
    
    # Clone repository
    if [ -d "$PROJECT_DIR" ]; then
        echo -e "${YELLOW}Project directory already exists${NC}"
        read -p "Remove and re-clone? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$PROJECT_DIR"
            git clone "$GITHUB_REPO" "$PROJECT_DIR"
        else
            cd "$PROJECT_DIR"
            git pull origin main
        fi
    else
        git clone "$GITHUB_REPO" "$PROJECT_DIR"
    fi
    
    cd "$PROJECT_DIR"
    echo -e "${GREEN}✓ Project cloned successfully${NC}"
}

# Function to setup development environment
setup_dev_environment() {
    echo -e "${YELLOW}Setting up development environment...${NC}"
    
    # Create development docker-compose
    cat > docker-compose.dev.yml << 'EOF'
version: '3.8'

services:
  # Frontend Development Server
  frontend:
    build:
      context: ./Client
      dockerfile: Dockerfile.dev
    container_name: checkmate-frontend-dev
    ports:
      - "3000:3000"
    volumes:
      - ./Client:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:5000
    command: npm start
    networks:
      - checkmate-dev

  # Backend Development Server
  backend:
    build:
      context: ./Server
      dockerfile: Dockerfile.dev
    container_name: checkmate-backend-dev
    ports:
      - "5000:5000"
    volumes:
      - ./Server:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=mongodb://mongodb:27017/checkmate-dev
      - JWT_SECRET=dev-secret-change-in-production
      - REDIS_URL=redis://redis:6379
    command: npm run dev
    depends_on:
      - mongodb
      - redis
    networks:
      - checkmate-dev

  # MongoDB for development
  mongodb:
    image: mongo:7
    container_name: checkmate-mongodb-dev
    ports:
      - "27017:27017"
    volumes:
      - mongodb-dev-data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=checkmate-dev
    networks:
      - checkmate-dev

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: checkmate-redis-dev
    ports:
      - "6379:6379"
    networks:
      - checkmate-dev

  # Capture agent for testing
  capture-test:
    build:
      context: .
      dockerfile: Dockerfile.capture
    container_name: checkmate-capture-test
    environment:
      - CAPTURE_SERVER=http://backend:5000
      - CAPTURE_TOKEN=dev-token
    depends_on:
      - backend
    networks:
      - checkmate-dev

volumes:
  mongodb-dev-data:

networks:
  checkmate-dev:
    driver: bridge
EOF

    echo -e "${GREEN}✓ Development environment configured${NC}"
}

# Function to create development Dockerfiles
create_dockerfiles() {
    echo -e "${YELLOW}Creating development Dockerfiles...${NC}"
    
    # Frontend Dockerfile
    cat > Client/Dockerfile.dev << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Development server
EXPOSE 3000
CMD ["npm", "start"]
EOF

    # Backend Dockerfile
    cat > Server/Dockerfile.dev << 'EOF'
FROM node:20-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Install nodemon for hot reload
RUN npm install -g nodemon

# Development server
EXPOSE 5000
CMD ["nodemon", "index.js"]
EOF

    # Capture Dockerfile for testing
    cat > Dockerfile.capture << 'EOF'
FROM golang:1.21-alpine AS builder

WORKDIR /app
RUN apk add --no-cache git
RUN git clone https://github.com/bluewave-labs/capture.git .
RUN go build -o capture .

FROM alpine:latest
RUN apk add --no-cache ca-certificates
COPY --from=builder /app/capture /usr/local/bin/capture
CMD ["capture"]
EOF

    echo -e "${GREEN}✓ Dockerfiles created${NC}"
}

# Function to create customization examples
create_customizations() {
    echo -e "${YELLOW}Creating customization examples...${NC}"
    
    # Create customizations directory
    mkdir -p customizations/{themes,plugins,integrations}
    
    # Custom theme example
    cat > customizations/themes/alias-theme.css << 'EOF'
/* ALIAS Custom Theme for Checkmate */
:root {
  /* Primary Colors */
  --primary-color: #00D4FF;
  --primary-dark: #0099CC;
  --primary-light: #66E5FF;
  
  /* Status Colors */
  --status-up: #00FF88;
  --status-down: #FF3366;
  --status-pending: #FFD700;
  
  /* Background Colors */
  --bg-primary: #0A0E27;
  --bg-secondary: #151935;
  --bg-card: #1E2139;
  
  /* Text Colors */
  --text-primary: #FFFFFF;
  --text-secondary: #B8BCC8;
  
  /* Accent Colors */
  --accent-blue: #00D4FF;
  --accent-green: #00FF88;
  --accent-purple: #9D4EDD;
}

/* Custom Dashboard Styling */
.dashboard-header {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-purple));
  padding: 2rem;
  border-radius: 1rem;
}

.monitor-card {
  background: var(--bg-card);
  border: 1px solid rgba(0, 212, 255, 0.2);
  transition: all 0.3s ease;
}

.monitor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
}
EOF

    # Custom plugin example - Proxmox integration
    cat > customizations/plugins/proxmox-integration.js << 'EOF'
// Proxmox Integration Plugin for Checkmate
class ProxmoxIntegration {
  constructor(config) {
    this.apiUrl = config.proxmoxUrl || 'https://*************:8006/api2/json';
    this.token = config.apiToken;
  }

  async getVMStatus() {
    try {
      const response = await fetch(`${this.apiUrl}/nodes/pve/qemu`, {
        headers: {
          'Authorization': `PVEAPIToken=${this.token}`
        }
      });
      return await response.json();
    } catch (error) {
      console.error('Proxmox API error:', error);
      return null;
    }
  }

  async createMonitorsFromVMs() {
    const vms = await this.getVMStatus();
    const monitors = [];
    
    for (const vm of vms.data) {
      monitors.push({
        name: `VM ${vm.vmid} - ${vm.name}`,
        type: 'proxmox-vm',
        target: vm.vmid,
        interval: 60,
        metadata: {
          node: 'pve',
          vmid: vm.vmid,
          type: 'qemu'
        }
      });
    }
    
    return monitors;
  }
}

module.exports = ProxmoxIntegration;
EOF

    # Voice alerts integration
    cat > customizations/integrations/voice-alerts.js << 'EOF'
// Voice Alerts Integration using ElevenLabs
const { ElevenLabsClient } = require('elevenlabs');

class VoiceAlerts {
  constructor(config) {
    this.client = new ElevenLabsClient({
      apiKey: config.elevenLabsApiKey
    });
    this.voiceId = config.voiceId || 'EXAVITQu4vr4xnSDxMaL';
  }

  async sendVoiceAlert(alert) {
    const message = this.formatAlertMessage(alert);
    
    try {
      const audio = await this.client.generate({
        voice: this.voiceId,
        text: message,
        model_id: 'eleven_multilingual_v2'
      });
      
      // Send to IT Assistant MCP for playback
      await fetch('http://localhost:8080/api/voice/play', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          audio: audio.toString('base64'),
          priority: alert.severity
        })
      });
    } catch (error) {
      console.error('Voice alert failed:', error);
    }
  }

  formatAlertMessage(alert) {
    const severity = alert.severity.toUpperCase();
    return `${severity} Alert: ${alert.monitor.name} is ${alert.status}. ${alert.message}`;
  }
}

module.exports = VoiceAlerts;
EOF

    # Custom API endpoints
    cat > customizations/integrations/custom-api.js << 'EOF'
// Custom API Extensions for Checkmate
const express = require('express');
const router = express.Router();

// ALIAS Infrastructure Status Endpoint
router.get('/api/alias/status', async (req, res) => {
  try {
    const status = {
      infrastructure: 'operational',
      services: await getServicesStatus(),
      metrics: await getSystemMetrics(),
      incidents: await getActiveIncidents()
    };
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Webhook for IT Assistant MCP
router.post('/api/webhooks/it-assistant', async (req, res) => {
  const { event, data } = req.body;
  
  // Process IT Assistant events
  switch (event) {
    case 'monitor.down':
      await triggerAutomatedResponse(data);
      break;
    case 'threshold.exceeded':
      await escalateToVoiceAlert(data);
      break;
    case 'incident.created':
      await notifyOncallTeam(data);
      break;
  }
  
  res.json({ processed: true });
});

module.exports = router;
EOF

    echo -e "${GREEN}✓ Customization examples created${NC}"
}

# Function to create development utilities
create_dev_utils() {
    echo -e "${YELLOW}Creating development utilities...${NC}"
    
    # Development commands script
    cat > dev.sh << 'EOF'
#!/bin/bash

# Checkmate Development Helper Script

case "$1" in
  start)
    echo "Starting development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "Frontend: http://localhost:3000"
    echo "Backend: http://localhost:5000"
    echo "MongoDB: mongodb://localhost:27017"
    ;;
  stop)
    echo "Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down
    ;;
  logs)
    docker-compose -f docker-compose.dev.yml logs -f $2
    ;;
  rebuild)
    echo "Rebuilding containers..."
    docker-compose -f docker-compose.dev.yml build --no-cache
    ;;
  shell)
    docker-compose -f docker-compose.dev.yml exec $2 sh
    ;;
  test)
    echo "Running tests..."
    cd Server && npm test
    cd ../Client && npm test
    ;;
  *)
    echo "Usage: ./dev.sh {start|stop|logs|rebuild|shell|test}"
    echo "  start   - Start development environment"
    echo "  stop    - Stop development environment"
    echo "  logs    - View logs (optional: service name)"
    echo "  rebuild - Rebuild containers"
    echo "  shell   - Shell into container"
    echo "  test    - Run tests"
    ;;
esac
EOF

    chmod +x dev.sh
    
    # Create VS Code workspace
    cat > checkmate.code-workspace << 'EOF'
{
  "folders": [
    {
      "path": "Client",
      "name": "Frontend"
    },
    {
      "path": "Server", 
      "name": "Backend"
    },
    {
      "path": "customizations",
      "name": "Customizations"
    }
  ],
  "settings": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "eslint.workingDirectories": ["Client", "Server"]
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "type": "node",
        "request": "attach",
        "name": "Attach to Backend",
        "port": 9229,
        "restart": true,
        "protocol": "inspector"
      }
    ]
  },
  "extensions": {
    "recommendations": [
      "dbaeumer.vscode-eslint",
      "esbenp.prettier-vscode",
      "mongodb.mongodb-vscode",
      "ms-azuretools.vscode-docker"
    ]
  }
}
EOF

    echo -e "${GREEN}✓ Development utilities created${NC}"
}

# Function to setup local development
setup_local_dev() {
    echo -e "${YELLOW}Setting up local development environment...${NC}"
    
    # Install dependencies
    echo "Installing frontend dependencies..."
    cd Client && npm install
    
    echo "Installing backend dependencies..."
    cd ../Server && npm install
    
    cd ..
    
    # Create .env files
    cat > Client/.env.development << 'EOF'
REACT_APP_API_URL=http://localhost:5000
REACT_APP_WEBSOCKET_URL=ws://localhost:5000
REACT_APP_VERSION=custom-dev
EOF

    cat > Server/.env.development << 'EOF'
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/checkmate-dev
JWT_SECRET=your-super-secret-jwt-key-change-in-production
REDIS_URL=redis://localhost:6379

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Integrations
PROXMOX_API_URL=https://*************:8006/api2/json
PROXMOX_API_TOKEN=root@pam!checkmate=your-token

# Voice Alerts
ELEVENLABS_API_KEY=your-elevenlabs-key
VOICE_ALERTS_ENABLED=true
EOF

    echo -e "${GREEN}✓ Local development environment ready${NC}"
}

# Function to show next steps
show_next_steps() {
    echo -e "\n${GREEN}✅ Development Environment Setup Complete!${NC}"
    echo -e "\n${YELLOW}Project Location:${NC} $PROJECT_DIR"
    echo -e "\n${BLUE}Quick Start:${NC}"
    echo -e "  cd $PROJECT_DIR"
    echo -e "  ./dev.sh start    # Start development environment"
    echo -e "\n${BLUE}Access Points:${NC}"
    echo -e "  Frontend: ${GREEN}http://localhost:3000${NC}"
    echo -e "  Backend API: ${GREEN}http://localhost:5000${NC}"
    echo -e "  MongoDB: ${GREEN}mongodb://localhost:27017${NC}"
    echo -e "\n${BLUE}Customization Files:${NC}"
    echo -e "  Theme: customizations/themes/alias-theme.css"
    echo -e "  Plugins: customizations/plugins/"
    echo -e "  Integrations: customizations/integrations/"
    echo -e "\n${BLUE}Development Commands:${NC}"
    echo -e "  ./dev.sh start    - Start dev environment"
    echo -e "  ./dev.sh stop     - Stop dev environment"
    echo -e "  ./dev.sh logs     - View logs"
    echo -e "  ./dev.sh shell    - Shell into container"
    echo -e "  ./dev.sh test     - Run tests"
    echo -e "\n${BLUE}VS Code:${NC}"
    echo -e "  code checkmate.code-workspace"
    echo -e "\n${YELLOW}Customization Ideas:${NC}"
    echo -e "  1. Apply ALIAS theme (customizations/themes/alias-theme.css)"
    echo -e "  2. Add Proxmox VM auto-discovery"
    echo -e "  3. Integrate voice alerts with IT Assistant"
    echo -e "  4. Create custom dashboard layouts"
    echo -e "  5. Add infrastructure-specific monitors"
}

# Main execution
main() {
    check_prerequisites
    
    echo -e "${YELLOW}This will set up Checkmate for development${NC}"
    echo -e "Location: $PROJECT_DIR"
    read -p "Continue? (y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    setup_project
    setup_dev_environment
    create_dockerfiles
    create_customizations
    create_dev_utils
    setup_local_dev
    show_next_steps
}

# Run main function
main