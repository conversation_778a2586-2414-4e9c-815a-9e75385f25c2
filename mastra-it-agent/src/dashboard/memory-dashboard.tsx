import React, { useState, useEffect } from 'react';
import { MemoryVisualization, ITEnhancedMemory } from '../memory/enhanced-memory';

// Memory Dashboard Component
export const MemoryDashboard: React.FC<{ memory: ITEnhancedMemory }> = ({ memory }) => {
  const [memoryData, setMemoryData] = useState<MemoryVisualization | null>(null);
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [selectedView, setSelectedView] = useState<'overview' | 'threads' | 'infrastructure' | 'patterns'>('overview');

  useEffect(() => {
    loadMemoryData();
    const interval = setInterval(loadMemoryData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadMemoryData = async () => {
    try {
      const context = memory.getConversationContext();
      const infrastructure = memory.getInfrastructureSummary();
      
      // Mock data structure - in real implementation, this would come from memory
      const data: MemoryVisualization = {
        threads: [
          {
            id: 'thread-1',
            title: 'System Health Investigation',
            timestamp: new Date().toISOString(),
            messageCount: 15,
            type: 'incident',
            severity: 'high'
          },
          {
            id: 'thread-2', 
            title: 'VM Deployment Assistance',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            messageCount: 8,
            type: 'conversation'
          },
          {
            id: 'thread-3',
            title: 'Weekly Maintenance Planning',
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            messageCount: 12,
            type: 'maintenance'
          }
        ],
        insights: {
          totalConversations: 45,
          totalIncidents: 12,
          resolvedIncidents: 10,
          commonIssues: context.commonIssues.map(([issue, count]) => ({ issue, count })),
          escalationTrends: [
            { date: '2025-01-01', level: 'critical', count: 2 },
            { date: '2025-01-02', level: 'high', count: 3 },
            { date: '2025-01-03', level: 'medium', count: 5 }
          ]
        },
        infrastructure: {
          nodes: infrastructure.nodes.map(([id, data]) => ({
            id,
            status: data.status || 'unknown',
            lastSeen: data.lastSeen || new Date().toISOString()
          })),
          vms: infrastructure.vms.map(([id, data]) => ({
            id,
            status: data.status || 'unknown',
            node: data.node || 'unknown',
            lastAction: data.lastAction || 'none'
          })),
          alerts: infrastructure.recentAlerts.map((alert, index) => ({
            id: `alert-${index}`,
            severity: alert.severity || 'info',
            message: alert.message || 'No message',
            timestamp: alert.timestamp || new Date().toISOString()
          }))
        },
        patterns: {
          timeOfDay: Array.from({ length: 24 }, (_, hour) => ({
            hour,
            incidents: Math.floor(Math.random() * 10),
            resolutions: Math.floor(Math.random() * 8)
          })),
          issueCategories: [
            { category: 'VM Issues', count: 15, avgResolutionTime: 45 },
            { category: 'Network Problems', count: 8, avgResolutionTime: 30 },
            { category: 'Storage Issues', count: 5, avgResolutionTime: 60 },
            { category: 'Service Failures', count: 12, avgResolutionTime: 25 }
          ],
          nodeHealth: infrastructure.nodes.map(([id]) => ({
            node: id,
            uptime: 95 + Math.random() * 5,
            issueCount: Math.floor(Math.random() * 5)
          }))
        }
      };
      
      setMemoryData(data);
    } catch (error) {
      console.error('Error loading memory data:', error);
    }
  };

  if (!memoryData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading memory data...</span>
      </div>
    );
  }

  return (
    <div className="memory-dashboard bg-white rounded-lg shadow-lg p-6">
      {/* Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: 'Overview', icon: '📊' },
              { key: 'threads', label: 'Conversation Threads', icon: '💬' },
              { key: 'infrastructure', label: 'Infrastructure Memory', icon: '🖥️' },
              { key: 'patterns', label: 'Learned Patterns', icon: '🧠' }
            ].map(({ key, label, icon }) => (
              <button
                key={key}
                onClick={() => setSelectedView(key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  selectedView === key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{icon}</span>
                {label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Overview Tab */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Summary Cards */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800">💬 Conversations</h3>
            <p className="text-2xl font-bold text-blue-600">{memoryData.insights.totalConversations}</p>
            <p className="text-sm text-blue-600">Total conversations stored</p>
          </div>
          
          <div className="bg-red-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-red-800">🚨 Incidents</h3>
            <p className="text-2xl font-bold text-red-600">{memoryData.insights.totalIncidents}</p>
            <p className="text-sm text-red-600">{memoryData.insights.resolvedIncidents} resolved</p>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800">🖥️ Infrastructure</h3>
            <p className="text-2xl font-bold text-green-600">{memoryData.infrastructure.nodes.length}</p>
            <p className="text-sm text-green-600">Nodes monitored</p>
          </div>

          {/* Common Issues */}
          <div className="col-span-full bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">🔍 Most Common Issues</h3>
            <div className="space-y-2">
              {memoryData.insights.commonIssues.slice(0, 5).map((issue, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-gray-700">{issue.issue}</span>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                    {issue.count} times
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Conversation Threads Tab */}
      {selectedView === 'threads' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Conversation Threads</h3>
          <div className="space-y-3">
            {memoryData.threads.map((thread) => (
              <div
                key={thread.id}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedThread === thread.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedThread(thread.id)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{thread.title}</h4>
                    <p className="text-sm text-gray-600">
                      {thread.messageCount} messages • {new Date(thread.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      thread.type === 'incident' ? 'bg-red-100 text-red-800' :
                      thread.type === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {thread.type}
                    </span>
                    {thread.severity && (
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        thread.severity === 'critical' ? 'bg-red-600 text-white' :
                        thread.severity === 'high' ? 'bg-orange-500 text-white' :
                        'bg-yellow-500 text-white'
                      }`}>
                        {thread.severity}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Infrastructure Memory Tab */}
      {selectedView === 'infrastructure' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Infrastructure State Memory</h3>
          
          {/* Nodes */}
          <div>
            <h4 className="font-medium mb-3">📡 Nodes ({memoryData.infrastructure.nodes.length})</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {memoryData.infrastructure.nodes.map((node) => (
                <div key={node.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{node.id}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      node.status === 'online' ? 'bg-green-100 text-green-800' :
                      node.status === 'offline' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {node.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Last seen: {new Date(node.lastSeen).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* VMs */}
          <div>
            <h4 className="font-medium mb-3">🖥️ Virtual Machines ({memoryData.infrastructure.vms.length})</h4>
            <div className="space-y-2">
              {memoryData.infrastructure.vms.map((vm) => (
                <div key={vm.id} className="p-3 border rounded-lg flex items-center justify-between">
                  <div>
                    <span className="font-medium">{vm.id}</span>
                    <span className="text-gray-600 ml-2">on {vm.node}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      vm.status === 'running' ? 'bg-green-100 text-green-800' :
                      vm.status === 'stopped' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {vm.status}
                    </span>
                    <span className="text-sm text-gray-600">{vm.lastAction}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Alerts */}
          <div>
            <h4 className="font-medium mb-3">🚨 Recent Alerts</h4>
            <div className="space-y-2">
              {memoryData.infrastructure.alerts.slice(0, 10).map((alert) => (
                <div key={alert.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      alert.severity === 'critical' ? 'bg-red-600 text-white' :
                      alert.severity === 'warning' ? 'bg-yellow-500 text-white' :
                      'bg-blue-500 text-white'
                    }`}>
                      {alert.severity}
                    </span>
                    <span className="text-sm text-gray-600">
                      {new Date(alert.timestamp).toLocaleString()}
                    </span>
                  </div>
                  <p className="mt-2 text-gray-700">{alert.message}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Learned Patterns Tab */}
      {selectedView === 'patterns' && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">🧠 Learned Patterns & Analytics</h3>
          
          {/* Issue Categories */}
          <div>
            <h4 className="font-medium mb-3">📊 Issue Categories</h4>
            <div className="space-y-2">
              {memoryData.patterns.issueCategories.map((category, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{category.category}</span>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{category.count} incidents</span>
                      <span>~{category.avgResolutionTime}min avg resolution</span>
                    </div>
                  </div>
                  <div className="mt-2 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(category.count / 20) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Node Health Patterns */}
          <div>
            <h4 className="font-medium mb-3">🏥 Node Health Patterns</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {memoryData.patterns.nodeHealth.map((node, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{node.node}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      node.uptime > 99 ? 'bg-green-100 text-green-800' :
                      node.uptime > 95 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {node.uptime.toFixed(1)}% uptime
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {node.issueCount} issues tracked
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Time-based Patterns */}
          <div>
            <h4 className="font-medium mb-3">⏰ Time-based Incident Patterns</h4>
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-600 mb-2">Incidents by hour of day</div>
              <div className="flex items-end space-x-1 h-32">
                {memoryData.patterns.timeOfDay.map((data, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div className="flex flex-col items-center space-y-1">
                      <div 
                        className="bg-red-500 w-full rounded-t"
                        style={{ height: `${(data.incidents / 10) * 80}px` }}
                        title={`${data.incidents} incidents`}
                      ></div>
                      <div 
                        className="bg-green-500 w-full rounded-b"
                        style={{ height: `${(data.resolutions / 10) * 80}px` }}
                        title={`${data.resolutions} resolutions`}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">{data.hour}</div>
                  </div>
                ))}
              </div>
              <div className="flex justify-center space-x-4 mt-2 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded mr-1"></div>
                  <span>Incidents</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded mr-1"></div>
                  <span>Resolutions</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemoryDashboard;