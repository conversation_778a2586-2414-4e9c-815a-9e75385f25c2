// Dashboard configuration for IT Infrastructure Agent
export const dashboardConfig = {
  title: 'IT Infrastructure Management',
  description: 'AI-powered infrastructure monitoring and management',
  
  // Agent configurations for the UI
  agents: {
    itInfrastructureAgent: {
      displayName: 'IT Infrastructure Agent',
      description: 'Comprehensive IT management and monitoring',
      icon: '🖥️',
      color: '#2563eb',
      featured: true,
      capabilities: [
        'System Health Monitoring',
        'VM Management',
        'Performance Analysis',
        'Log Analysis',
        'Network Diagnostics',
        'Task Automation'
      ]
    },
    monitoringAgent: {
      displayName: 'Monitoring Specialist',
      description: 'System monitoring and alerting specialist',
      icon: '📊',
      color: '#059669',
      capabilities: [
        'Real-time Monitoring',
        'Alert Management',
        'Performance Metrics',
        'Trend Analysis'
      ]
    },
    vmManagementAgent: {
      displayName: 'VM Manager',
      description: 'Virtual machine operations specialist',
      icon: '⚙️',
      color: '#dc2626',
      capabilities: [
        'VM Lifecycle Management',
        'Resource Optimization',
        'Proxmox Integration',
        'Backup Management'
      ]
    },
    automationAgent: {
      displayName: 'Automation Engine',
      description: 'Task automation and scheduling specialist',
      icon: '🤖',
      color: '#7c3aed',
      capabilities: [
        'Task Scheduling',
        'Workflow Automation',
        'Infrastructure as Code',
        'Compliance Monitoring'
      ]
    }
  },
  
  // Tool categories for organized display
  toolCategories: {
    monitoring: {
      name: 'System Monitoring',
      icon: '📈',
      tools: ['checkSystemHealth', 'getSystemMetrics', 'analyzeLogs']
    },
    infrastructure: {
      name: 'Infrastructure Management',
      icon: '🏗️',
      tools: ['manageVM', 'scheduleTask']
    },
    networking: {
      name: 'Network Operations',
      icon: '🌐',
      tools: ['networkDiagnostics']
    }
  },
  
  // Workflow display configuration
  workflows: {
    systemHealthWorkflow: {
      displayName: 'System Health Check',
      description: 'Automated comprehensive health assessment',
      icon: '🩺',
      category: 'monitoring'
    },
    vmDeploymentWorkflow: {
      displayName: 'VM Deployment',
      description: 'Streamlined virtual machine deployment',
      icon: '🚀',
      category: 'infrastructure'
    },
    backupWorkflow: {
      displayName: 'Automated Backup',
      description: 'Scheduled backup and retention management',
      icon: '💾',
      category: 'infrastructure'
    },
    incidentResponseWorkflow: {
      displayName: 'Incident Response',
      description: 'Structured incident handling and escalation',
      icon: '🚨',
      category: 'monitoring'
    },
    maintenanceWorkflow: {
      displayName: 'Maintenance Window',
      description: 'Planned maintenance coordination',
      icon: '🔧',
      category: 'infrastructure'
    },
    networkDiagnosticsWorkflow: {
      displayName: 'Network Diagnostics',
      description: 'Network connectivity troubleshooting',
      icon: '🔍',
      category: 'networking'
    },
    capacityPlanningWorkflow: {
      displayName: 'Capacity Planning',
      description: 'Resource growth analysis and planning',
      icon: '📊',
      category: 'monitoring'
    }
  },
  
  // Quick actions for the dashboard
  quickActions: [
    {
      name: 'System Health Check',
      description: 'Run comprehensive health assessment',
      action: 'checkSystemHealth',
      icon: '🩺',
      agent: 'monitoringAgent'
    },
    {
      name: 'VM Status',
      description: 'Check all VM statuses',
      action: 'manageVM',
      icon: '📊',
      agent: 'vmManagementAgent'
    },
    {
      name: 'Network Test',
      description: 'Run network diagnostics',
      action: 'networkDiagnostics',
      icon: '🌐',
      agent: 'itInfrastructureAgent'
    },
    {
      name: 'Schedule Backup',
      description: 'Set up automated backups',
      action: 'scheduleTask',
      icon: '💾',
      agent: 'automationAgent'
    }
  ],
  
  // Sample queries for different scenarios
  sampleQueries: {
    monitoring: [
      'Check the overall system health across all nodes',
      'What is the current CPU and memory usage?',
      'Show me any failed services in the last hour',
      'Analyze logs for any critical errors'
    ],
    vmManagement: [
      'List all VMs and their current status',
      'Start VM 101 on node proxmox-01',
      'Create a snapshot of the database VM',
      'Show me VMs with high resource usage'
    ],
    automation: [
      'Schedule daily backups for all production VMs',
      'Set up weekly system updates',
      'Create a maintenance window for this weekend',
      'Automate health checks every 15 minutes'
    ],
    troubleshooting: [
      'Why is VM 205 showing high CPU usage?',
      'Diagnose network connectivity issues',
      'Find the root cause of service failures',
      'Run a full system diagnostic'
    ]
  },
  
  // Monitoring thresholds for dashboard alerts
  thresholds: {
    cpu: { warning: 70, critical: 80 },
    memory: { warning: 80, critical: 90 },
    disk: { warning: 75, critical: 85 },
    network: { warning: 80, critical: 95 }
  },
  
  // Dashboard refresh intervals
  refreshIntervals: {
    systemMetrics: 30000,      // 30 seconds
    vmStatus: 60000,           // 1 minute
    logs: 10000,               // 10 seconds
    alerts: 5000,              // 5 seconds
    memory: 15000              // 15 seconds
  },
  
  // Memory visualization settings
  memoryVisualization: {
    enabled: true,
    maxThreadsDisplay: 50,
    maxMessagesPerThread: 100,
    retentionDays: 30,
    searchLimit: 20,
    patternAnalysisDepth: 7    // days
  },
  
  // Enhanced features for memory integration
  features: {
    memorySearch: true,
    patternRecognition: true,
    incidentLearning: true,
    contextualHelp: true,
    proactiveAlerts: true,
    resolutionSuggestions: true
  }
}
};