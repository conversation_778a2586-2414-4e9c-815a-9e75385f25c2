#!/usr/bin/env bun
import { mastra } from './mastra/index.js';
import { dashboardConfig } from './dashboard/config.js';
import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Example usage of the IT Infrastructure Agent
async function demonstrateAgent() {
  const agent = mastra.getAgent('itInfrastructureAgent');
  
  console.log('🚀 IT Infrastructure Agent Demo\n');
  
  // Example 1: System Health Check
  console.log('📊 Checking system health...');
  try {
    const healthResponse = await agent.generate(
      'Check the overall system health including services and resource usage',
      {
        tools: ['checkSystemHealth', 'getSystemMetrics']
      }
    );
    console.log('Health Check Result:', healthResponse.text);
  } catch (error) {
    console.error('Health check failed:', error.message);
  }
  
  console.log('\n-------------------\n');
  
  // Example 2: VM Management Query
  console.log('🖥️ VM Management Query...');
  try {
    const vmResponse = await agent.generate(
      'What is the status of VM 101? If it\'s stopped, please start it.',
      {
        tools: ['manageVM']
      }
    );
    console.log('VM Management Result:', vmResponse.text);
  } catch (error) {
    console.error('VM management failed:', error.message);
  }
  
  console.log('\n-------------------\n');
  
  // Example 3: Schedule a Backup Task
  console.log('💾 Scheduling automated backup...');
  try {
    const backupResponse = await agent.generate(
      'Schedule a daily backup task for VMs 101, 102, and 103 at 2 AM every day',
      {
        tools: ['scheduleTask']
      }
    );
    console.log('Backup Scheduling Result:', backupResponse.text);
  } catch (error) {
    console.error('Backup scheduling failed:', error.message);
  }
  
  console.log('\n-------------------\n');
  
  // Example 4: Network Diagnostics
  console.log('🌐 Running network diagnostics...');
  try {
    const networkResponse = await agent.generate(
      'Run network diagnostics for google.com including ping and DNS tests',
      {
        tools: ['networkDiagnostics']
      }
    );
    console.log('Network Diagnostics Result:', networkResponse.text);
  } catch (error) {
    console.error('Network diagnostics failed:', error.message);
  }
}

// Interactive CLI mode
async function interactiveMode() {
  const agent = mastra.getAgent('itInfrastructureAgent');
  
  console.log('🤖 IT Infrastructure Agent - Interactive Mode');
  console.log('Type your IT management queries below. Type "exit" to quit.\n');
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const askQuestion = () => {
    rl.question('IT Agent> ', async (input) => {
      if (input.toLowerCase() === 'exit') {
        console.log('👋 Goodbye!');
        rl.close();
        return;
      }
      
      if (!input.trim()) {
        askQuestion();
        return;
      }
      
      try {
        console.log('🔄 Processing...');
        const response = await agent.generate(input);
        console.log('\n🤖 Agent Response:');
        console.log(response.text);
        console.log('\n');
      } catch (error) {
        console.error('❌ Error:', error.message);
        console.log('');
      }
      
      askQuestion();
    });
  };
  
  askQuestion();
}

// Monitoring mode - continuous system monitoring
async function monitoringMode() {
  const monitoringAgent = mastra.getAgent('monitoringAgent');
  
  console.log('📊 Starting continuous system monitoring...');
  console.log('Press Ctrl+C to stop monitoring\n');
  
  const monitorInterval = setInterval(async () => {
    try {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Running health check...`);
      
      const response = await monitoringAgent.generate(
        'Perform a comprehensive system health check and report any issues',
        {
          tools: ['checkSystemHealth', 'getSystemMetrics']
        }
      );
      
      console.log('Monitoring Result:', response.text);
      console.log('---');
    } catch (error) {
      console.error('Monitoring error:', error.message);
    }
  }, 60000); // Check every minute
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping monitoring...');
    clearInterval(monitorInterval);
    process.exit(0);
  });
}

// Main execution function
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'demo';
  
  switch (mode) {
    case 'demo':
      await demonstrateAgent();
      break;
    case 'interactive':
    case 'chat':
      await interactiveMode();
      break;
    case 'monitor':
      await monitoringMode();
      break;
    case 'help':
      console.log(`
🚀 IT Infrastructure Agent Usage:

Commands:
  bun start demo        - Run demonstration of agent capabilities
  bun start interactive - Start interactive chat mode with the agent
  bun start monitor     - Start continuous monitoring mode
  bun start help        - Show this help message

Environment Variables:
  OPENAI_API_KEY       - Required for AI model access
  ELEVENLABS_API_KEY   - Optional for voice alerts
  ELEVENLABS_VOICE_ID  - Optional voice ID for ElevenLabs
  PROXMOX_HOST         - Proxmox server hostname/IP
  PROXMOX_TOKEN        - Proxmox API token
  UPSTASH_REDIS_URL    - Redis URL for memory persistence
  UPSTASH_REDIS_TOKEN  - Redis token for memory persistence

Examples:
  bun start demo
  bun start interactive
  bun start monitor
      `);
      break;
    default:
      console.log(`Unknown mode: ${mode}. Use 'help' for usage information.`);
  }
}

// Check required environment variables
function checkEnvironment() {
  const required = ['OPENAI_API_KEY'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`  - ${key}`));
    console.log('\nPlease set these variables and try again.');
    process.exit(1);
  }
  
  // Warn about optional variables
  const optional = {
    'ELEVENLABS_API_KEY': 'Voice alerts will be disabled',
    'PROXMOX_HOST': 'VM management will use mock data',
    'UPSTASH_REDIS_URL': 'Memory will not persist between sessions'
  };
  
  Object.entries(optional).forEach(([key, warning]) => {
    if (!process.env[key]) {
      console.warn(`⚠️  ${key} not set: ${warning}`);
    }
  });
  
  console.log(''); // Add spacing
}

// Run the application
checkEnvironment();
main().catch(console.error);