import { <PERSON><PERSON> } from '@mastra/core';
import { agents } from './agents/index.js';
import { itTools } from './tools/index.js';
import { itWorkflows } from './workflows/index.js';

// Initialize <PERSON>stra with IT Infrastructure configuration
export const mastra = new Mastra({
  name: 'IT Infrastructure Management System',
  agents: agents,
  tools: itTools,
  // workflows: itWorkflows, // Temporarily disabled for basic functionality
  
  // Environment configuration
  environment: process.env.NODE_ENV || 'development',
  
  // Logging configuration
  logs: {
    type: 'CONSOLE',
    level: process.env.LOG_LEVEL || 'info'
  }
});

// Export individual components for direct access
export { agents, itTools, itWorkflows };

// Export the configured Mastra instance as default
export default mastra;