import { tool } from 'ai';
import { z } from 'zod';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ITMemoryProcessor } from '../../memory/enhanced-memory.js';

const execAsync = promisify(exec);

// Global memory instance (will be injected by agents)
let memoryInstance: any = null;

export const setMemoryInstance = (memory: any) => {
  memoryInstance = memory;
};

// System Health Check Tool
export const checkSystemHealth = tool({
  description: 'Get comprehensive system health status including services, resources, and VMs',
  parameters: z.object({
    includeServices: z.boolean().optional().describe('Include service status'),
    includeResources: z.boolean().optional().describe('Include resource usage'),
    includeVMs: z.boolean().optional().describe('Include VM status'),
    nodes: z.array(z.string()).optional().describe('Specific nodes to check')
  }),
  execute: async ({ includeServices = true, includeResources = true, includeVMs = false, nodes }) => {
    const health = {
      overall: 'healthy',
      timestamp: new Date().toISOString(),
      services: {},
      resources: {},
      vms: {},
      alerts: []
    };

    try {
      if (includeServices) {
        // Check system services
        const { stdout: services } = await execAsync('systemctl list-units --failed --no-pager');
        health.services = {
          failed: services.split('\n').filter(line => line.includes('failed')).length,
          status: services.includes('0 loaded units listed') ? 'healthy' : 'issues_detected'
        };
      }

      if (includeResources) {
        // Get CPU, memory, disk usage
        const { stdout: cpuInfo } = await execAsync("top -l 1 | grep 'CPU usage' || echo 'CPU: N/A'");
        const { stdout: memInfo } = await execAsync("vm_stat | head -5");
        const { stdout: diskInfo } = await execAsync("df -h / | tail -1");
        
        health.resources = {
          cpu: cpuInfo.trim(),
          memory: memInfo.trim(),
          disk: diskInfo.trim()
        };
      }

      if (includeVMs && nodes) {
        // This would integrate with Proxmox API
        health.vms = {
          total: 0,
          running: 0,
          stopped: 0,
          message: 'Proxmox integration required'
        };
      }

      // Store in enhanced memory
      if (memoryInstance) {
        await memoryInstance.updateInfrastructureState('system', 'health', health);
      }

      return {
        success: true,
        data: health
      };
    } catch (error) {
      // Record error in memory
      if (memoryInstance) {
        await memoryInstance.recordIncident({
          severity: 'medium',
          description: `System health check failed: ${error.message}`,
          affectedSystems: ['monitoring'],
          timestamp: new Date().toISOString()
        });
      }

      return {
        success: false,
        error: error.message
      };
    }
  }
});

// VM Management Tool
export const manageVM = tool({
  description: 'Start, stop, restart, or check status of a VM',
  parameters: z.object({
    vmid: z.string().describe('VM ID'),
    action: z.enum(['start', 'stop', 'restart', 'status']).describe('Action to perform'),
    node: z.string().optional().describe('Node where VM is located')
  }),
  execute: async ({ vmid, action, node }) => {
    try {
      // Mock Proxmox VM management
      const result = {
        vmid,
        action,
        node: node || 'default',
        status: 'success',
        message: `VM ${vmid} ${action} operation completed`,
        timestamp: new Date().toISOString()
      };

      // In a real implementation, this would call Proxmox API
      // Example: await proxmox.nodes(node).qemu(vmid)[action]();

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

// System Monitoring Tool
export const getSystemMetrics = tool({
  description: 'Get detailed system metrics (CPU, memory, disk, network)',
  parameters: z.object({
    node: z.string().optional().describe('Node to check'),
    timeRange: z.enum(['1h', '6h', '24h', '7d']).optional().describe('Time range for metrics'),
    metricType: z.enum(['cpu', 'memory', 'disk', 'network', 'all']).optional().describe('Type of metrics to retrieve')
  }),
  execute: async ({ node = 'localhost', timeRange = '1h', metricType = 'all' }) => {
    try {
      const metrics = {
        node,
        timeRange,
        timestamp: new Date().toISOString(),
        data: {}
      };

      if (metricType === 'cpu' || metricType === 'all') {
        const { stdout: cpuUsage } = await execAsync("ps -A -o %cpu | awk '{s+=$1} END {print s}'");
        metrics.data.cpu = {
          usage: parseFloat(cpuUsage.trim()) || 0,
          cores: require('os').cpus().length
        };
      }

      if (metricType === 'memory' || metricType === 'all') {
        const totalMem = require('os').totalmem();
        const freeMem = require('os').freemem();
        metrics.data.memory = {
          total: Math.round(totalMem / 1024 / 1024 / 1024),
          free: Math.round(freeMem / 1024 / 1024 / 1024),
          used: Math.round((totalMem - freeMem) / 1024 / 1024 / 1024),
          percentage: Math.round(((totalMem - freeMem) / totalMem) * 100)
        };
      }

      if (metricType === 'disk' || metricType === 'all') {
        const { stdout: diskUsage } = await execAsync("df -h / | tail -1 | awk '{print $5}' | sed 's/%//'");
        metrics.data.disk = {
          usage_percentage: parseInt(diskUsage.trim()) || 0
        };
      }

      return {
        success: true,
        data: metrics
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

// Automated Task Scheduling Tool
export const scheduleTask = tool({
  description: 'Schedule automated IT tasks (backup, maintenance, updates)',
  parameters: z.object({
    name: z.string().describe('Task name'),
    type: z.enum(['backup', 'update', 'maintenance', 'custom']).describe('Task type'),
    schedule: z.string().describe('Cron schedule expression'),
    targets: z.array(z.string()).describe('Target systems or VMs'),
    script: z.string().optional().describe('Script to execute for custom tasks'),
    enabled: z.boolean().optional().default(true)
  }),
  execute: async ({ name, type, schedule, targets, script, enabled }) => {
    try {
      const task = {
        id: `task-${Date.now()}`,
        name,
        type,
        schedule,
        targets,
        script: script || '',
        enabled,
        created: new Date().toISOString(),
        status: 'scheduled'
      };

      // In a real implementation, this would integrate with cron or a task scheduler
      console.log(`Scheduled task: ${name} with schedule: ${schedule}`);

      return {
        success: true,
        data: task,
        message: `Task "${name}" scheduled successfully`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

// Log Analysis Tool
export const analyzeLogs = tool({
  description: 'Search and analyze system logs for troubleshooting',
  parameters: z.object({
    query: z.string().describe('Search query or pattern'),
    service: z.string().optional().describe('Specific service logs to search'),
    severity: z.enum(['debug', 'info', 'warning', 'error', 'critical']).optional().describe('Minimum severity level'),
    timeRange: z.string().optional().describe('Time range (e.g., "1h", "24h")'),
    maxLines: z.number().optional().default(100).describe('Maximum lines to return')
  }),
  execute: async ({ query, service, severity, timeRange = '1h', maxLines }) => {
    try {
      let logCommand = 'tail -n 1000 /var/log/system.log';
      
      // Adjust command based on service
      if (service) {
        logCommand = `journalctl -u ${service} --since="${timeRange} ago" -n ${maxLines}`;
      }

      // For macOS, use log command
      if (process.platform === 'darwin') {
        logCommand = `log show --last ${timeRange} --predicate 'eventMessage contains "${query}"' | head -${maxLines}`;
      }

      const { stdout } = await execAsync(logCommand);
      const logs = stdout.split('\n').filter(line => 
        line.toLowerCase().includes(query.toLowerCase())
      ).slice(0, maxLines);

      const analysis = {
        query,
        service: service || 'system',
        timeRange,
        totalMatches: logs.length,
        logs: logs,
        summary: `Found ${logs.length} log entries matching "${query}"`,
        timestamp: new Date().toISOString()
      };

      return {
        success: true,
        data: analysis
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Log analysis failed - check system log access permissions'
      };
    }
  }
});

// Network Diagnostics Tool
export const networkDiagnostics = tool({
  description: 'Run network diagnostics and connectivity tests',
  parameters: z.object({
    target: z.string().describe('Target host or IP to test'),
    tests: z.array(z.enum(['ping', 'traceroute', 'port', 'dns'])).describe('Types of tests to run'),
    port: z.number().optional().describe('Port number for port connectivity test')
  }),
  execute: async ({ target, tests, port }) => {
    try {
      const results = {
        target,
        tests: {},
        timestamp: new Date().toISOString()
      };

      for (const test of tests) {
        switch (test) {
          case 'ping':
            try {
              const { stdout } = await execAsync(`ping -c 4 ${target}`);
              results.tests.ping = {
                success: true,
                output: stdout.trim()
              };
            } catch (error) {
              results.tests.ping = {
                success: false,
                error: error.message
              };
            }
            break;

          case 'dns':
            try {
              const { stdout } = await execAsync(`nslookup ${target}`);
              results.tests.dns = {
                success: true,
                output: stdout.trim()
              };
            } catch (error) {
              results.tests.dns = {
                success: false,
                error: error.message
              };
            }
            break;

          case 'port':
            if (port) {
              try {
                const { stdout } = await execAsync(`nc -zv ${target} ${port}`);
                results.tests.port = {
                  success: true,
                  port,
                  output: stdout.trim()
                };
              } catch (error) {
                results.tests.port = {
                  success: false,
                  port,
                  error: error.message
                };
              }
            }
            break;

          case 'traceroute':
            try {
              const { stdout } = await execAsync(`traceroute ${target}`);
              results.tests.traceroute = {
                success: true,
                output: stdout.trim()
              };
            } catch (error) {
              results.tests.traceroute = {
                success: false,
                error: error.message
              };
            }
            break;
        }
      }

      return {
        success: true,
        data: results
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

// Export all tools
export const itTools = {
  checkSystemHealth,
  manageVM,
  getSystemMetrics,
  scheduleTask,
  analyzeLogs,
  networkDiagnostics
};