// Memory Integration for Mastra IT Agent
import { ITEnhancedMemory } from './enhanced-memory.js';
import { dashboardConfig } from '../dashboard/config.js';

// Initialize and configure enhanced memory with IT-specific settings
export const initializeITMemory = () => {
  const memory = new ITEnhancedMemory({
    // Use Upstash Redis if available, fallback to in-memory
    provider: process.env.UPSTASH_REDIS_URL ? 'UPSTASH_REDIS' : 'IN_MEMORY',
    url: process.env.UPSTASH_REDIS_URL,
    token: process.env.UPSTASH_REDIS_TOKEN,
    
    // IT-specific configuration
    config: {
      // Retention policies for different data types
      retention: {
        conversations: dashboardConfig.memoryVisualization.retentionDays,
        incidents: 90,      // Keep incidents for 90 days
        metrics: 30,        // Keep metrics for 30 days
        patterns: 365       // Keep learned patterns for 1 year
      },
      
      // Search configuration
      search: {
        limit: dashboardConfig.memoryVisualization.searchLimit,
        includeContext: true,
        semanticSearch: true
      },
      
      // Pattern recognition settings
      patternRecognition: {
        enabled: dashboardConfig.features.patternRecognition,
        minOccurrences: 3,  // Need 3+ occurrences to recognize pattern
        analysisDepth: dashboardConfig.memoryVisualization.patternAnalysisDepth
      },
      
      // Auto-categorization
      categorization: {
        enabled: true,
        categories: [
          'incident',
          'maintenance', 
          'monitoring',
          'conversation',
          'automation',
          'troubleshooting'
        ]
      }
    }
  });

  return memory;
};

// Memory middleware for tool integration
export const memoryMiddleware = (memory: ITEnhancedMemory) => {
  return {
    // Pre-execution: Add context from memory
    beforeToolExecution: async (toolName: string, params: any) => {
      const context = memory.getConversationContext();
      
      // Add relevant context based on tool type
      if (toolName === 'checkSystemHealth') {
        const infrastructure = memory.getInfrastructureSummary();
        return {
          ...params,
          context: {
            recentAlerts: infrastructure.recentAlerts,
            knownIssues: context.commonIssues
          }
        };
      }
      
      if (toolName === 'manageVM') {
        const similarIssues = await memory.findSimilarIssues(`vm ${params.vmid} ${params.action}`);
        return {
          ...params,
          context: {
            similarIssues: similarIssues.slice(0, 3),
            vmHistory: `Recent actions on VM ${params.vmid}`
          }
        };
      }
      
      return params;
    },
    
    // Post-execution: Store results in memory
    afterToolExecution: async (toolName: string, params: any, result: any) => {
      // Store successful operations
      if (result.success) {
        switch (toolName) {
          case 'checkSystemHealth':
            await memory.updateInfrastructureState('system', 'health', result.data);
            break;
            
          case 'manageVM':
            await memory.updateInfrastructureState('vm', params.vmid, {
              action: params.action,
              result: result.data,
              timestamp: new Date().toISOString()
            });
            break;
            
          case 'scheduleTask':
            await memory.recordResolution(
              `task_scheduling_${params.type}`,
              {
                action: 'scheduled',
                task: result.data,
                success: true
              }
            );
            break;
        }
      } else {
        // Record failures as incidents
        await memory.recordIncident({
          severity: 'medium',
          description: `Tool ${toolName} failed: ${result.error}`,
          affectedSystems: [toolName],
          parameters: params,
          timestamp: new Date().toISOString()
        });
      }
    }
  };
};

// Memory-enhanced prompt suggestions
export const getMemoryEnhancedPrompts = async (memory: ITEnhancedMemory, category?: string) => {
  const context = memory.getConversationContext();
  const infrastructure = memory.getInfrastructureSummary();
  
  const prompts = [];
  
  // Add context-aware prompts based on current state
  if (context.escalationLevel === 'critical') {
    prompts.push(
      'What is the current status of the critical incident?',
      'Show me the incident resolution timeline',
      'What are the immediate next steps for this critical issue?'
    );
  }
  
  if (context.commonIssues.length > 0) {
    const topIssue = context.commonIssues[0][0];
    prompts.push(
      `How can I prevent ${topIssue} from happening again?`,
      `Show me the resolution history for ${topIssue}`,
      `What patterns do you see with ${topIssue}?`
    );
  }
  
  if (infrastructure.activeAlerts > 0) {
    prompts.push(
      'Analyze the current alerts and prioritize them',
      'What do these alerts tell us about system health?',
      'Create an action plan for resolving active alerts'
    );
  }
  
  // Add category-specific prompts
  if (category === 'monitoring') {
    prompts.push(
      'What performance trends do you see across the infrastructure?',
      'Which nodes need attention based on historical patterns?',
      'Generate a proactive monitoring report'
    );
  }
  
  return prompts;
};

// Export memory utilities
export const memoryUtils = {
  initializeITMemory,
  memoryMiddleware,
  getMemoryEnhancedPrompts,
  
  // Helper functions
  formatMemoryForUI: (memoryData: any) => {
    return {
      ...memoryData,
      formattedTimestamp: new Date(memoryData.timestamp).toLocaleString(),
      severity: memoryData.severity || 'info',
      category: memoryData.type || 'general'
    };
  },
  
  searchMemory: async (memory: ITEnhancedMemory, query: string, filters?: any) => {
    const results = await memory.findSimilarIssues(query);
    
    // Apply filters if provided
    if (filters) {
      return results.filter(result => {
        if (filters.severity && result.severity !== filters.severity) return false;
        if (filters.category && result.type !== filters.category) return false;
        if (filters.dateRange) {
          const resultDate = new Date(result.timestamp);
          const startDate = new Date(filters.dateRange.start);
          const endDate = new Date(filters.dateRange.end);
          if (resultDate < startDate || resultDate > endDate) return false;
        }
        return true;
      });
    }
    
    return results;
  }
};

export default memoryUtils;