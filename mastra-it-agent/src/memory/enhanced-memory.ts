import { Memory } from '@mastra/memory';
import { z } from 'zod';

// Enhanced Memory Configuration for IT Infrastructure
export interface ITMemoryConfig {
  // Store system states and infrastructure context
  infrastructure: {
    nodes: Map<string, any>;
    vms: Map<string, any>;
    services: Map<string, any>;
    alerts: Array<any>;
  };
  
  // Conversation context with technical details
  conversations: {
    currentIssue?: string;
    diagnosticHistory: Array<any>;
    actionHistory: Array<any>;
    escalationLevel: 'none' | 'warning' | 'critical';
  };
  
  // Learning and patterns
  patterns: {
    commonIssues: Map<string, any>;
    resolutionHistory: Array<any>;
    performanceBaselines: Map<string, any>;
  };
}

// Memory processors for IT-specific data
export class ITMemoryProcessor {
  
  // Process system health data for memory storage
  static processSystemHealth(healthData: any) {
    return {
      type: 'system_health',
      timestamp: new Date().toISOString(),
      summary: `System health check: ${healthData.overall}`,
      data: {
        overall: healthData.overall,
        alerts: healthData.alerts?.length || 0,
        services: healthData.services,
        resources: healthData.resources
      },
      searchableText: `system health ${healthData.overall} ${JSON.stringify(healthData.alerts || [])}`,
      importance: healthData.overall === 'critical' ? 1.0 : healthData.overall === 'warning' ? 0.7 : 0.5
    };
  }
  
  // Process VM operations for memory
  static processVMOperation(vmData: any) {
    return {
      type: 'vm_operation',
      timestamp: new Date().toISOString(),
      summary: `VM ${vmData.vmid}: ${vmData.action}`,
      data: vmData,
      searchableText: `vm ${vmData.vmid} ${vmData.action} ${vmData.node || ''}`,
      importance: vmData.action === 'stop' || vmData.action === 'start' ? 0.8 : 0.6
    };
  }
  
  // Process incident data
  static processIncident(incidentData: any) {
    return {
      type: 'incident',
      timestamp: new Date().toISOString(),
      summary: `${incidentData.severity} incident: ${incidentData.description}`,
      data: incidentData,
      searchableText: `incident ${incidentData.severity} ${incidentData.description} ${incidentData.affectedSystems?.join(' ') || ''}`,
      importance: incidentData.severity === 'critical' ? 1.0 : incidentData.severity === 'high' ? 0.9 : 0.7
    };
  }
  
  // Process performance metrics
  static processMetrics(metricsData: any) {
    return {
      type: 'performance_metrics',
      timestamp: new Date().toISOString(),
      summary: `Performance metrics for ${metricsData.node}`,
      data: metricsData,
      searchableText: `metrics performance ${metricsData.node} cpu memory disk`,
      importance: 0.4
    };
  }
  
  // Process automation tasks
  static processTask(taskData: any) {
    return {
      type: 'automation_task',
      timestamp: new Date().toISOString(),
      summary: `Scheduled task: ${taskData.name}`,
      data: taskData,
      searchableText: `task automation ${taskData.name} ${taskData.type} ${taskData.schedule}`,
      importance: 0.6
    };
  }
}

// Enhanced Memory with IT-specific features
export class ITEnhancedMemory extends Memory {
  private infrastructureState: ITMemoryConfig['infrastructure'];
  private conversationContext: ITMemoryConfig['conversations'];
  private learnedPatterns: ITMemoryConfig['patterns'];
  
  constructor(config?: any) {
    super(config);
    
    this.infrastructureState = {
      nodes: new Map(),
      vms: new Map(),
      services: new Map(),
      alerts: []
    };
    
    this.conversationContext = {
      diagnosticHistory: [],
      actionHistory: [],
      escalationLevel: 'none'
    };
    
    this.learnedPatterns = {
      commonIssues: new Map(),
      resolutionHistory: [],
      performanceBaselines: new Map()
    };
  }
  
  // Store infrastructure state
  async updateInfrastructureState(type: string, id: string, data: any) {
    switch (type) {
      case 'node':
        this.infrastructureState.nodes.set(id, data);
        break;
      case 'vm':
        this.infrastructureState.vms.set(id, data);
        break;
      case 'service':
        this.infrastructureState.services.set(id, data);
        break;
    }
    
    // Store in persistent memory with processor
    const processed = ITMemoryProcessor.processSystemHealth(data);
    await this.add([{
      role: 'system',
      content: JSON.stringify(processed)
    }]);
  }
  
  // Add incident to memory with context
  async recordIncident(incident: any) {
    this.conversationContext.diagnosticHistory.push(incident);
    this.conversationContext.escalationLevel = incident.severity === 'critical' ? 'critical' : 
                                               incident.severity === 'high' ? 'warning' : 'none';
    
    const processed = ITMemoryProcessor.processIncident(incident);
    await this.add([{
      role: 'system',
      content: JSON.stringify(processed)
    }]);
  }
  
  // Record resolution patterns for learning
  async recordResolution(issue: string, resolution: any) {
    const pattern = {
      issue,
      resolution,
      timestamp: new Date().toISOString(),
      success: resolution.success || false
    };
    
    this.learnedPatterns.resolutionHistory.push(pattern);
    
    // Update common issues frequency
    const count = this.learnedPatterns.commonIssues.get(issue) || 0;
    this.learnedPatterns.commonIssues.set(issue, count + 1);
    
    await this.add([{
      role: 'system',
      content: JSON.stringify({
        type: 'resolution_pattern',
        summary: `Resolution recorded for: ${issue}`,
        data: pattern,
        searchableText: `resolution ${issue} ${resolution.action || ''}`,
        importance: 0.8
      })
    }]);
  }
  
  // Get relevant context for current conversation
  getConversationContext() {
    return {
      currentIssue: this.conversationContext.currentIssue,
      escalationLevel: this.conversationContext.escalationLevel,
      recentDiagnostics: this.conversationContext.diagnosticHistory.slice(-5),
      recentActions: this.conversationContext.actionHistory.slice(-10),
      commonIssues: Array.from(this.learnedPatterns.commonIssues.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
    };
  }
  
  // Get infrastructure summary
  getInfrastructureSummary() {
    return {
      totalNodes: this.infrastructureState.nodes.size,
      totalVMs: this.infrastructureState.vms.size,
      totalServices: this.infrastructureState.services.size,
      activeAlerts: this.infrastructureState.alerts.length,
      nodes: Array.from(this.infrastructureState.nodes.entries()),
      vms: Array.from(this.infrastructureState.vms.entries()),
      recentAlerts: this.infrastructureState.alerts.slice(-10)
    };
  }
  
  // Search for similar issues in memory
  async findSimilarIssues(query: string) {
    // This would use the underlying memory search functionality
    const searchResults = await this.search(query, { limit: 10 });
    
    return searchResults.filter(result => {
      try {
        const data = JSON.parse(result.content);
        return data.type === 'incident' || data.type === 'resolution_pattern';
      } catch {
        return false;
      }
    });
  }
  
  // Get performance trends
  getPerformanceTrends(node?: string) {
    const baselines = Array.from(this.learnedPatterns.performanceBaselines.entries());
    
    if (node) {
      return baselines.filter(([key]) => key.includes(node));
    }
    
    return baselines;
  }
}

// Memory visualization data structure
export interface MemoryVisualization {
  threads: Array<{
    id: string;
    title: string;
    timestamp: string;
    messageCount: number;
    type: 'conversation' | 'incident' | 'maintenance' | 'monitoring';
    severity?: 'low' | 'medium' | 'high' | 'critical';
  }>;
  
  insights: {
    totalConversations: number;
    totalIncidents: number;
    resolvedIncidents: number;
    commonIssues: Array<{ issue: string; count: number }>;
    escalationTrends: Array<{ date: string; level: string; count: number }>;
  };
  
  infrastructure: {
    nodes: Array<{ id: string; status: string; lastSeen: string }>;
    vms: Array<{ id: string; status: string; node: string; lastAction: string }>;
    alerts: Array<{ id: string; severity: string; message: string; timestamp: string }>;
  };
  
  patterns: {
    timeOfDay: Array<{ hour: number; incidents: number; resolutions: number }>;
    issueCategories: Array<{ category: string; count: number; avgResolutionTime: number }>;
    nodeHealth: Array<{ node: string; uptime: number; issueCount: number }>;
  };
}

export { ITEnhancedMemory as default };