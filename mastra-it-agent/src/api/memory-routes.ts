import { Router } from 'express';
import { ITEnhancedMemory } from '../memory/enhanced-memory.js';

export const createMemoryRoutes = (memory: ITEnhancedMemory) => {
  const router = Router();

  // Get memory status
  router.get('/status', async (req, res) => {
    try {
      res.json({
        result: true,
        provider: 'ITEnhancedMemory',
        status: 'active'
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get conversation threads
  router.get('/threads', async (req, res) => {
    try {
      const { agentId, resourceid } = req.query;
      
      // Mock thread data - in real implementation, get from memory
      const threads = [
        {
          id: 'thread-1',
          title: 'System Health Investigation',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          messageCount: 15
        },
        {
          id: 'thread-2',
          title: 'VM Deployment Assistance', 
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          updatedAt: new Date(Date.now() - 1800000).toISOString(),
          messageCount: 8
        },
        {
          id: 'thread-3',
          title: 'Network Troubleshooting',
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          updatedAt: new Date(Date.now() - 3600000).toISOString(),
          messageCount: 12
        }
      ];

      res.json(threads);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get messages for a specific thread
  router.get('/threads/:threadId/messages', async (req, res) => {
    try {
      const { threadId } = req.params;
      const { agentId } = req.query;

      // Mock message data - in real implementation, get from memory
      const messages = [
        {
          id: '1',
          role: 'user',
          content: 'Check the system health across all nodes',
          timestamp: new Date(Date.now() - 300000).toISOString()
        },
        {
          id: '2',
          role: 'assistant',
          content: 'I\'ll check the system health across all nodes. Let me run a comprehensive health assessment.',
          timestamp: new Date(Date.now() - 295000).toISOString()
        },
        {
          id: '3',
          role: 'system',
          content: JSON.stringify({
            type: 'tool_call',
            tool: 'checkSystemHealth',
            result: {
              overall: 'healthy',
              services: { status: 'all_running' },
              resources: { cpu: '25%', memory: '60%' }
            }
          }),
          timestamp: new Date(Date.now() - 290000).toISOString()
        }
      ];

      const uiMessages = messages.map(msg => ({
        ...msg,
        role: msg.role as 'user' | 'assistant' | 'system'
      }));

      res.json({
        uiMessages,
        messages
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Delete a thread
  router.delete('/threads/:threadId', async (req, res) => {
    try {
      const { threadId } = req.params;
      
      // In real implementation, delete from memory
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get memory visualization data
  router.get('/visualization', async (req, res) => {
    try {
      const context = memory.getConversationContext();
      const infrastructure = memory.getInfrastructureSummary();
      
      const visualization = {
        threads: [
          {
            id: 'thread-1',
            title: 'System Health Investigation',
            timestamp: new Date().toISOString(),
            messageCount: 15,
            type: 'incident',
            severity: 'high'
          },
          {
            id: 'thread-2',
            title: 'VM Deployment Assistance',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            messageCount: 8,
            type: 'conversation'
          }
        ],
        insights: {
          totalConversations: 45,
          totalIncidents: 12,
          resolvedIncidents: 10,
          commonIssues: context.commonIssues || [],
          escalationTrends: [
            { date: '2025-01-01', level: 'critical', count: 2 },
            { date: '2025-01-02', level: 'high', count: 3 }
          ]
        },
        infrastructure: {
          nodes: infrastructure.nodes.map(([id, data]) => ({
            id,
            status: data.status || 'unknown',
            lastSeen: data.lastSeen || new Date().toISOString()
          })),
          vms: infrastructure.vms.map(([id, data]) => ({
            id,
            status: data.status || 'unknown',
            node: data.node || 'unknown',
            lastAction: data.lastAction || 'none'
          })),
          alerts: infrastructure.recentAlerts || []
        },
        patterns: {
          timeOfDay: Array.from({ length: 24 }, (_, hour) => ({
            hour,
            incidents: Math.floor(Math.random() * 10),
            resolutions: Math.floor(Math.random() * 8)
          })),
          issueCategories: [
            { category: 'VM Issues', count: 15, avgResolutionTime: 45 },
            { category: 'Network Problems', count: 8, avgResolutionTime: 30 }
          ],
          nodeHealth: infrastructure.nodes.map(([id]) => ({
            node: id,
            uptime: 95 + Math.random() * 5,
            issueCount: Math.floor(Math.random() * 5)
          }))
        }
      };

      res.json(visualization);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Search memory
  router.post('/search', async (req, res) => {
    try {
      const { query, limit = 10 } = req.body;
      
      const results = await memory.findSimilarIssues(query);
      
      res.json({
        results: results.slice(0, limit),
        total: results.length
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get infrastructure summary
  router.get('/infrastructure', async (req, res) => {
    try {
      const summary = memory.getInfrastructureSummary();
      res.json(summary);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get conversation context
  router.get('/context', async (req, res) => {
    try {
      const context = memory.getConversationContext();
      res.json(context);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  return router;
};

export default createMemoryRoutes;