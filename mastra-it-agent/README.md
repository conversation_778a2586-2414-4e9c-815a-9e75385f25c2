# Mastra IT Infrastructure Agent

A powerful AI-driven IT infrastructure management agent built with the Mastra framework. This agent provides comprehensive monitoring, VM management, automation, and troubleshooting capabilities for IT environments.

## Features

### 🧠 Enhanced Memory & Learning
- **IT-Specific Memory**: Persistent storage of infrastructure state, incidents, and resolutions
- **Visual Memory Dashboard**: Interactive visualization of conversation threads, patterns, and insights
- **Pattern Recognition**: AI learns from past incidents to provide better future responses
- **Infrastructure Context**: Remembers node states, VM configurations, and service dependencies
- **Incident Learning**: Tracks resolution patterns and suggests similar solutions

### 🖥️ System Management
- **Health Monitoring**: Real-time system health checks and performance metrics
- **VM Management**: Complete VM lifecycle management (start, stop, restart, status)
- **Resource Monitoring**: CPU, memory, disk, and network utilization tracking
- **Log Analysis**: Intelligent log searching and analysis for troubleshooting

### 🤖 AI-Powered Agents
- **IT Infrastructure Agent**: Main agent for comprehensive IT management
- **Monitoring Agent**: Specialized for system monitoring and alerting
- **VM Management Agent**: Focused on virtual machine operations
- **Automation Agent**: Handles task scheduling and automation

### 🔧 Automation & Workflows
- **System Health Workflow**: Automated health checks with alerting
- **VM Deployment Workflow**: Streamlined VM creation and configuration
- **Backup Workflow**: Automated backup scheduling and management
- **Incident Response Workflow**: Structured incident handling and escalation
- **Maintenance Workflow**: Planned maintenance window management

### 🛠️ Available Tools
- `checkSystemHealth` - Comprehensive system health assessment
- `manageVM` - VM operations (start, stop, restart, status)
- `getSystemMetrics` - Detailed performance metrics collection
- `scheduleTask` - Automated task scheduling
- `analyzeLogs` - Log analysis and troubleshooting
- `networkDiagnostics` - Network connectivity testing

## Quick Start

### Prerequisites
- Node.js 18+ or Bun runtime
- OpenAI API key (required)
- Optional: ElevenLabs API key for voice alerts
- Optional: Proxmox access for VM management

### Installation

1. **Clone and setup**:
   ```bash
   cd mastra-it-agent
   bun install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Run the agent**:
   ```bash
   # Web UI/Playground (recommended)
   bun run playground
   
   # Demo mode
   bun start demo
   
   # Interactive chat mode
   bun start interactive
   
   # Continuous monitoring mode
   bun start monitor
   ```

## Usage Modes

### Web UI/Playground (Recommended)
```bash
bun run playground
# or
bun run ui
```
**Full-featured web interface with:**
- Interactive chat with IT agents
- **Enhanced Memory Visualization** - Visual conversation threads, incident patterns, and learned behaviors
- **Infrastructure Memory** - Persistent state tracking for nodes, VMs, and services
- **Pattern Recognition** - AI learns from past incidents and resolutions
- Visual workflow execution
- Tool testing and debugging
- Real-time system monitoring dashboard
- Network agent coordination
- Trace viewer for debugging

Access at: `http://localhost:3000`

## Memory Visualization Features

### 🧠 **Memory Dashboard**
The IT agent includes a comprehensive memory visualization system:

#### **Conversation Threads**
- **Visual Thread Management**: See all IT conversations organized by type (incidents, maintenance, monitoring)
- **Thread Categorization**: Automatic categorization by incident severity and topic
- **Message Analytics**: Track conversation patterns and resolution timelines
- **Thread Search**: Find past conversations by keywords, issues, or timeframes

#### **Infrastructure Memory**
- **Persistent State Tracking**: Remembers node statuses, VM configurations, and service states
- **Historical Context**: Tracks changes over time for trend analysis
- **Alert History**: Visual timeline of all alerts and their resolutions
- **Resource Tracking**: Persistent monitoring of CPU, memory, disk usage patterns

#### **Pattern Recognition & Learning**
- **Incident Patterns**: Identifies recurring issues and common failure modes
- **Resolution Learning**: Learns from successful resolutions and suggests similar solutions
- **Time-based Analytics**: Analyzes when incidents occur most frequently
- **Performance Baselines**: Establishes normal performance patterns for proactive alerting

#### **Visual Analytics**
- **Issue Category Analysis**: Breakdown of incident types and resolution times
- **Node Health Trends**: Visual health patterns for each infrastructure node
- **Escalation Analytics**: Track how incidents escalate and resolve over time
- **Performance Heat Maps**: Visual representation of system performance over time

### **Memory API Endpoints**
- `/api/memory/visualization` - Complete memory dashboard data
- `/api/memory/infrastructure` - Infrastructure state summary
- `/api/memory/context` - Current conversation context
- `/api/memory/search` - Search historical conversations and incidents

### Demo Mode
```bash
bun start demo
```
Demonstrates key agent capabilities with example scenarios:
- System health checking
- VM management operations
- Backup task scheduling
- Network diagnostics

### Interactive Mode
```bash
bun start interactive
```
Chat directly with the IT Infrastructure Agent:
```
IT Agent> Check the status of all VMs on node proxmox-01
IT Agent> Schedule a weekly backup for the database VMs
IT Agent> Analyze logs for any errors in the last hour
IT Agent> What's the current CPU and memory usage?
```

### Monitoring Mode
```bash
bun start monitor
```
Continuous system monitoring with automated health checks every minute.

## Configuration

### Environment Variables

**Required:**
- `OPENAI_API_KEY` - OpenAI API key for AI model access

**Optional:**
- `ELEVENLABS_API_KEY` - ElevenLabs API key for voice alerts
- `ELEVENLABS_VOICE_ID` - Voice ID for text-to-speech (default provided)
- `PROXMOX_HOST` - Proxmox server hostname/IP
- `PROXMOX_TOKEN` - Proxmox API token for VM management
- `UPSTASH_REDIS_URL` - Redis URL for conversation memory persistence
- `UPSTASH_REDIS_TOKEN` - Redis authentication token

**Monitoring:**
- `MONITOR_INTERVAL` - Monitoring interval in seconds (default: 60)
- `ALERT_THRESHOLD_CPU` - CPU alert threshold percentage (default: 80)
- `ALERT_THRESHOLD_MEMORY` - Memory alert threshold percentage (default: 90)
- `ALERT_THRESHOLD_DISK` - Disk alert threshold percentage (default: 85)

### Agent Configuration

Each agent is configured with specific instructions and capabilities:

```typescript
// Example: Get a specific agent
const agent = mastra.getAgent('itInfrastructureAgent');
const response = await agent.generate('Check system health');
```

## Agent Specializations

### IT Infrastructure Agent
**Primary Role**: Comprehensive IT management
- System monitoring and health assessment
- VM lifecycle management
- Task automation and scheduling
- Network diagnostics and troubleshooting
- Incident response and escalation

### Monitoring Agent
**Primary Role**: System monitoring and alerting
- Real-time performance metrics
- Alert generation and management
- Trend analysis and capacity planning
- Proactive issue detection

### VM Management Agent
**Primary Role**: Virtual machine operations
- VM creation and configuration
- Lifecycle management
- Resource optimization
- Snapshot and backup management
- Proxmox cluster operations

### Automation Agent
**Primary Role**: Task automation and scheduling
- Automated backup and maintenance
- System updates and patch management
- Infrastructure provisioning
- Compliance and security checks

## Example Queries

### System Health
```
"Check the overall system health including CPU, memory, and disk usage"
"Are there any failed services on the system?"
"Show me the current resource utilization across all nodes"
```

### VM Management
```
"Start VM 101 on node proxmox-01"
"What's the status of all VMs in the cluster?"
"Create a snapshot of VM 205 before the maintenance"
```

### Automation
```
"Schedule daily backups for all production VMs at 2 AM"
"Set up weekly system updates for the development environment"
"Create an automated health check that runs every 15 minutes"
```

### Troubleshooting
```
"Analyze logs for any errors in the last 24 hours"
"Run network diagnostics to google.com"
"Why is VM 303 showing high CPU usage?"
```

## Workflows

### System Health Workflow
Automated comprehensive health checking:
1. Check system services
2. Monitor resource usage
3. Analyze overall health
4. Generate alerts for issues

### VM Deployment Workflow
Streamlined VM creation:
1. Validate resources
2. Create VM
3. Apply configuration
4. Start VM (optional)
5. Create initial snapshot
6. Verify deployment

### Incident Response Workflow
Structured incident handling:
1. Assess incident severity
2. Gather diagnostics
3. Identify root cause
4. Attempt auto-remediation
5. Escalate if needed
6. Document resolution

## Development

### Project Structure
```
src/
├── mastra/
│   ├── agents/           # AI agent definitions
│   ├── tools/            # Tool implementations
│   ├── workflows/        # Workflow definitions
│   └── index.ts          # Mastra configuration
└── index.ts              # Main application entry
```

### Adding New Tools
1. Define tool in `src/mastra/tools/index.ts`
2. Add to agent tool configuration
3. Update tool exports

### Creating New Agents
1. Define agent in `src/mastra/agents/index.ts`
2. Configure instructions and capabilities
3. Export in agents collection

### Custom Workflows
1. Create workflow in `src/mastra/workflows/index.ts`
2. Define steps and conditions
3. Associate with relevant agents

## Integration

### Proxmox VE
For VM management integration:
1. Set `PROXMOX_HOST` and `PROXMOX_TOKEN`
2. Ensure network connectivity to Proxmox API
3. Configure appropriate API permissions

### Voice Alerts
For critical alert notifications:
1. Set `ELEVENLABS_API_KEY`
2. Optionally configure `ELEVENLABS_VOICE_ID`
3. Agents will use voice for critical incidents

### Memory Persistence
For conversation memory across sessions:
1. Set `UPSTASH_REDIS_URL` and `UPSTASH_REDIS_TOKEN`
2. Agents will remember context between interactions

## Troubleshooting

### Common Issues

**"Missing OPENAI_API_KEY"**
- Ensure you have set the OpenAI API key in your `.env` file

**"VM management not working"**
- Check Proxmox connectivity and API token permissions
- Without Proxmox configuration, VM tools will use mock data

**"Voice alerts not working"**
- Verify ElevenLabs API key is set correctly
- Check API quota and permissions

**"Memory not persisting"**
- Ensure Redis configuration is set up
- Without Redis, memory only persists during current session

### Debug Mode
```bash
LOG_LEVEL=debug bun start interactive
```

### Health Check
```bash
# Quick system verification
bun start demo
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## License

MIT License - See LICENSE file for details

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review environment configuration
3. Test with demo mode first
4. Check logs for error details