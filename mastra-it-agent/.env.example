# AI Model Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Voice Alerts (Optional)
ELEVENLABS_API_KEY=your_elevenlabs_api_key
ELEVENLABS_VOICE_ID=cgSgspJ2msm6clMCkdW9

# Proxmox Integration (Optional)
PROXMOX_HOST=*************
PROXMOX_PORT=8006
PROXMOX_TOKEN=your_proxmox_api_token

# Memory Persistence (Optional)
UPSTASH_REDIS_URL=your_upstash_redis_url
UPSTASH_REDIS_TOKEN=your_upstash_redis_token

# Monitoring Configuration
MONITOR_INTERVAL=60
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=90
ALERT_THRESHOLD_DISK=85

# System Configuration
NODE_ENV=production
LOG_LEVEL=info
TZ=America/New_York