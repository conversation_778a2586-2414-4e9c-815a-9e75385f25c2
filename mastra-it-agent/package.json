{"name": "mastra-it-agent", "type": "module", "version": "1.0.0", "description": "Mastra IT Infrastructure Management Agent", "main": "src/index.ts", "scripts": {"start": "bun run src/index.ts", "dev": "bun --watch src/index.ts", "build": "mastra build", "mastra:dev": "<PERSON>ra dev", "playground": "mastra dev --playground", "ui": "mastra dev --playground", "test": "bun test"}, "dependencies": {"@ai-sdk/openai": "latest", "@mastra/core": "latest", "@mastra/memory": "latest", "@mastra/voice-elevenlabs": "latest", "ai": "^4.3.16", "mastra": "latest", "zod": "^3.25.67", "node-cron": "^3.0.3", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^22.0.0", "bun-types": "latest"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mastra", "it-management", "infrastructure", "monitoring", "automation", "proxmox", "ai-agent"], "author": "IT Assistant Team", "license": "MIT"}