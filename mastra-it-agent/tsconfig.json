{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "noEmit": true, "strict": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "declaration": true, "outDir": "dist", "rootDir": "src", "resolveJsonModule": true, "types": ["bun-types"], "lib": ["ES2022", "DOM"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}