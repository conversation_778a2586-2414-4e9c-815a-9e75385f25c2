#!/bin/bash

# IT Infrastructure Agent - Web UI Launcher
echo "🚀 Starting Mastra IT Infrastructure Agent Web UI..."
echo ""

# Check if .env exists
if [ ! -f .env ]; then
    echo "⚠️  No .env file found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env with your API keys before continuing."
    echo ""
fi

# Check for required environment variables
if ! grep -q "OPENAI_API_KEY=your_openai_api_key_here" .env; then
    echo "✅ Environment configured"
else
    echo "❌ Please configure your OPENAI_API_KEY in .env file"
    echo "   Edit .env and replace 'your_openai_api_key_here' with your actual API key"
    exit 1
fi

echo "🌐 Starting web interface..."
echo "   Access at: http://localhost:3000"
echo "   API at: http://localhost:4000"
echo ""
echo "📖 Available features:"
echo "   • Interactive chat with IT agents"
echo "   • Visual workflow execution"
echo "   • Tool testing and debugging"
echo "   • Real-time monitoring dashboard"
echo "   • Memory and conversation history"
echo ""
echo "Press Ctrl+C to stop"
echo ""

# Start the Mastra development server with playground
bun run playground