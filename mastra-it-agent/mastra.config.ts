import { defineConfig } from 'mastra';

export default defineConfig({
  // Enable the built-in playground UI
  playground: {
    enabled: true,
    port: 3000,
    title: 'IT Infrastructure Agent',
    theme: 'dark'
  },
  
  // Development server configuration
  dev: {
    port: 4000,
    hot: true
  },
  
  // Build configuration
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  
  // Enable telemetry and logging
  telemetry: {
    enabled: true,
    level: 'info'
  }
});