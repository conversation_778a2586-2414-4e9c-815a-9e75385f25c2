#!/usr/bin/env node
import { spawn } from 'child_process';
import readline from 'readline';

console.log('Testing IT Assistant MCP Server...\n');

// Start the server
const server = spawn('node', ['src/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Handle server output
server.stdout.on('data', (data) => {
  const response = data.toString();
  console.log('Server response:', response);
  
  try {
    const json = JSON.parse(response);
    if (json.result) {
      console.log('\nParsed result:', JSON.stringify(json.result, null, 2));
    }
  } catch (e) {
    // Not JSON, that's okay
  }
});

server.stderr.on('data', (data) => {
  console.error('Server error:', data.toString());
});

// Send test requests
async function sendRequest(method, params = {}) {
  const request = {
    jsonrpc: '2.0',
    method,
    params,
    id: Date.now()
  };
  
  console.log('\nSending:', JSON.stringify(request, null, 2));
  server.stdin.write(JSON.stringify(request) + '\n');
}

// Test sequence
async function runTests() {
  console.log('Running MCP server tests...\n');
  
  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 1: List tools
  console.log('\n=== Test 1: List available tools ===');
  await sendRequest('tools/list');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 2: Check system health
  console.log('\n=== Test 2: Check system health ===');
  await sendRequest('tools/call', {
    name: 'check_system_health',
    arguments: {
      include_services: true,
      include_resources: true
    }
  });
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 3: List resources
  console.log('\n=== Test 3: List resources ===');
  await sendRequest('resources/list');
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('\n\nTests completed. Press Ctrl+C to exit.');
}

// Handle exit
process.on('SIGINT', () => {
  console.log('\nShutting down...');
  server.kill();
  process.exit(0);
});

// Run tests
runTests().catch(console.error);