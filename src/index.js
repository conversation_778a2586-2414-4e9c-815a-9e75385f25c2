#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import dotenv from 'dotenv';
import { SystemMonitor } from './lib/monitoring.js';
import { VMManager } from './lib/vm-manager.js';
import { IncidentManager } from './lib/incidents.js';
import { ReportGenerator } from './lib/reporting.js';
import { TaskScheduler } from './lib/automation.js';
import { VoiceAlerts } from './lib/voice-alerts.js';

// Load environment variables
dotenv.config();

class ITAssistantServer {
  constructor() {
    this.monitor = new SystemMonitor();
    this.vmManager = new VMManager();
    this.incidents = new IncidentManager();
    this.reports = new ReportGenerator();
    this.scheduler = new TaskScheduler();
    this.voice = new VoiceAlerts();
    
    this.server = new Server({
      name: 'it-assistant',
      version: '1.0.0',
      description: 'Comprehensive IT infrastructure management and monitoring',
    }, {
      capabilities: {
        tools: {},
        resources: {}
      },
    });

    this.setupHandlers();
    this.errorHandler = (error) => {
      console.error('[IT Assistant Error]', error);
      this.server.notification({
        method: 'notifications/error',
        params: { error: error.message }
      });
    };
  }

  setupHandlers() {
    // List available tools
    this.server.setRequestHandler('tools/list', async () => ({
      tools: [
        // System Monitoring Tools
        {
          name: 'check_system_health',
          description: 'Get comprehensive system health status including services, resources, and VMs',
          inputSchema: {
            type: 'object',
            properties: {
              include_services: { type: 'boolean', description: 'Include service status' },
              include_resources: { type: 'boolean', description: 'Include resource usage' },
              include_vms: { type: 'boolean', description: 'Include VM status' },
              nodes: { 
                type: 'array', 
                items: { type: 'string' },
                description: 'Specific nodes to check (default: all)'
              }
            }
          }
        },
        {
          name: 'monitor_services',
          description: 'Check status of specific services across systems',
          inputSchema: {
            type: 'object',
            properties: {
              services: {
                type: 'array',
                items: { type: 'string' },
                description: 'Service names to monitor'
              },
              nodes: {
                type: 'array',
                items: { type: 'string' },
                description: 'Nodes to check (default: all)'
              }
            },
            required: ['services']
          }
        },
        {
          name: 'get_resource_usage',
          description: 'Get detailed resource usage (CPU, memory, disk)',
          inputSchema: {
            type: 'object',
            properties: {
              node: { type: 'string', description: 'Node to check' },
              timeRange: { 
                type: 'string', 
                enum: ['1h', '6h', '24h', '7d'],
                description: 'Time range for historical data'
              }
            }
          }
        },
        {
          name: 'analyze_logs',
          description: 'Search and analyze system logs',
          inputSchema: {
            type: 'object',
            properties: {
              query: { type: 'string', description: 'Search query' },
              service: { type: 'string', description: 'Specific service logs' },
              severity: { 
                type: 'string',
                enum: ['debug', 'info', 'warning', 'error', 'critical'],
                description: 'Minimum severity level'
              },
              timeRange: { type: 'string', description: 'Time range (e.g., "1h", "24h")' }
            },
            required: ['query']
          }
        },

        // VM Management Tools
        {
          name: 'list_all_vms',
          description: 'List all VMs across all nodes with their status',
          inputSchema: {
            type: 'object',
            properties: {
              filter: {
                type: 'object',
                properties: {
                  status: { type: 'string', enum: ['running', 'stopped', 'all'] },
                  node: { type: 'string' },
                  tags: { type: 'array', items: { type: 'string' } }
                }
              }
            }
          }
        },
        {
          name: 'create_vm',
          description: 'Create a new virtual machine',
          inputSchema: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'VM name' },
              template: { type: 'string', description: 'Template to use' },
              cores: { type: 'number', description: 'Number of CPU cores' },
              memory: { type: 'number', description: 'Memory in MB' },
              disk: { type: 'number', description: 'Disk size in GB' },
              network: { type: 'string', description: 'Network configuration' },
              node: { type: 'string', description: 'Target node' }
            },
            required: ['name', 'template']
          }
        },
        {
          name: 'manage_vm',
          description: 'Start, stop, restart, or modify a VM',
          inputSchema: {
            type: 'object',
            properties: {
              vmid: { type: 'string', description: 'VM ID' },
              action: { 
                type: 'string',
                enum: ['start', 'stop', 'restart', 'shutdown', 'suspend', 'resume'],
                description: 'Action to perform'
              },
              node: { type: 'string', description: 'Node where VM is located' }
            },
            required: ['vmid', 'action']
          }
        },
        {
          name: 'snapshot_vm',
          description: 'Create or manage VM snapshots',
          inputSchema: {
            type: 'object',
            properties: {
              vmid: { type: 'string', description: 'VM ID' },
              action: {
                type: 'string',
                enum: ['create', 'restore', 'delete', 'list'],
                description: 'Snapshot action'
              },
              name: { type: 'string', description: 'Snapshot name' },
              description: { type: 'string', description: 'Snapshot description' }
            },
            required: ['vmid', 'action']
          }
        },

        // Automation Tools
        {
          name: 'schedule_task',
          description: 'Schedule automated IT tasks',
          inputSchema: {
            type: 'object',
            properties: {
              name: { type: 'string', description: 'Task name' },
              type: {
                type: 'string',
                enum: ['backup', 'update', 'maintenance', 'custom'],
                description: 'Task type'
              },
              schedule: { type: 'string', description: 'Cron schedule expression' },
              targets: {
                type: 'array',
                items: { type: 'string' },
                description: 'Target systems or VMs'
              },
              script: { type: 'string', description: 'Script to execute (for custom tasks)' },
              enabled: { type: 'boolean', default: true }
            },
            required: ['name', 'type', 'schedule']
          }
        },
        {
          name: 'run_playbook',
          description: 'Execute automation playbook',
          inputSchema: {
            type: 'object',
            properties: {
              playbook: { type: 'string', description: 'Playbook name or path' },
              targets: {
                type: 'array',
                items: { type: 'string' },
                description: 'Target systems'
              },
              variables: {
                type: 'object',
                description: 'Variables to pass to playbook'
              },
              dryRun: { type: 'boolean', description: 'Perform dry run' }
            },
            required: ['playbook']
          }
        },
        {
          name: 'batch_execute',
          description: 'Execute commands on multiple systems',
          inputSchema: {
            type: 'object',
            properties: {
              command: { type: 'string', description: 'Command to execute' },
              targets: {
                type: 'array',
                items: { type: 'string' },
                description: 'Target systems'
              },
              parallel: { type: 'boolean', default: true },
              timeout: { type: 'number', description: 'Timeout in seconds' }
            },
            required: ['command', 'targets']
          }
        },

        // Incident Response Tools
        {
          name: 'diagnose_issue',
          description: 'Automated troubleshooting for common issues',
          inputSchema: {
            type: 'object',
            properties: {
              symptom: { type: 'string', description: 'Problem description' },
              system: { type: 'string', description: 'Affected system' },
              service: { type: 'string', description: 'Affected service' },
              autoFix: { type: 'boolean', description: 'Attempt automatic fix' }
            },
            required: ['symptom']
          }
        },
        {
          name: 'apply_fix',
          description: 'Apply recommended fix for an issue',
          inputSchema: {
            type: 'object',
            properties: {
              fixId: { type: 'string', description: 'Fix ID from diagnosis' },
              target: { type: 'string', description: 'Target system' },
              confirm: { type: 'boolean', description: 'Confirm before applying' }
            },
            required: ['fixId', 'target']
          }
        },
        {
          name: 'escalate_incident',
          description: 'Escalate incident to human operator',
          inputSchema: {
            type: 'object',
            properties: {
              title: { type: 'string', description: 'Incident title' },
              description: { type: 'string', description: 'Detailed description' },
              severity: {
                type: 'string',
                enum: ['low', 'medium', 'high', 'critical'],
                description: 'Incident severity'
              },
              affectedSystems: {
                type: 'array',
                items: { type: 'string' }
              },
              attachLogs: { type: 'boolean', default: true }
            },
            required: ['title', 'description', 'severity']
          }
        },

        // Reporting Tools
        {
          name: 'generate_report',
          description: 'Generate IT infrastructure reports',
          inputSchema: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['daily', 'weekly', 'monthly', 'custom'],
                description: 'Report type'
              },
              include_metrics: { type: 'boolean', default: true },
              include_incidents: { type: 'boolean', default: true },
              include_compliance: { type: 'boolean', default: false },
              format: {
                type: 'string',
                enum: ['pdf', 'html', 'json', 'markdown'],
                default: 'pdf'
              },
              send_voice_summary: { type: 'boolean', default: false },
              recipients: {
                type: 'array',
                items: { type: 'string' }
              }
            },
            required: ['type']
          }
        },

        // Voice Alert Tools
        {
          name: 'send_voice_alert',
          description: 'Send voice alert for critical issues',
          inputSchema: {
            type: 'object',
            properties: {
              severity: {
                type: 'string',
                enum: ['info', 'warning', 'critical'],
                description: 'Alert severity'
              },
              message: { type: 'string', description: 'Alert message' },
              recipients: {
                type: 'array',
                items: { type: 'string' },
                description: 'Recipient identifiers'
              },
              repeat: { type: 'number', description: 'Times to repeat' }
            },
            required: ['severity', 'message']
          }
        },
        {
          name: 'status_report',
          description: 'Generate and send voice status report',
          inputSchema: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['quick', 'detailed', 'critical_only'],
                default: 'quick'
              },
              voice_id: { type: 'string', description: 'ElevenLabs voice ID' }
            }
          }
        }
      ],
    }));

    // Handle tool calls
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          // System Monitoring
          case 'check_system_health':
            return await this.monitor.checkSystemHealth(args);
          case 'monitor_services':
            return await this.monitor.checkServices(args);
          case 'get_resource_usage':
            return await this.monitor.getResourceUsage(args);
          case 'analyze_logs':
            return await this.monitor.analyzeLogs(args);

          // VM Management
          case 'list_all_vms':
            return await this.vmManager.listAllVMs(args);
          case 'create_vm':
            return await this.vmManager.createVM(args);
          case 'manage_vm':
            return await this.vmManager.manageVM(args);
          case 'snapshot_vm':
            return await this.vmManager.snapshotVM(args);

          // Automation
          case 'schedule_task':
            return await this.scheduler.scheduleTask(args);
          case 'run_playbook':
            return await this.scheduler.runPlaybook(args);
          case 'batch_execute':
            return await this.scheduler.batchExecute(args);

          // Incident Response
          case 'diagnose_issue':
            return await this.incidents.diagnoseIssue(args);
          case 'apply_fix':
            return await this.incidents.applyFix(args);
          case 'escalate_incident':
            return await this.incidents.escalateIncident(args);

          // Reporting
          case 'generate_report':
            return await this.reports.generateReport(args);

          // Voice Alerts
          case 'send_voice_alert':
            return await this.voice.sendAlert(args);
          case 'status_report':
            return await this.voice.generateStatusReport(args);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(`Error in tool ${name}:`, error);
        return {
          content: [{
            type: 'text',
            text: `Error executing ${name}: ${error.message}`
          }],
          isError: true,
        };
      }
    });

    // Resource handlers
    this.server.setRequestHandler('resources/list', async () => ({
      resources: [
        {
          uri: 'it-assistant://documentation',
          name: 'IT Assistant Documentation',
          mimeType: 'text/markdown',
          description: 'Complete documentation for IT Assistant MCP'
        },
        {
          uri: 'it-assistant://playbooks',
          name: 'Automation Playbooks',
          mimeType: 'application/json',
          description: 'Available automation playbooks'
        },
        {
          uri: 'it-assistant://templates',
          name: 'Report Templates',
          mimeType: 'application/json',
          description: 'Available report templates'
        },
        {
          uri: 'it-assistant://proxmox-scripts',
          name: 'Proxmox Community Scripts',
          mimeType: 'text/markdown',
          description: 'Community scripts for Proxmox VE automation and container deployment'
        },
        {
          uri: 'it-assistant://deployment-prompts',
          name: 'Deployment Prompt Templates',
          mimeType: 'text/markdown',
          description: 'Ready-to-use prompts for common IT infrastructure deployments'
        },
        // ALIAS Infrastructure Ontology Resources
        {
          uri: 'alias://infrastructure/ontology',
          name: 'ALIAS Infrastructure Ontology',
          mimeType: 'text/markdown',
          description: 'Complete knowledge base with all configurations, scripts, and commands'
        },
        {
          uri: 'alias://infrastructure/commands',
          name: 'Infrastructure Command Reference',
          mimeType: 'text/markdown',
          description: 'Quick reference for all Proxmox, Kubernetes, and service commands'
        },
        {
          uri: 'alias://infrastructure/scripts',
          name: 'Infrastructure Scripts Library',
          mimeType: 'text/markdown',
          description: 'Ready-to-use automation scripts for common tasks'
        },
        {
          uri: 'alias://infrastructure/emergency',
          name: 'Emergency Procedures Guide',
          mimeType: 'text/markdown',
          description: 'Critical procedures for system recovery and incident response'
        },
        {
          uri: 'alias://infrastructure/terraform',
          name: 'Terraform IaC Configuration',
          mimeType: 'text/markdown',
          description: 'Complete Terraform modules for infrastructure deployment'
        },
        {
          uri: 'alias://prompts/infrastructure',
          name: 'Infrastructure Management Prompts',
          mimeType: 'text/markdown',
          description: 'AI prompts for common infrastructure tasks and troubleshooting'
        }
      ]
    }));

    this.server.setRequestHandler('resources/read', async (request) => {
      const { uri } = request.params;
      
      switch (uri) {
        case 'it-assistant://documentation':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getDocumentation()
            }]
          };
        case 'it-assistant://playbooks':
          return {
            contents: [{
              uri,
              mimeType: 'application/json',
              text: JSON.stringify(await this.scheduler.listPlaybooks(), null, 2)
            }]
          };
        case 'it-assistant://templates':
          return {
            contents: [{
              uri,
              mimeType: 'application/json',
              text: JSON.stringify(await this.reports.listTemplates(), null, 2)
            }]
          };
        case 'it-assistant://proxmox-scripts':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getProxmoxScripts()
            }]
          };
        case 'it-assistant://deployment-prompts':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getDeploymentPrompts()
            }]
          };
        // ALIAS Infrastructure Ontology Resources
        case 'alias://infrastructure/ontology':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getInfrastructureOntology()
            }]
          };
        case 'alias://infrastructure/commands':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getCommandReference()
            }]
          };
        case 'alias://infrastructure/scripts':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getScriptsLibrary()
            }]
          };
        case 'alias://infrastructure/emergency':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getEmergencyProcedures()
            }]
          };
        case 'alias://infrastructure/terraform':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getTerraformConfig()
            }]
          };
        case 'alias://prompts/infrastructure':
          return {
            contents: [{
              uri,
              mimeType: 'text/markdown',
              text: await this.getInfrastructurePrompts()
            }]
          };
        default:
          throw new Error(`Unknown resource: ${uri}`);
      }
    });
  }

  async getDocumentation() {
    return `# IT Assistant MCP Server

## Overview
The IT Assistant MCP provides comprehensive IT infrastructure management capabilities including:
- System monitoring and alerting
- VM lifecycle management
- Automated task scheduling
- Incident response and troubleshooting
- Report generation
- Voice alerts and notifications

## Key Features

### System Monitoring
Real-time monitoring of servers, services, and resources with customizable alert thresholds.

### VM Management
Full VM lifecycle management through Proxmox integration including creation, snapshots, and resource management.

### Automation
Schedule and execute automated tasks, run playbooks, and perform batch operations across multiple systems.

### Incident Response
Automated troubleshooting, fix application, and incident escalation with full tracking.

### Voice Integration
Critical alerts and status reports delivered via ElevenLabs text-to-speech for immediate attention.

## Configuration
See README.md for detailed configuration options and environment variables.`;
  }

  async getProxmoxScripts() {
    return `# Proxmox Community Scripts

## Overview
Community-maintained scripts for Proxmox VE automation from https://github.com/community-scripts/ProxmoxVE

## Quick Deployment Command
\`\`\`bash
bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/<script-name>.sh)"
\`\`\`

## Essential IT Infrastructure Scripts

### Monitoring & Observability
- **prometheus** - Time-series metrics collection
- **grafana** - Metrics visualization and dashboards
- **zabbix** - Enterprise monitoring solution
- **graylog** - Centralized log management
- **uptimekuma** - Uptime monitoring with beautiful UI
- **changedetection** - Website change monitoring

### Security & Networking
- **wireguard** - Modern VPN server
- **nginx-proxy-manager** - Easy reverse proxy with SSL
- **traefik** - Cloud-native reverse proxy
- **pihole** - Network-wide ad blocking
- **adguard** - Advanced ad blocking with DNS
- **cloudflared** - Cloudflare tunnel for secure access

### Development & CI/CD
- **docker** - Container runtime
- **podman** - Rootless container engine
- **jenkins** - CI/CD automation server
- **gitea** - Lightweight Git hosting
- **gitlab** - Complete DevOps platform

### Databases
- **postgresql** - Advanced SQL database
- **mysql** - Popular SQL database
- **mariadb** - MySQL fork
- **redis** - In-memory data store
- **influxdb** - Time-series database

### IT Services
- **nextcloud** - File sharing and collaboration
- **vaultwarden** - Bitwarden-compatible password manager
- **paperless-ngx** - Document management system
- **bookstack** - Documentation wiki
- **n8n** - Workflow automation

### Home Automation
- **homeassistant** - Smart home platform
- **mqtt** - Message broker for IoT
- **esphome** - ESP device management
- **zigbee2mqtt** - Zigbee device bridge

## VM Scripts

### Operating Systems
- **ubuntu-24.04** - Latest Ubuntu LTS
- **debian-12** - Stable Debian
- **alpine** - Lightweight Linux

### Network Appliances
- **opnsense** - Firewall and router
- **openwrt** - Router firmware
- **mikrotik** - RouterOS CHR

### Specialized VMs
- **haos** - Home Assistant Operating System
- **windows-11** - Debloated Windows 11
- **macos** - macOS virtualization

## Script Features
- Interactive installation with customization
- Automatic updates via menu system
- Post-installation configuration
- Resource optimization
- Systemd service management

## Best Practices
1. Always run scripts on Proxmox host (not in containers)
2. Review script parameters before execution
3. Use LXC containers when possible (lower overhead)
4. Enable automatic updates for security
5. Configure backups after deployment`;
  }

  async getDeploymentPrompts() {
    return `# IT Infrastructure Deployment Prompts

## Monitoring Stack Deployment
\`\`\`
Deploy a complete monitoring stack on Proxmox:
1. Create Prometheus container for metrics collection
2. Create Grafana container for visualization
3. Configure Prometheus to scrape Proxmox metrics
4. Set up Grafana dashboards for VM and host monitoring
5. Create alerts for CPU > 80%, Memory > 90%, Disk > 85%
\`\`\`

## Security Infrastructure
\`\`\`
Set up secure remote access infrastructure:
1. Deploy Wireguard VPN server container
2. Configure Nginx Proxy Manager for reverse proxy
3. Set up Cloudflared tunnel for zero-trust access
4. Deploy Pi-hole for network-wide ad blocking
5. Configure firewall rules for security
\`\`\`

## Development Platform
\`\`\`
Create a complete development environment:
1. Deploy GitLab or Gitea for code hosting
2. Set up Jenkins for CI/CD pipelines
3. Create Docker/Podman container for builds
4. Deploy PostgreSQL and Redis for applications
5. Configure automated backups for all services
\`\`\`

## IT Service Desk
\`\`\`
Deploy IT service management tools:
1. Create Nextcloud for file sharing
2. Deploy Vaultwarden for password management
3. Set up Bookstack for documentation
4. Deploy Paperless-NGX for document management
5. Configure LDAP/SSO integration
\`\`\`

## Backup & Recovery System
\`\`\`
Implement comprehensive backup solution:
1. Schedule daily VM snapshots for all containers
2. Set up remote backup to NAS/cloud storage
3. Configure 3-2-1 backup strategy
4. Test restore procedures monthly
5. Create disaster recovery documentation
\`\`\`

## Home Lab Setup
\`\`\`
Deploy a complete home lab environment:
1. Create Home Assistant OS VM for automation
2. Deploy MQTT broker for IoT devices
3. Set up Plex/Jellyfin for media streaming
4. Configure Nextcloud for personal cloud
5. Implement monitoring with Uptime Kuma
\`\`\`

## Log Management System
\`\`\`
Set up centralized logging:
1. Deploy Graylog for log collection
2. Configure all VMs to send logs to Graylog
3. Set up log retention policies
4. Create dashboards for error tracking
5. Configure alerts for critical errors
\`\`\`

## Network Monitoring
\`\`\`
Implement network monitoring solution:
1. Deploy Zabbix for comprehensive monitoring
2. Configure SNMP on all network devices
3. Set up network maps and topology
4. Create bandwidth monitoring dashboards
5. Configure alerts for network issues
\`\`\`

## High Availability Setup
\`\`\`
Create HA infrastructure:
1. Set up multiple Proxmox nodes in cluster
2. Configure shared storage (Ceph/NFS)
3. Enable HA for critical VMs
4. Set up automatic failover
5. Test failover scenarios
\`\`\`

## Disaster Recovery Plan
\`\`\`
Implement DR procedures:
1. Document all infrastructure components
2. Create automated backup verification
3. Set up off-site backup replication
4. Create runbooks for common failures
5. Schedule quarterly DR drills
\`\`\`

## Quick Commands

### Deploy Container
\`\`\`
bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/[script].sh)"
\`\`\`

### Create VM from Template
\`\`\`
qm clone [template-id] [new-vm-id] --name [vm-name] --full
\`\`\`

### Batch Container Creation
\`\`\`
for service in prometheus grafana nginx-proxy-manager; do
  bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/$service.sh)"
done
\`\`\`

## Integration with IT Assistant

Use these prompts with IT Assistant MCP:
1. "Deploy monitoring stack using Proxmox community scripts"
2. "Create secure remote access infrastructure"
3. "Set up automated backup system for all VMs"
4. "Configure high availability for critical services"
5. "Generate infrastructure documentation"`;
  }

  async getInfrastructureOntology() {
    const fs = require('fs').promises;
    const path = require('path');
    try {
      const ontologyPath = path.join(process.cwd(), 'ALIAS-INFRASTRUCTURE-ONTOLOGY.md');
      return await fs.readFile(ontologyPath, 'utf-8');
    } catch (error) {
      return `# ALIAS Infrastructure Ontology

Error loading full ontology: ${error.message}

Please ensure ALIAS-INFRASTRUCTURE-ONTOLOGY.md exists in the project root.`;
    }
  }

  async getCommandReference() {
    return `# Infrastructure Command Reference

## Proxmox Commands

### VM Management
\`\`\`bash
# List all VMs
qm list

# Create VM from template
qm clone 9000 <new-vmid> --name <vm-name> --full

# Start/Stop/Restart VM
qm start <vmid>
qm stop <vmid>
qm restart <vmid>

# VM Configuration
qm set <vmid> --cores 4 --memory 8192
qm resize <vmid> scsi0 +50G

# Snapshot Management
qm snapshot <vmid> <snapname>
qm rollback <vmid> <snapname>
\`\`\`

### Container Management
\`\`\`bash
# List containers
pct list

# Create container
pct create <vmid> <template> --hostname <name> --storage local-lvm

# Start/Stop container
pct start <vmid>
pct stop <vmid>
\`\`\`

## Kubernetes Commands

### Cluster Management
\`\`\`bash
# Context switching
kubectl config get-contexts
kubectl config use-context production

# Node management
kubectl get nodes -o wide
kubectl drain <node-name> --ignore-daemonsets
\`\`\`

### Deployment Commands
\`\`\`bash
# Apply configurations
kubectl apply -f <file.yaml>
kubectl apply -k <kustomization-dir>/

# Rollout management
kubectl rollout status deployment/<name> -n <namespace>
kubectl rollout restart deployment/<name> -n <namespace>
\`\`\`

## Quick Commands
\`\`\`bash
# VM Operations
qm list                          # List VMs
qm start/stop/restart <vmid>     # VM control
vzdump <vmid> --mode snapshot    # Backup VM

# Kubernetes
kubectl get pods -A              # All pods
kubectl logs -f <pod> -n <ns>    # Follow logs
kubectl exec -it <pod> -n <ns> -- /bin/bash

# Docker
docker ps -a                     # List containers
docker logs -f <container>       # Follow logs
docker system prune -a           # Clean up
\`\`\``;
  }

  async getScriptsLibrary() {
    return `# Infrastructure Scripts Library

## Network Setup Script
\`\`\`bash
#!/bin/bash
# Configure VLANs and bridges on Proxmox

VLANS=(10 20 30 40 50 60 70 90)
NETWORKS=(
  "**********/24"  # Management
  "**********/24"  # Production
  "**********/24"  # Staging
  "**********/24"  # Development
  "**********/24"  # DMZ
  "**********/24"  # Storage
  "**********/24"  # Monitoring
  "**********/24"  # Guest
)

for i in \${!VLANS[@]}; do
  vlan=\${VLANS[$i]}
  network=\${NETWORKS[$i]}
  gateway=$(echo $network | cut -d'/' -f1 | sed 's/\\.0$/\\.1/')
  
  cat >> /etc/network/interfaces <<EOF

auto vmbr\${vlan}
iface vmbr\${vlan} inet static
    address \${gateway}/24
    bridge-ports eno1.\${vlan}
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
EOF
done

systemctl restart networking
\`\`\`

## Health Check Script
\`\`\`bash
#!/bin/bash
# Comprehensive health check

# Colors
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
NC='\\033[0m'

# Check Proxmox services
echo "Checking Proxmox Services..."
for service in pve-cluster pvedaemon pveproxy pvestatd; do
  if systemctl is-active --quiet $service; then
    echo -e "\${GREEN}✓\${NC} $service is running"
  else
    echo -e "\${RED}✗\${NC} $service is not running"
  fi
done

# Check storage usage
echo -e "\\nChecking Storage Usage..."
df -h | grep -E '^/dev/' | while read line; do
  usage=$(echo $line | awk '{print $5}' | sed 's/%//')
  mount=$(echo $line | awk '{print $6}')
  
  if [ $usage -gt 80 ]; then
    echo -e "\${RED}✗\${NC} $mount is $usage% full"
  elif [ $usage -gt 60 ]; then
    echo -e "\${YELLOW}!\${NC} $mount is $usage% full"
  else
    echo -e "\${GREEN}✓\${NC} $mount is $usage% full"
  fi
done
\`\`\`

## Backup Automation Script
\`\`\`bash
#!/bin/bash
# Automated backup with retention management

BACKUP_STORAGE="pbs-backup"
RETENTION_DAYS=30
EMAIL="<EMAIL>"

# Get all VMs
VMS=$(qm list | awk 'NR>1 {print $1}')

# Backup each VM
for VM in $VMS; do
  echo "Backing up VM $VM..."
  vzdump $VM \\\\
    --compress zstd \\\\
    --storage $BACKUP_STORAGE \\\\
    --mode snapshot \\\\
    --mailto $EMAIL
done

# Clean old backups
find /var/lib/vz/dump/ -name "*.vma.zst" -mtime +$RETENTION_DAYS -delete
\`\`\``;
  }

  async getEmergencyProcedures() {
    return `# Emergency Procedures Guide

## System Recovery

### Proxmox Host Recovery
\`\`\`bash
# Boot from Proxmox ISO
# Select "Install Proxmox VE (Debug mode)"

# Mount existing installation
mkdir /mnt/pve
mount /dev/mapper/pve-root /mnt/pve

# Backup configuration
cp -R /mnt/pve/etc/pve /root/pve-backup

# Repair installation
proxmox-boot-tool refresh
update-grub
\`\`\`

### Kubernetes Cluster Recovery
\`\`\`bash
# Backup etcd
ETCDCTL_API=3 etcdctl \\\\
  --endpoints=https://127.0.0.1:2379 \\\\
  --cacert=/etc/kubernetes/pki/etcd/ca.crt \\\\
  --cert=/etc/kubernetes/pki/etcd/server.crt \\\\
  --key=/etc/kubernetes/pki/etcd/server.key \\\\
  snapshot save /backup/etcd-snapshot.db

# Restore etcd
ETCDCTL_API=3 etcdctl \\\\
  snapshot restore /backup/etcd-snapshot.db \\\\
  --data-dir=/var/lib/etcd-restore
\`\`\`

## Network Emergencies

### VLAN Isolation Breach
\`\`\`bash
# Immediate isolation
iptables -I FORWARD -i vmbr20 -o vmbr10 -j DROP
iptables -I FORWARD -i vmbr10 -o vmbr20 -j DROP

# Investigate
tcpdump -i vmbr20 -w breach-capture.pcap
\`\`\`

### DDoS Mitigation
\`\`\`bash
# Rate limiting
iptables -A INPUT -p tcp --dport 443 -m limit --limit 100/minute --limit-burst 200 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j DROP

# Block source
iptables -A INPUT -s <attacker-ip> -j DROP
\`\`\`

## Critical Contacts
- Infrastructure Team: <EMAIL>
- Security Team: <EMAIL>
- On-Call: +1-XXX-XXX-XXXX`;
  }

  async getTerraformConfig() {
    return `# Terraform IaC Configuration

## Quick Start
\`\`\`bash
# Initialize Terraform
terraform init

# Plan deployment
terraform plan

# Deploy infrastructure
terraform apply
\`\`\`

## Module Structure
\`\`\`
modules/
├── network/          # VLAN and network configuration
├── kubernetes/       # K8s/K3s cluster deployment
├── core-services/    # GitLab, Harbor, Vault, Keycloak
└── monitoring/       # Prometheus, Grafana, ELK stack
\`\`\`

## Resource Allocation
- **Total VMs**: 20
- **Total vCPUs**: 88
- **Total RAM**: 172GB
- **Total Storage**: ~4TB

## Key Variables
\`\`\`hcl
variable "proxmox_token_id" {
  description = "Proxmox API Token ID"
  type        = string
  default     = "root@pam!terraform"
}

variable "proxmox_token_secret" {
  description = "Proxmox API Token Secret"
  type        = string
  sensitive   = true
}
\`\`\`

## Deployment Commands
\`\`\`bash
# Deploy specific modules
terraform apply -target=module.network
terraform apply -target=module.kubernetes
terraform apply -target=module.core_services
terraform apply -target=module.monitoring

# Destroy resources
terraform destroy -target=module.monitoring
\`\`\``;
  }

  async getInfrastructurePrompts() {
    return `# Infrastructure Management Prompts

## System Health Check
"Check the health of all Proxmox nodes and VMs, including CPU, memory, and disk usage. Alert me to any issues above 80% utilization."

## VM Deployment
"Create a new Ubuntu 22.04 VM with 4 cores, 8GB RAM, and 100GB disk. Configure it with cloud-init and add my SSH key for access."

## Kubernetes Cluster Status
"Check the status of all Kubernetes clusters. Show me node health, pod count, and any failing deployments across production, staging, and development."

## Backup Verification
"Verify that all VMs have been backed up in the last 24 hours. List any VMs without recent backups and schedule immediate backups for them."

## Security Audit
"Run a security audit on the infrastructure. Check for:
- Failed login attempts in the last 24 hours
- Open ports that shouldn't be exposed
- VMs without recent security updates
- Expired SSL certificates"

## Performance Analysis
"Analyze performance metrics for the last 7 days. Identify:
- VMs with consistent high CPU usage
- Memory bottlenecks
- Disk I/O issues
- Network latency problems"

## Incident Response
"A production service is down. Please:
1. Check the health of all production VMs
2. Analyze recent logs for errors
3. Identify potential root causes
4. Suggest remediation steps
5. Create an incident report"

## Infrastructure Deployment
"Deploy a complete monitoring stack:
1. Create Prometheus container for metrics
2. Create Grafana container for visualization
3. Configure data sources and dashboards
4. Set up alerts for critical metrics
5. Test the entire monitoring pipeline"

## Disaster Recovery Test
"Initiate a disaster recovery test:
1. Create snapshots of critical VMs
2. Simulate a failure scenario
3. Test restore procedures
4. Verify service functionality
5. Document the results"

## Capacity Planning
"Analyze current resource usage and predict when we'll need to expand:
- Calculate growth rate over the last 3 months
- Identify resources approaching capacity
- Recommend specific upgrades needed
- Estimate timeline for expansion"

## Automation Request
"Create an automated task to:
1. Take daily snapshots of all production VMs at 2 AM
2. Verify backup completion
3. Clean up snapshots older than 7 days
4. Send a daily report of backup status
5. Alert on any failures"

## Network Troubleshooting
"Troubleshoot network connectivity issues:
1. Test connectivity between all VLANs
2. Check firewall rules for blocking
3. Analyze network traffic patterns
4. Identify any bottlenecks
5. Suggest optimization strategies"`;
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    console.error('[IT Assistant] MCP server started successfully');
    console.error(`[IT Assistant] Connected to Proxmox at ${process.env.PROXMOX_HOST}`);
    
    // Start monitoring
    await this.monitor.startMonitoring();
  }
}

// Start the server
const server = new ITAssistantServer();
server.start().catch(console.error);