import fetch from 'node-fetch';
import https from 'https';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export class VMManager {
  constructor() {
    this.proxmoxHost = process.env.PROXMOX_HOST || '*************';
    this.proxmoxPort = process.env.PROXMOX_PORT || '8080';
    this.proxmoxToken = process.env.PROXMOX_TOKEN || '';
    
    // For development, allow self-signed certificates
    this.httpsAgent = new https.Agent({
      rejectUnauthorized: false
    });
  }

  async makeProxmoxRequest(path, method = 'GET', data = null) {
    const url = `https://${this.proxmoxHost}:${this.proxmoxPort}/api2/json${path}`;
    
    const options = {
      method,
      headers: {
        'Authorization': `PVEAPIToken=${this.proxmoxToken}`,
        'Content-Type': 'application/json'
      },
      agent: this.httpsAgent
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.errors || `Proxmox API error: ${response.status}`);
      }
      
      return result.data;
    } catch (error) {
      console.error('Proxmox API error:', error);
      throw error;
    }
  }

  async listAllVMs(options = {}) {
    try {
      // Get all nodes first
      const nodes = await this.makeProxmoxRequest('/nodes');
      
      const allVMs = [];
      
      // Get VMs from each node
      for (const node of nodes) {
        const vms = await this.makeProxmoxRequest(`/nodes/${node.node}/qemu`);
        const lxcs = await this.makeProxmoxRequest(`/nodes/${node.node}/lxc`);
        
        // Add node info to each VM
        vms.forEach(vm => {
          vm.node = node.node;
          vm.type = 'qemu';
          allVMs.push(vm);
        });
        
        lxcs.forEach(lxc => {
          lxc.node = node.node;
          lxc.type = 'lxc';
          allVMs.push(lxc);
        });
      }

      // Apply filters if provided
      let filteredVMs = allVMs;
      if (options.filter) {
        if (options.filter.status && options.filter.status !== 'all') {
          filteredVMs = filteredVMs.filter(vm => vm.status === options.filter.status);
        }
        if (options.filter.node) {
          filteredVMs = filteredVMs.filter(vm => vm.node === options.filter.node);
        }
        if (options.filter.tags && options.filter.tags.length > 0) {
          filteredVMs = filteredVMs.filter(vm => {
            const vmTags = vm.tags ? vm.tags.split(',') : [];
            return options.filter.tags.some(tag => vmTags.includes(tag));
          });
        }
      }

      const summary = {
        total: filteredVMs.length,
        running: filteredVMs.filter(vm => vm.status === 'running').length,
        stopped: filteredVMs.filter(vm => vm.status === 'stopped').length,
        nodes: [...new Set(filteredVMs.map(vm => vm.node))],
        vms: filteredVMs
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(summary, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error listing VMs: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async createVM(options) {
    const {
      name,
      template,
      cores = 1,
      memory = 1024,
      disk = 10,
      network = 'vmbr0',
      node = 'pve'
    } = options;

    try {
      // Find next available VM ID
      const vms = await this.makeProxmoxRequest(`/nodes/${node}/qemu`);
      const usedIds = vms.map(vm => parseInt(vm.vmid));
      let vmid = 100;
      while (usedIds.includes(vmid)) {
        vmid++;
      }

      // Create VM configuration
      const vmConfig = {
        vmid,
        name,
        cores,
        memory,
        scsihw: 'virtio-scsi-pci',
        ostype: 'l26', // Linux 2.6/3.x/4.x
        net0: `virtio,bridge=${network}`,
        ide2: `${node}:cloudinit`,
        boot: 'c',
        bootdisk: 'scsi0',
        serial0: 'socket',
        vga: 'serial0'
      };

      // If template is specified, clone from template
      if (template && !isNaN(template)) {
        // Clone from template
        const cloneData = {
          newid: vmid,
          name,
          full: true
        };
        
        await this.makeProxmoxRequest(
          `/nodes/${node}/qemu/${template}/clone`,
          'POST',
          cloneData
        );

        // Update VM configuration after cloning
        await this.makeProxmoxRequest(
          `/nodes/${node}/qemu/${vmid}/config`,
          'PUT',
          { cores, memory }
        );
      } else {
        // Create new VM
        await this.makeProxmoxRequest(
          `/nodes/${node}/qemu`,
          'POST',
          vmConfig
        );

        // Add disk
        const diskData = {
          size: `${disk}G`,
          storage: 'local-lvm'
        };
        
        await this.makeProxmoxRequest(
          `/nodes/${node}/qemu/${vmid}/config`,
          'PUT',
          { scsi0: `local-lvm:${disk}` }
        );
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            vmid,
            name,
            node,
            message: `VM ${name} (${vmid}) created successfully`
          }, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error creating VM: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async manageVM(options) {
    const { vmid, action, node } = options;

    try {
      let actualNode = node;
      
      // If node not specified, find which node has this VM
      if (!actualNode) {
        const nodes = await this.makeProxmoxRequest('/nodes');
        for (const n of nodes) {
          const vms = await this.makeProxmoxRequest(`/nodes/${n.node}/qemu`);
          if (vms.some(vm => vm.vmid == vmid)) {
            actualNode = n.node;
            break;
          }
        }
      }

      if (!actualNode) {
        throw new Error(`VM ${vmid} not found on any node`);
      }

      // Execute action
      const validActions = {
        'start': 'start',
        'stop': 'stop',
        'restart': 'reset',
        'shutdown': 'shutdown',
        'suspend': 'suspend',
        'resume': 'resume'
      };

      const proxmoxAction = validActions[action];
      if (!proxmoxAction) {
        throw new Error(`Invalid action: ${action}`);
      }

      await this.makeProxmoxRequest(
        `/nodes/${actualNode}/qemu/${vmid}/status/${proxmoxAction}`,
        'POST'
      );

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            vmid,
            action,
            node: actualNode,
            message: `VM ${vmid} ${action} successful`
          }, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error managing VM: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async snapshotVM(options) {
    const { vmid, action, name, description } = options;

    try {
      // Find which node has this VM
      const nodes = await this.makeProxmoxRequest('/nodes');
      let actualNode = null;
      
      for (const n of nodes) {
        const vms = await this.makeProxmoxRequest(`/nodes/${n.node}/qemu`);
        if (vms.some(vm => vm.vmid == vmid)) {
          actualNode = n.node;
          break;
        }
      }

      if (!actualNode) {
        throw new Error(`VM ${vmid} not found on any node`);
      }

      let result;

      switch (action) {
        case 'create':
          if (!name) {
            throw new Error('Snapshot name is required');
          }
          
          await this.makeProxmoxRequest(
            `/nodes/${actualNode}/qemu/${vmid}/snapshot`,
            'POST',
            { snapname: name, description }
          );
          
          result = {
            success: true,
            action: 'created',
            snapshot: name,
            message: `Snapshot ${name} created for VM ${vmid}`
          };
          break;

        case 'list':
          const snapshots = await this.makeProxmoxRequest(
            `/nodes/${actualNode}/qemu/${vmid}/snapshot`
          );
          
          result = {
            success: true,
            vmid,
            snapshots: snapshots.filter(s => s.name !== 'current')
          };
          break;

        case 'restore':
          if (!name) {
            throw new Error('Snapshot name is required');
          }
          
          await this.makeProxmoxRequest(
            `/nodes/${actualNode}/qemu/${vmid}/snapshot/${name}/rollback`,
            'POST'
          );
          
          result = {
            success: true,
            action: 'restored',
            snapshot: name,
            message: `VM ${vmid} restored to snapshot ${name}`
          };
          break;

        case 'delete':
          if (!name) {
            throw new Error('Snapshot name is required');
          }
          
          await this.makeProxmoxRequest(
            `/nodes/${actualNode}/qemu/${vmid}/snapshot/${name}`,
            'DELETE'
          );
          
          result = {
            success: true,
            action: 'deleted',
            snapshot: name,
            message: `Snapshot ${name} deleted from VM ${vmid}`
          };
          break;

        default:
          throw new Error(`Invalid snapshot action: ${action}`);
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error managing snapshot: ${error.message}`
        }],
        isError: true
      };
    }
  }
}