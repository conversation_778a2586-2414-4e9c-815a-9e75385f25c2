import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export class VoiceAlerts {
  constructor() {
    this.alertHistory = [];
    this.voiceProfiles = new Map();
    this.initializeVoiceProfiles();
  }

  initializeVoiceProfiles() {
    // Voice profiles for different alert types
    this.voiceProfiles.set('critical', {
      voice_id: process.env.ELEVENLABS_CRITICAL_VOICE || 'cgSgspJ2msm6clMCkdW9',
      stability: 0.8,
      similarity_boost: 0.9,
      style: 0.5,
      use_speaker_boost: true,
      speed: 1.1
    });

    this.voiceProfiles.set('warning', {
      voice_id: process.env.ELEVENLABS_WARNING_VOICE || 'cgSgspJ2msm6clMCkdW9',
      stability: 0.7,
      similarity_boost: 0.8,
      style: 0.3,
      use_speaker_boost: true,
      speed: 1.0
    });

    this.voiceProfiles.set('info', {
      voice_id: process.env.ELEVENLABS_INFO_VOICE || 'cgSgspJ2msm6clMCkdW9',
      stability: 0.6,
      similarity_boost: 0.7,
      style: 0.2,
      use_speaker_boost: false,
      speed: 0.95
    });
  }

  async sendAlert(options) {
    const {
      severity,
      message,
      recipients = [],
      repeat = 1
    } = options;

    try {
      const alertId = `alert_${Date.now()}`;
      const alert = {
        id: alertId,
        severity,
        message,
        recipients,
        repeat,
        timestamp: new Date().toISOString(),
        status: 'sending'
      };

      // Get voice profile for severity
      const voiceProfile = this.voiceProfiles.get(severity) || this.voiceProfiles.get('info');

      // Format message for voice
      const voiceMessage = this.formatMessageForVoice(message, severity);

      // In production, would integrate with ElevenLabs MCP
      const voiceAlert = {
        text: voiceMessage,
        voice_id: voiceProfile.voice_id,
        voice_settings: {
          stability: voiceProfile.stability,
          similarity_boost: voiceProfile.similarity_boost,
          style: voiceProfile.style,
          use_speaker_boost: voiceProfile.use_speaker_boost
        },
        speed: voiceProfile.speed,
        output_format: 'mp3_44100_128'
      };

      // Simulate sending voice alert
      alert.voiceConfig = voiceAlert;
      alert.audioFile = `alerts/alert_${alertId}.mp3`;
      
      // Handle multiple iterations
      if (repeat > 1) {
        alert.iterations = [];
        for (let i = 0; i < repeat; i++) {
          alert.iterations.push({
            iteration: i + 1,
            sent: new Date(Date.now() + i * 5000).toISOString()
          });
        }
      }

      // Handle recipients
      if (recipients.length > 0) {
        alert.delivery = recipients.map(recipient => ({
          recipient,
          method: this.getDeliveryMethod(recipient),
          status: 'sent',
          timestamp: new Date().toISOString()
        }));
      }

      alert.status = 'sent';
      this.alertHistory.push(alert);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            alertId,
            message: voiceMessage,
            severity,
            recipients,
            audioFile: alert.audioFile,
            voiceConfig: voiceAlert
          }, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error sending voice alert: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async generateStatusReport(options) {
    const { type = 'quick', voice_id } = options;

    try {
      // Gather system status data
      const statusData = await this.gatherStatusData(type);
      
      // Generate voice script
      const script = this.generateStatusScript(statusData, type);

      // Voice configuration
      const voiceConfig = {
        text: script,
        voice_id: voice_id || process.env.ELEVENLABS_VOICE_ID || 'cgSgspJ2msm6clMCkdW9',
        voice_settings: {
          stability: 0.6,
          similarity_boost: 0.7,
          style: 0.2,
          use_speaker_boost: false
        },
        speed: 0.95,
        output_format: 'mp3_44100_128'
      };

      const reportId = `report_${Date.now()}`;
      const report = {
        id: reportId,
        type,
        timestamp: new Date().toISOString(),
        script,
        voiceConfig,
        audioFile: `reports/status_report_${reportId}.mp3`,
        duration: this.estimateDuration(script)
      };

      // Save report
      await this.saveReport(report);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            reportId,
            type,
            script,
            audioFile: report.audioFile,
            estimatedDuration: report.duration,
            voiceConfig
          }, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error generating status report: ${error.message}`
        }],
        isError: true
      };
    }
  }

  formatMessageForVoice(message, severity) {
    const prefix = {
      critical: 'Critical alert! Immediate attention required. ',
      warning: 'Warning. System attention needed. ',
      info: 'Information update. '
    };

    const suffix = {
      critical: ' This is a critical issue requiring immediate response.',
      warning: ' Please investigate at your earliest convenience.',
      info: ''
    };

    return `${prefix[severity] || ''}${message}${suffix[severity] || ''}`;
  }

  getDeliveryMethod(recipient) {
    if (recipient.includes('@')) {
      return 'email';
    } else if (recipient.startsWith('+')) {
      return 'sms';
    } else if (recipient.startsWith('team:')) {
      return 'team_channel';
    } else {
      return 'direct';
    }
  }

  async gatherStatusData(type) {
    // In production, would gather real system data
    const mockData = {
      quick: {
        overall: 'healthy',
        alerts: 0,
        systems: { total: 25, healthy: 25 },
        services: { total: 150, running: 148 },
        incidents: { active: 0, resolved_today: 2 }
      },
      detailed: {
        overall: 'healthy',
        alerts: 0,
        systems: {
          total: 25,
          healthy: 23,
          warning: 2,
          critical: 0,
          details: [
            { name: 'web-01', status: 'warning', issue: 'High memory usage' },
            { name: 'db-02', status: 'warning', issue: 'Disk usage at 82%' }
          ]
        },
        services: {
          total: 150,
          running: 148,
          stopped: 2,
          critical_services: 'All critical services operational'
        },
        performance: {
          average_cpu: '35%',
          average_memory: '62%',
          average_response_time: '145ms'
        },
        incidents: {
          active: 0,
          resolved_today: 2,
          mttr: '45 minutes'
        }
      },
      critical_only: {
        critical_issues: [],
        message: 'No critical issues to report'
      }
    };

    return mockData[type] || mockData.quick;
  }

  generateStatusScript(data, type) {
    const now = new Date();
    const timeOfDay = now.getHours() < 12 ? 'morning' : 
                      now.getHours() < 17 ? 'afternoon' : 'evening';

    let script = `Good ${timeOfDay}. This is your ${type} IT infrastructure status report. `;

    switch (type) {
      case 'quick':
        script += `Overall system health is ${data.overall}. `;
        script += `We have ${data.systems.total} systems monitored, with ${data.systems.healthy} reporting healthy status. `;
        script += `${data.services.running} out of ${data.services.total} services are running normally. `;
        if (data.incidents.active > 0) {
          script += `There are ${data.incidents.active} active incidents requiring attention. `;
        } else {
          script += `There are no active incidents at this time. `;
        }
        if (data.incidents.resolved_today > 0) {
          script += `${data.incidents.resolved_today} incidents were resolved today. `;
        }
        break;

      case 'detailed':
        script += `Overall infrastructure health is ${data.overall}. `;
        
        // Systems
        script += `System status: ${data.systems.healthy} systems are healthy, `;
        if (data.systems.warning > 0) {
          script += `${data.systems.warning} systems have warnings, `;
        }
        if (data.systems.critical > 0) {
          script += `${data.systems.critical} systems are in critical state. `;
        }
        
        // Specific issues
        if (data.systems.details && data.systems.details.length > 0) {
          script += 'Specific issues include: ';
          data.systems.details.forEach(system => {
            script += `${system.name} is reporting ${system.issue}. `;
          });
        }
        
        // Services
        script += `Service status: ${data.services.running} of ${data.services.total} services are operational. `;
        script += data.services.critical_services + '. ';
        
        // Performance
        script += `Performance metrics: Average CPU usage is ${data.performance.average_cpu}, `;
        script += `memory usage is ${data.performance.average_memory}, `;
        script += `and average response time is ${data.performance.average_response_time}. `;
        
        // Incidents
        if (data.incidents.active > 0) {
          script += `There are ${data.incidents.active} active incidents. `;
        }
        if (data.incidents.resolved_today > 0) {
          script += `${data.incidents.resolved_today} incidents were resolved today with an average resolution time of ${data.incidents.mttr}. `;
        }
        break;

      case 'critical_only':
        if (data.critical_issues.length === 0) {
          script += data.message + '. All systems are operating normally.';
        } else {
          script += `There are ${data.critical_issues.length} critical issues requiring immediate attention: `;
          data.critical_issues.forEach(issue => {
            script += `${issue}. `;
          });
        }
        break;
    }

    script += ' End of status report.';
    return script;
  }

  estimateDuration(text) {
    // Estimate ~150 words per minute for speech
    const words = text.split(' ').length;
    const minutes = words / 150;
    const seconds = Math.ceil(minutes * 60);
    return `${seconds} seconds`;
  }

  async saveReport(report) {
    try {
      const reportsDir = path.join(__dirname, '../../reports');
      await fs.mkdir(reportsDir, { recursive: true });
      
      const reportPath = path.join(reportsDir, `${report.id}.json`);
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    } catch (error) {
      console.error('Error saving report:', error);
    }
  }

  async getAlertHistory(limit = 10) {
    return this.alertHistory.slice(-limit);
  }
}