import * as cron from 'node-cron';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export class TaskScheduler {
  constructor() {
    this.scheduledTasks = new Map();
    this.playbooks = new Map();
    this.taskHistory = [];
    this.initializePlaybooks();
  }

  initializePlaybooks() {
    // Common automation playbooks
    this.playbooks.set('backup_vms', {
      name: 'VM Backup Playbook',
      description: 'Automated VM backup with snapshot and export',
      steps: [
        { name: 'Pre-checks', command: 'check_disk_space', minSpace: '50GB' },
        { name: 'Create snapshots', command: 'snapshot_vms', parallel: true },
        { name: 'Export backups', command: 'export_vm_backups', destination: '/backups' },
        { name: 'Verify backups', command: 'verify_backups' },
        { name: 'Cleanup old backups', command: 'cleanup_old_backups', retention: '30d' }
      ]
    });

    this.playbooks.set('security_patching', {
      name: 'Security Patching Playbook',
      description: 'Apply security updates to systems',
      steps: [
        { name: 'Update package lists', command: 'apt-get update' },
        { name: 'Check available updates', command: 'apt list --upgradable' },
        { name: 'Create system snapshot', command: 'create_snapshot' },
        { name: 'Apply security updates', command: 'apt-get upgrade -y' },
        { name: 'Verify system health', command: 'system_health_check' },
        { name: 'Reboot if required', command: 'check_reboot_required' }
      ]
    });

    this.playbooks.set('disk_cleanup', {
      name: 'Disk Cleanup Playbook',
      description: 'Free up disk space on systems',
      steps: [
        { name: 'Analyze disk usage', command: 'df -h' },
        { name: 'Clean package cache', command: 'apt-get clean' },
        { name: 'Remove old logs', command: 'find /var/log -name "*.log" -mtime +30 -delete' },
        { name: 'Clean temp files', command: 'rm -rf /tmp/* /var/tmp/*' },
        { name: 'Remove orphaned packages', command: 'apt-get autoremove -y' },
        { name: 'Report freed space', command: 'df -h' }
      ]
    });

    this.playbooks.set('performance_optimization', {
      name: 'Performance Optimization Playbook',
      description: 'Optimize system performance',
      steps: [
        { name: 'Analyze current performance', command: 'performance_baseline' },
        { name: 'Optimize kernel parameters', command: 'apply_sysctl_tweaks' },
        { name: 'Clean memory caches', command: 'sync && echo 3 > /proc/sys/vm/drop_caches' },
        { name: 'Optimize database', command: 'optimize_databases' },
        { name: 'Restart services', command: 'restart_optimized_services' },
        { name: 'Verify improvements', command: 'performance_comparison' }
      ]
    });
  }

  async scheduleTask(options) {
    const {
      name,
      type,
      schedule,
      targets = [],
      script,
      enabled = true
    } = options;

    const taskId = `task_${Date.now()}`;
    
    try {
      const task = {
        id: taskId,
        name,
        type,
        schedule,
        targets,
        script,
        enabled,
        created: new Date().toISOString(),
        lastRun: null,
        nextRun: this.getNextRunTime(schedule),
        status: 'scheduled'
      };

      // Create cron job
      if (enabled) {
        const cronJob = cron.schedule(schedule, async () => {
          await this.executeTask(task);
        }, {
          scheduled: false
        });

        task.cronJob = cronJob;
        cronJob.start();
      }

      this.scheduledTasks.set(taskId, task);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify({
            success: true,
            taskId,
            task: {
              ...task,
              cronJob: undefined // Don't serialize cron object
            }
          }, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error scheduling task: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async runPlaybook(options) {
    const {
      playbook,
      targets = [],
      variables = {},
      dryRun = false
    } = options;

    try {
      const playbookData = this.playbooks.get(playbook);
      if (!playbookData) {
        throw new Error(`Playbook ${playbook} not found`);
      }

      const execution = {
        id: `exec_${Date.now()}`,
        playbook,
        targets,
        variables,
        dryRun,
        started: new Date().toISOString(),
        steps: [],
        status: 'running'
      };

      // Execute each step
      for (const step of playbookData.steps) {
        const stepResult = {
          name: step.name,
          command: step.command,
          started: new Date().toISOString(),
          status: 'pending'
        };

        if (dryRun) {
          stepResult.status = 'dry-run';
          stepResult.output = `Would execute: ${step.command}`;
        } else {
          try {
            // In production, would execute on target systems
            stepResult.output = `Simulated execution of: ${step.command}`;
            stepResult.status = 'completed';
          } catch (error) {
            stepResult.status = 'failed';
            stepResult.error = error.message;
            execution.status = 'failed';
            break;
          }
        }

        stepResult.completed = new Date().toISOString();
        execution.steps.push(stepResult);
      }

      if (execution.status === 'running') {
        execution.status = dryRun ? 'dry-run-completed' : 'completed';
      }

      execution.completed = new Date().toISOString();
      this.taskHistory.push(execution);

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(execution, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error running playbook: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async batchExecute(options) {
    const {
      command,
      targets,
      parallel = true,
      timeout = 300
    } = options;

    try {
      const execution = {
        id: `batch_${Date.now()}`,
        command,
        targets,
        parallel,
        timeout,
        started: new Date().toISOString(),
        results: {}
      };

      if (parallel) {
        // Execute in parallel
        const promises = targets.map(target => 
          this.executeOnTarget(command, target, timeout)
        );

        const results = await Promise.allSettled(promises);
        
        targets.forEach((target, index) => {
          const result = results[index];
          execution.results[target] = {
            status: result.status === 'fulfilled' ? 'success' : 'failed',
            output: result.value || result.reason?.message || 'Unknown error'
          };
        });
      } else {
        // Execute sequentially
        for (const target of targets) {
          try {
            const output = await this.executeOnTarget(command, target, timeout);
            execution.results[target] = {
              status: 'success',
              output
            };
          } catch (error) {
            execution.results[target] = {
              status: 'failed',
              error: error.message
            };
          }
        }
      }

      execution.completed = new Date().toISOString();
      execution.summary = {
        total: targets.length,
        successful: Object.values(execution.results).filter(r => r.status === 'success').length,
        failed: Object.values(execution.results).filter(r => r.status === 'failed').length
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(execution, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error in batch execution: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async executeTask(task) {
    task.lastRun = new Date().toISOString();
    task.status = 'running';

    try {
      let result;
      
      switch (task.type) {
        case 'backup':
          result = await this.runPlaybook({
            playbook: 'backup_vms',
            targets: task.targets
          });
          break;
          
        case 'update':
          result = await this.runPlaybook({
            playbook: 'security_patching',
            targets: task.targets
          });
          break;
          
        case 'maintenance':
          result = await this.runPlaybook({
            playbook: 'disk_cleanup',
            targets: task.targets
          });
          break;
          
        case 'custom':
          if (task.script) {
            result = await this.batchExecute({
              command: task.script,
              targets: task.targets
            });
          }
          break;
      }

      task.status = 'completed';
      task.lastResult = result;
    } catch (error) {
      task.status = 'failed';
      task.lastError = error.message;
    }

    task.nextRun = this.getNextRunTime(task.schedule);
  }

  async executeOnTarget(command, target, timeout) {
    // In production, would use SSH for remote targets
    if (target === 'localhost') {
      const { stdout, stderr } = await execAsync(command, { timeout: timeout * 1000 });
      return stdout || stderr;
    } else {
      // Simulated remote execution
      return `Would execute '${command}' on ${target}`;
    }
  }

  getNextRunTime(schedule) {
    try {
      const interval = cron.validate(schedule);
      if (!interval) {
        return 'Invalid schedule';
      }

      // Parse cron expression to get next run time
      // This is a simplified implementation
      const parts = schedule.split(' ');
      const now = new Date();
      
      if (parts[0] === '*' && parts[1] === '*') {
        // Hourly
        const next = new Date(now);
        next.setHours(next.getHours() + 1, 0, 0, 0);
        return next.toISOString();
      } else if (parts[1] === '*') {
        // Daily at specific hour
        const next = new Date(now);
        next.setDate(next.getDate() + 1);
        next.setHours(parseInt(parts[0]), 0, 0, 0);
        return next.toISOString();
      }
      
      return 'Next run calculation pending';
    } catch (error) {
      return 'Invalid schedule';
    }
  }

  async listPlaybooks() {
    const playbooks = [];
    for (const [key, playbook] of this.playbooks) {
      playbooks.push({
        id: key,
        name: playbook.name,
        description: playbook.description,
        steps: playbook.steps.length
      });
    }
    return playbooks;
  }

  async listScheduledTasks() {
    const tasks = [];
    for (const [id, task] of this.scheduledTasks) {
      tasks.push({
        id,
        name: task.name,
        type: task.type,
        schedule: task.schedule,
        enabled: task.enabled,
        status: task.status,
        lastRun: task.lastRun,
        nextRun: task.nextRun
      });
    }
    return tasks;
  }
}