import { exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import fs from 'fs/promises';

const execAsync = promisify(exec);

export class SystemMonitor {
  constructor() {
    this.alerts = [];
    this.monitoringInterval = null;
    this.thresholds = {
      cpu: 80,
      memory: 90,
      disk: 85
    };
  }

  async checkSystemHealth(options = {}) {
    const health = {
      overall: 'healthy',
      timestamp: new Date().toISOString(),
      services: {},
      resources: {},
      vms: {},
      alerts: []
    };

    try {
      if (options.include_services !== false) {
        health.services = await this.checkSystemServices();
      }

      if (options.include_resources !== false) {
        health.resources = await this.getSystemResources();
      }

      if (options.include_vms) {
        // This would integrate with Proxmox
        health.vms = await this.getVMStatus(options.nodes);
      }

      // Analyze health
      health.alerts = this.analyzeHealth(health);
      health.overall = health.alerts.length > 0 ? 
        (health.alerts.some(a => a.severity === 'critical') ? 'critical' : 'warning') : 
        'healthy';

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(health, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error checking system health: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async checkServices(options) {
    const { services, nodes = ['localhost'] } = options;
    const results = {};

    for (const node of nodes) {
      results[node] = {};
      
      for (const service of services) {
        try {
          if (node === 'localhost') {
            // Check local services
            const { stdout } = await execAsync(`systemctl is-active ${service}`);
            results[node][service] = {
              status: stdout.trim() === 'active' ? 'running' : 'stopped',
              checked: new Date().toISOString()
            };
          } else {
            // For remote nodes, would use SSH
            results[node][service] = {
              status: 'unknown',
              error: 'Remote monitoring not implemented'
            };
          }
        } catch (error) {
          results[node][service] = {
            status: 'error',
            error: error.message
          };
        }
      }
    }

    return {
      content: [{
        type: 'text',
        text: JSON.stringify(results, null, 2)
      }]
    };
  }

  async getResourceUsage(options = {}) {
    const { node = 'localhost', timeRange = '1h' } = options;
    
    try {
      const usage = {
        node,
        timestamp: new Date().toISOString(),
        timeRange,
        current: await this.getCurrentResources(),
        history: [] // Would fetch from monitoring database
      };

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(usage, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error getting resource usage: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async analyzeLogs(options) {
    const { query, service, severity = 'info', timeRange = '1h' } = options;
    
    try {
      const logs = {
        query,
        service,
        severity,
        timeRange,
        matches: [],
        summary: {}
      };

      // Simple log analysis - in production would use proper log aggregation
      if (service) {
        const { stdout } = await execAsync(
          `journalctl -u ${service} --since="${timeRange} ago" --no-pager | grep -i "${query}" | tail -100`
        );
        
        logs.matches = stdout.split('\n').filter(line => line.trim());
        logs.summary = {
          total: logs.matches.length,
          byLevel: this.categorizeLogLevels(logs.matches)
        };
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(logs, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error analyzing logs: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async startMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    // Monitor every minute
    this.monitoringInterval = setInterval(async () => {
      await this.performMonitoringCheck();
    }, 60000);

    // Initial check
    await this.performMonitoringCheck();
  }

  async performMonitoringCheck() {
    const resources = await this.getCurrentResources();
    
    // Check thresholds
    if (resources.cpu.usage > this.thresholds.cpu) {
      this.alerts.push({
        type: 'cpu',
        severity: 'warning',
        message: `CPU usage at ${resources.cpu.usage}%`,
        timestamp: new Date().toISOString()
      });
    }

    if (resources.memory.percent > this.thresholds.memory) {
      this.alerts.push({
        type: 'memory',
        severity: 'critical',
        message: `Memory usage at ${resources.memory.percent}%`,
        timestamp: new Date().toISOString()
      });
    }

    // Clean old alerts (keep last hour)
    const oneHourAgo = Date.now() - 3600000;
    this.alerts = this.alerts.filter(
      alert => new Date(alert.timestamp).getTime() > oneHourAgo
    );
  }

  async getCurrentResources() {
    const cpus = os.cpus();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    
    // Calculate CPU usage
    const cpuUsage = cpus.reduce((acc, cpu) => {
      const total = Object.values(cpu.times).reduce((a, b) => a + b);
      const idle = cpu.times.idle;
      return acc + ((total - idle) / total * 100);
    }, 0) / cpus.length;

    // Get disk usage
    let diskUsage = { available: 0, total: 0, percent: 0 };
    try {
      const { stdout } = await execAsync("df -h / | tail -1 | awk '{print $4, $2, $5}'");
      const [available, total, percent] = stdout.trim().split(' ');
      diskUsage = { available, total, percent: parseInt(percent) };
    } catch (error) {
      console.error('Error getting disk usage:', error);
    }

    return {
      cpu: {
        cores: cpus.length,
        usage: Math.round(cpuUsage),
        model: cpus[0].model
      },
      memory: {
        total: Math.round(totalMemory / 1024 / 1024 / 1024) + 'GB',
        used: Math.round(usedMemory / 1024 / 1024 / 1024) + 'GB',
        free: Math.round(freeMemory / 1024 / 1024 / 1024) + 'GB',
        percent: Math.round(usedMemory / totalMemory * 100)
      },
      disk: diskUsage,
      uptime: Math.round(os.uptime() / 3600) + ' hours',
      loadavg: os.loadavg().map(v => v.toFixed(2))
    };
  }

  async checkSystemServices() {
    const criticalServices = [
      'sshd', 'networking', 'systemd-resolved'
    ];
    
    const services = {};
    
    for (const service of criticalServices) {
      try {
        const { stdout } = await execAsync(`systemctl is-active ${service}`);
        services[service] = stdout.trim() === 'active' ? 'running' : 'stopped';
      } catch (error) {
        services[service] = 'unknown';
      }
    }
    
    return services;
  }

  async getSystemResources() {
    return await this.getCurrentResources();
  }

  async getVMStatus(nodes) {
    // This would integrate with Proxmox API
    // For now, return placeholder
    return {
      total: 0,
      running: 0,
      stopped: 0,
      vms: []
    };
  }

  analyzeHealth(health) {
    const alerts = [];
    
    // Check services
    if (health.services) {
      Object.entries(health.services).forEach(([service, status]) => {
        if (status !== 'running') {
          alerts.push({
            type: 'service',
            severity: 'critical',
            service,
            message: `Service ${service} is ${status}`
          });
        }
      });
    }
    
    // Check resources
    if (health.resources) {
      if (health.resources.cpu?.usage > this.thresholds.cpu) {
        alerts.push({
          type: 'resource',
          severity: 'warning',
          resource: 'cpu',
          message: `CPU usage at ${health.resources.cpu.usage}%`
        });
      }
      
      if (health.resources.memory?.percent > this.thresholds.memory) {
        alerts.push({
          type: 'resource',
          severity: 'critical',
          resource: 'memory',
          message: `Memory usage at ${health.resources.memory.percent}%`
        });
      }
      
      if (health.resources.disk?.percent > this.thresholds.disk) {
        alerts.push({
          type: 'resource',
          severity: 'warning',
          resource: 'disk',
          message: `Disk usage at ${health.resources.disk.percent}%`
        });
      }
    }
    
    return alerts;
  }

  categorizeLogLevels(logs) {
    const levels = {
      error: 0,
      warning: 0,
      info: 0,
      debug: 0
    };
    
    logs.forEach(log => {
      const lowerLog = log.toLowerCase();
      if (lowerLog.includes('error') || lowerLog.includes('fail')) {
        levels.error++;
      } else if (lowerLog.includes('warn')) {
        levels.warning++;
      } else if (lowerLog.includes('debug')) {
        levels.debug++;
      } else {
        levels.info++;
      }
    });
    
    return levels;
  }
}