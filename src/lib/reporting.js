import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export class ReportGenerator {
  constructor() {
    this.templates = new Map();
    this.initializeTemplates();
  }

  initializeTemplates() {
    // Report templates
    this.templates.set('daily', {
      name: 'Daily IT Report',
      sections: [
        'executive_summary',
        'system_health',
        'incidents',
        'resource_usage',
        'upcoming_maintenance'
      ]
    });

    this.templates.set('weekly', {
      name: 'Weekly IT Report',
      sections: [
        'executive_summary',
        'system_health_trends',
        'incidents_summary',
        'performance_metrics',
        'capacity_planning',
        'recommendations'
      ]
    });

    this.templates.set('monthly', {
      name: 'Monthly IT Report',
      sections: [
        'executive_summary',
        'infrastructure_overview',
        'availability_metrics',
        'incident_analysis',
        'cost_analysis',
        'capacity_trends',
        'compliance_status',
        'recommendations'
      ]
    });
  }

  async generateReport(options) {
    const {
      type,
      include_metrics = true,
      include_incidents = true,
      include_compliance = false,
      format = 'markdown',
      send_voice_summary = false,
      recipients = []
    } = options;

    try {
      const template = this.templates.get(type);
      if (!template) {
        throw new Error(`Unknown report type: ${type}`);
      }

      const report = {
        type,
        title: template.name,
        generated: new Date().toISOString(),
        period: this.getReportPeriod(type),
        sections: {}
      };

      // Generate each section
      for (const section of template.sections) {
        report.sections[section] = await this.generateSection(section, {
          include_metrics,
          include_incidents,
          include_compliance
        });
      }

      // Format report based on requested format
      let formattedReport;
      switch (format) {
        case 'markdown':
          formattedReport = this.formatMarkdown(report);
          break;
        case 'html':
          formattedReport = this.formatHTML(report);
          break;
        case 'json':
          formattedReport = JSON.stringify(report, null, 2);
          break;
        case 'pdf':
          // In production, would use a PDF library
          formattedReport = 'PDF generation would happen here';
          break;
        default:
          formattedReport = JSON.stringify(report, null, 2);
      }

      // Save report
      const filename = `${type}_report_${new Date().toISOString().split('T')[0]}.${format}`;
      const reportPath = path.join(__dirname, '../../reports', filename);
      
      try {
        await fs.mkdir(path.dirname(reportPath), { recursive: true });
        await fs.writeFile(reportPath, formattedReport);
      } catch (error) {
        console.error('Error saving report:', error);
      }

      const result = {
        success: true,
        type,
        filename,
        path: reportPath,
        recipients,
        voiceSummary: send_voice_summary
      };

      // Handle distribution
      if (recipients.length > 0) {
        result.distribution = {
          email: recipients.filter(r => r.includes('@')),
          teams: recipients.filter(r => r.startsWith('team:')),
          status: 'Would send to recipients'
        };
      }

      if (send_voice_summary) {
        result.voiceSummaryGenerated = true;
        result.voiceSummaryText = this.generateVoiceSummary(report);
      }

      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Error generating report: ${error.message}`
        }],
        isError: true
      };
    }
  }

  async generateSection(section, options) {
    // In production, would fetch real data
    const mockData = {
      executive_summary: {
        overall_health: 'Good',
        key_metrics: {
          uptime: '99.95%',
          incidents: 3,
          resolved: 3,
          average_resolution_time: '45 minutes'
        },
        highlights: [
          'Successfully completed monthly patching',
          'Migrated 5 VMs to new storage',
          'Resolved critical database issue'
        ]
      },
      system_health: {
        servers: {
          total: 25,
          healthy: 23,
          warning: 2,
          critical: 0
        },
        services: {
          total: 150,
          running: 148,
          stopped: 2
        },
        resources: {
          average_cpu: '35%',
          average_memory: '62%',
          average_disk: '58%'
        }
      },
      incidents: {
        total: 3,
        by_severity: {
          critical: 0,
          high: 1,
          medium: 2,
          low: 0
        },
        top_issues: [
          'High memory usage on DB server',
          'Network connectivity issue in DMZ',
          'Disk space warning on file server'
        ]
      },
      resource_usage: {
        cpu: {
          current: '35%',
          peak: '78%',
          trend: 'stable'
        },
        memory: {
          current: '62%',
          peak: '85%',
          trend: 'increasing'
        },
        storage: {
          used: '4.2TB',
          total: '10TB',
          trend: 'increasing'
        }
      },
      upcoming_maintenance: [
        {
          date: '2024-02-15',
          description: 'Quarterly security patching',
          impact: 'Medium',
          duration: '4 hours'
        },
        {
          date: '2024-02-20',
          description: 'Network switch firmware upgrade',
          impact: 'Low',
          duration: '2 hours'
        }
      ]
    };

    return mockData[section] || { message: `Data for ${section} would be fetched here` };
  }

  getReportPeriod(type) {
    const now = new Date();
    const periods = {
      daily: {
        start: new Date(now.setHours(0, 0, 0, 0)),
        end: new Date(now.setHours(23, 59, 59, 999))
      },
      weekly: {
        start: new Date(now.setDate(now.getDate() - 7)),
        end: new Date()
      },
      monthly: {
        start: new Date(now.getFullYear(), now.getMonth(), 1),
        end: new Date(now.getFullYear(), now.getMonth() + 1, 0)
      }
    };

    return periods[type] || periods.daily;
  }

  formatMarkdown(report) {
    let markdown = `# ${report.title}\n\n`;
    markdown += `**Generated:** ${report.generated}\n\n`;
    markdown += `**Period:** ${report.period.start.toDateString()} - ${report.period.end.toDateString()}\n\n`;

    for (const [section, data] of Object.entries(report.sections)) {
      markdown += `## ${this.formatSectionTitle(section)}\n\n`;
      markdown += this.formatSectionData(data);
      markdown += '\n\n';
    }

    return markdown;
  }

  formatHTML(report) {
    let html = `<!DOCTYPE html>
<html>
<head>
  <title>${report.title}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    h1 { color: #333; }
    h2 { color: #666; margin-top: 30px; }
    .metric { display: inline-block; margin: 10px; padding: 10px; background: #f0f0f0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background: #f5f5f5; }
  </style>
</head>
<body>
  <h1>${report.title}</h1>
  <p><strong>Generated:</strong> ${report.generated}</p>
  <p><strong>Period:</strong> ${report.period.start.toDateString()} - ${report.period.end.toDateString()}</p>
`;

    for (const [section, data] of Object.entries(report.sections)) {
      html += `<h2>${this.formatSectionTitle(section)}</h2>`;
      html += this.formatSectionDataHTML(data);
    }

    html += '</body></html>';
    return html;
  }

  formatSectionTitle(section) {
    return section.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  formatSectionData(data) {
    if (typeof data === 'string') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => `- ${JSON.stringify(item)}`).join('\n');
    }

    if (typeof data === 'object') {
      return Object.entries(data).map(([key, value]) => {
        const formattedKey = this.formatSectionTitle(key);
        if (typeof value === 'object') {
          return `**${formattedKey}:**\n${this.formatSectionData(value)}`;
        }
        return `**${formattedKey}:** ${value}`;
      }).join('\n');
    }

    return String(data);
  }

  formatSectionDataHTML(data) {
    if (typeof data === 'string') {
      return `<p>${data}</p>`;
    }

    if (Array.isArray(data)) {
      return '<ul>' + data.map(item => `<li>${JSON.stringify(item)}</li>`).join('') + '</ul>';
    }

    if (typeof data === 'object') {
      let html = '<div>';
      for (const [key, value] of Object.entries(data)) {
        const formattedKey = this.formatSectionTitle(key);
        if (typeof value === 'object') {
          html += `<h3>${formattedKey}</h3>${this.formatSectionDataHTML(value)}`;
        } else {
          html += `<div class="metric"><strong>${formattedKey}:</strong> ${value}</div>`;
        }
      }
      html += '</div>';
      return html;
    }

    return `<p>${String(data)}</p>`;
  }

  generateVoiceSummary(report) {
    const summary = report.sections.executive_summary;
    if (!summary) {
      return 'Report generated successfully.';
    }

    let text = `${report.title} summary. `;
    
    if (summary.overall_health) {
      text += `Overall system health is ${summary.overall_health}. `;
    }

    if (summary.key_metrics) {
      const { uptime, incidents, resolved } = summary.key_metrics;
      text += `System uptime is ${uptime}. `;
      text += `There were ${incidents} incidents, with ${resolved} resolved. `;
    }

    if (summary.highlights && summary.highlights.length > 0) {
      text += `Key highlights: ${summary.highlights.join('. ')}. `;
    }

    return text;
  }

  async listTemplates() {
    const templates = [];
    for (const [key, template] of this.templates) {
      templates.push({
        id: key,
        name: template.name,
        sections: template.sections
      });
    }
    return templates;
  }
}