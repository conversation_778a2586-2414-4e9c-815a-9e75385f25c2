import ping from 'ping';
import { NodeSSH } from 'node-ssh';

export class MonitoringService {
  constructor(systemManager) {
    this.systemManager = systemManager;
    this.db = systemManager.db;
  }

  async checkSystemStatus(systemId) {
    const system = await this.db.get('SELECT * FROM systems WHERE id = ?', [systemId]);
    if (!system) {
      throw new Error(`System not found: ${systemId}`);
    }

    let status = 'unknown';
    let details = {};

    try {
      // Ping check
      const pingResult = await ping.promise.probe(system.ip_address, {
        timeout: 5,
        extra: ['-c', '3']
      });

      if (pingResult.alive) {
        status = 'online';
        details.ping = {
          alive: true,
          time: pingResult.time,
          min: pingResult.min,
          max: pingResult.max,
          avg: pingResult.avg
        };

        // If SSH is enabled, try to get more details
        if (system.ssh_enabled && system.ssh_username) {
          try {
            const sshDetails = await this.getSSHSystemInfo(system);
            details = { ...details, ...sshDetails };
          } catch (sshError) {
            details.ssh_error = sshError.message;
          }
        }
      } else {
        status = 'offline';
        details.ping = { alive: false };
      }
    } catch (error) {
      status = 'error';
      details.error = error.message;
    }

    // Update system status
    await this.db.run(
      'UPDATE systems SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?',
      [status, systemId]
    );

    // Record metrics if online
    if (status === 'online' && details.ping) {
      await this.recordMetric(systemId, 'ping_latency', details.ping.avg || 0, 'ms');
    }

    return {
      result: {
        system_id: systemId,
        system_name: system.name,
        status,
        details,
        checked_at: new Date().toISOString()
      }
    };
  }

  async getSSHSystemInfo(system) {
    const ssh = new NodeSSH();
    const info = {};

    try {
      await ssh.connect({
        host: system.ip_address,
        username: system.ssh_username,
        port: system.ssh_port || 22,
        readyTimeout: 5000
      });

      // Get uptime
      const uptime = await ssh.execCommand('uptime');
      if (uptime.code === 0) {
        info.uptime = uptime.stdout.trim();
      }

      // Get load average
      const loadavg = await ssh.execCommand('cat /proc/loadavg');
      if (loadavg.code === 0) {
        const loads = loadavg.stdout.trim().split(' ').slice(0, 3);
        info.load_average = {
          '1min': parseFloat(loads[0]),
          '5min': parseFloat(loads[1]),
          '15min': parseFloat(loads[2])
        };
      }

      // Get memory info
      const meminfo = await ssh.execCommand("free -m | grep Mem | awk '{print $2,$3,$4}'");
      if (meminfo.code === 0) {
        const [total, used, free] = meminfo.stdout.trim().split(' ').map(Number);
        info.memory = {
          total_mb: total,
          used_mb: used,
          free_mb: free,
          usage_percent: Math.round((used / total) * 100)
        };
      }

      // Get disk usage
      const df = await ssh.execCommand("df -h / | tail -1 | awk '{print $2,$3,$4,$5}'");
      if (df.code === 0) {
        const [size, used, avail, use] = df.stdout.trim().split(' ');
        info.disk = {
          size,
          used,
          available: avail,
          usage_percent: parseInt(use)
        };
      }

      await ssh.dispose();
    } catch (error) {
      await ssh.dispose();
      throw error;
    }

    return info;
  }

  async getSystemMetrics(systemId, metricTypes = ['cpu', 'memory', 'disk']) {
    const system = await this.db.get('SELECT * FROM systems WHERE id = ?', [systemId]);
    if (!system) {
      throw new Error(`System not found: ${systemId}`);
    }

    if (!system.ssh_enabled || !system.ssh_username) {
      // Return historical metrics from database
      const metrics = await this.db.query(
        `SELECT metric_type, value, unit, timestamp 
         FROM metrics 
         WHERE system_id = ? AND metric_type IN (${metricTypes.map(() => '?').join(',')})
         ORDER BY metric_type, timestamp DESC
         LIMIT 100`,
        [systemId, ...metricTypes]
      );

      return {
        result: {
          system_id: systemId,
          system_name: system.name,
          metrics: this.groupMetricsByType(metrics),
          source: 'historical'
        }
      };
    }

    // Get live metrics via SSH
    const ssh = new NodeSSH();
    const metrics = {};

    try {
      await ssh.connect({
        host: system.ip_address,
        username: system.ssh_username,
        port: system.ssh_port || 22,
        readyTimeout: 5000
      });

      if (metricTypes.includes('cpu')) {
        const cpu = await ssh.execCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
        if (cpu.code === 0) {
          const usage = parseFloat(cpu.stdout.trim());
          metrics.cpu = { usage_percent: usage };
          await this.recordMetric(systemId, 'cpu_usage', usage, '%');
        }
      }

      if (metricTypes.includes('memory')) {
        const mem = await ssh.execCommand("free -m | grep Mem | awk '{print $2,$3}'");
        if (mem.code === 0) {
          const [total, used] = mem.stdout.trim().split(' ').map(Number);
          const usage = Math.round((used / total) * 100);
          metrics.memory = {
            total_mb: total,
            used_mb: used,
            usage_percent: usage
          };
          await this.recordMetric(systemId, 'memory_usage', usage, '%');
        }
      }

      if (metricTypes.includes('disk')) {
        const disk = await ssh.execCommand("df -h / | tail -1 | awk '{print $5}' | cut -d'%' -f1");
        if (disk.code === 0) {
          const usage = parseInt(disk.stdout.trim());
          metrics.disk = { usage_percent: usage };
          await this.recordMetric(systemId, 'disk_usage', usage, '%');
        }
      }

      if (metricTypes.includes('network')) {
        const net = await ssh.execCommand("cat /proc/net/dev | grep -E 'eth0|ens' | head -1 | awk '{print $2,$10}'");
        if (net.code === 0) {
          const [rx, tx] = net.stdout.trim().split(' ').map(Number);
          metrics.network = {
            rx_bytes: rx,
            tx_bytes: tx
          };
          await this.recordMetric(systemId, 'network_rx', rx, 'bytes');
          await this.recordMetric(systemId, 'network_tx', tx, 'bytes');
        }
      }

      if (metricTypes.includes('processes')) {
        const procs = await ssh.execCommand("ps aux | wc -l");
        if (procs.code === 0) {
          const count = parseInt(procs.stdout.trim()) - 1; // Subtract header
          metrics.processes = { count };
          await this.recordMetric(systemId, 'process_count', count, 'count');
        }
      }

      await ssh.dispose();
    } catch (error) {
      await ssh.dispose();
      throw new Error(`Failed to get metrics: ${error.message}`);
    }

    return {
      result: {
        system_id: systemId,
        system_name: system.name,
        metrics,
        source: 'live',
        timestamp: new Date().toISOString()
      }
    };
  }

  async bulkStatusCheck(systemIds = []) {
    if (systemIds.length === 0) {
      // Check all systems
      const systems = await this.db.query('SELECT id FROM systems');
      systemIds = systems.map(s => s.id);
    }

    const results = await Promise.allSettled(
      systemIds.map(id => this.checkSystemStatus(id))
    );

    const summary = {
      total: results.length,
      online: 0,
      offline: 0,
      error: 0,
      details: []
    };

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const status = result.value.result.status;
        summary[status] = (summary[status] || 0) + 1;
        summary.details.push(result.value.result);
      } else {
        summary.error++;
        summary.details.push({
          system_id: systemIds[index],
          status: 'error',
          error: result.reason.message
        });
      }
    });

    return {
      result: summary
    };
  }

  async recordMetric(systemId, metricType, value, unit) {
    await this.db.run(
      'INSERT INTO metrics (system_id, metric_type, value, unit) VALUES (?, ?, ?, ?)',
      [systemId, metricType, value, unit]
    );
  }

  groupMetricsByType(metrics) {
    const grouped = {};
    metrics.forEach(m => {
      if (!grouped[m.metric_type]) {
        grouped[m.metric_type] = [];
      }
      grouped[m.metric_type].push({
        value: m.value,
        unit: m.unit,
        timestamp: m.timestamp
      });
    });
    return grouped;
  }
}