import { randomUUID } from 'crypto';
import { NodeSSH } from 'node-ssh';

export class SystemManager {
  constructor(db) {
    this.db = db;
    this.sshConnections = new Map();
  }

  async listSystems({ type = 'all', status = 'all', search = '' } = {}) {
    let query = 'SELECT * FROM systems WHERE 1=1';
    const params = [];

    if (type !== 'all') {
      query += ' AND type = ?';
      params.push(type);
    }

    if (status !== 'all') {
      query += ' AND status = ?';
      params.push(status);
    }

    if (search) {
      query += ' AND (name LIKE ? OR ip_address LIKE ? OR description LIKE ? OR location LIKE ?)';
      const searchParam = `%${search}%`;
      params.push(searchParam, searchParam, searchParam, searchParam);
    }

    query += ' ORDER BY name';

    const systems = await this.db.query(query, params);
    
    return {
      result: {
        systems: systems.map(s => ({
          ...s,
          metadata: s.metadata ? JSON.parse(s.metadata) : {}
        })),
        count: systems.length
      }
    };
  }

  async getSystem(id) {
    const system = await this.db.get('SELECT * FROM systems WHERE id = ?', [id]);
    
    if (!system) {
      throw new Error(`System not found: ${id}`);
    }

    // Get recent metrics
    const metrics = await this.db.query(
      `SELECT metric_type, value, unit, timestamp 
       FROM metrics 
       WHERE system_id = ? 
       ORDER BY timestamp DESC 
       LIMIT 50`,
      [id]
    );

    // Get active alerts
    const alerts = await this.db.query(
      `SELECT * FROM alerts 
       WHERE system_id = ? AND resolved = 0 
       ORDER BY created_at DESC`,
      [id]
    );

    return {
      result: {
        ...system,
        metadata: system.metadata ? JSON.parse(system.metadata) : {},
        recent_metrics: metrics,
        active_alerts: alerts
      }
    };
  }

  async addSystem(data) {
    const id = randomUUID();
    const {
      name,
      type,
      ip_address,
      operating_system = 'Unknown',
      location = '',
      description = '',
      management_url = '',
      ssh_enabled = false,
      credentials = {},
      ...metadata
    } = data;

    await this.db.run(
      `INSERT INTO systems (
        id, name, type, ip_address, operating_system, 
        location, description, management_url, ssh_enabled,
        ssh_port, ssh_username, metadata, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        id, name, type, ip_address, operating_system,
        location, description, management_url, ssh_enabled ? 1 : 0,
        credentials.port || 22, credentials.username || '',
        JSON.stringify(metadata), 'unknown'
      ]
    );

    return {
      result: {
        id,
        message: `System '${name}' added successfully`,
        system: await this.getSystem(id).then(r => r.result)
      }
    };
  }

  async updateSystem(id, updates) {
    const system = await this.db.get('SELECT * FROM systems WHERE id = ?', [id]);
    if (!system) {
      throw new Error(`System not found: ${id}`);
    }

    const allowedFields = [
      'name', 'type', 'ip_address', 'operating_system', 
      'location', 'description', 'management_url', 'ssh_enabled',
      'ssh_port', 'ssh_username', 'cpu_cores', 'memory_gb', 'storage_gb'
    ];

    const updateFields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key)) {
        updateFields.push(`${key} = ?`);
        values.push(key === 'ssh_enabled' ? (value ? 1 : 0) : value);
      }
    }

    if (updateFields.length > 0) {
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(id);
      
      await this.db.run(
        `UPDATE systems SET ${updateFields.join(', ')} WHERE id = ?`,
        values
      );
    }

    return {
      result: {
        message: `System '${system.name}' updated successfully`,
        system: await this.getSystem(id).then(r => r.result)
      }
    };
  }

  async deleteSystem(id) {
    const system = await this.db.get('SELECT name FROM systems WHERE id = ?', [id]);
    if (!system) {
      throw new Error(`System not found: ${id}`);
    }

    await this.db.run('DELETE FROM systems WHERE id = ?', [id]);

    return {
      result: {
        message: `System '${system.name}' deleted successfully`
      }
    };
  }

  async executeCommand(systemId, command, sudo = false) {
    const system = await this.db.get(
      'SELECT * FROM systems WHERE id = ? AND ssh_enabled = 1',
      [systemId]
    );

    if (!system) {
      throw new Error('System not found or SSH not enabled');
    }

    if (!system.ssh_username) {
      throw new Error('SSH username not configured for this system');
    }

    const ssh = new NodeSSH();
    
    try {
      // Connect to the system
      await ssh.connect({
        host: system.ip_address,
        username: system.ssh_username,
        port: system.ssh_port || 22,
        // In production, use proper key-based auth
        // For now, this requires SSH keys to be set up
        tryKeyboard: true,
        readyTimeout: 5000
      });

      // Execute command
      const result = await ssh.execCommand(sudo ? `sudo ${command}` : command);

      await ssh.dispose();

      return {
        result: {
          stdout: result.stdout,
          stderr: result.stderr,
          code: result.code,
          success: result.code === 0
        }
      };
    } catch (error) {
      await ssh.dispose();
      throw new Error(`SSH execution failed: ${error.message}`);
    }
  }

  async searchSystems(query) {
    // Simple natural language search implementation
    const keywords = query.toLowerCase().split(' ');
    
    let conditions = [];
    let params = [];

    // Look for type keywords
    const types = ['server', 'workstation', 'network', 'storage', 'vm', 'container', 'iot', 'security'];
    const typeKeywords = keywords.filter(k => types.includes(k));
    if (typeKeywords.length > 0) {
      conditions.push(`type IN (${typeKeywords.map(() => '?').join(',')})`);
      params.push(...typeKeywords);
    }

    // Look for status keywords
    const statuses = ['online', 'offline', 'maintenance', 'error'];
    const statusKeywords = keywords.filter(k => statuses.includes(k));
    if (statusKeywords.length > 0) {
      conditions.push(`status IN (${statusKeywords.map(() => '?').join(',')})`);
      params.push(...statusKeywords);
    }

    // Look for IP addresses
    const ipPattern = /\d+\.\d+\.\d+\.\d+/;
    const ips = keywords.filter(k => ipPattern.test(k));
    if (ips.length > 0) {
      conditions.push(`ip_address IN (${ips.map(() => '?').join(',')})`);
      params.push(...ips);
    }

    // General text search for other keywords
    const otherKeywords = keywords.filter(k => 
      !types.includes(k) && !statuses.includes(k) && !ipPattern.test(k)
    );
    
    if (otherKeywords.length > 0) {
      const searchConditions = otherKeywords.map(() => 
        '(name LIKE ? OR description LIKE ? OR location LIKE ?)'
      ).join(' OR ');
      conditions.push(`(${searchConditions})`);
      
      otherKeywords.forEach(k => {
        const param = `%${k}%`;
        params.push(param, param, param);
      });
    }

    const whereClause = conditions.length > 0 ? 
      `WHERE ${conditions.join(' AND ')}` : '';

    const systems = await this.db.query(
      `SELECT * FROM systems ${whereClause} ORDER BY name`,
      params
    );

    return {
      result: {
        query,
        systems: systems.map(s => ({
          ...s,
          metadata: s.metadata ? JSON.parse(s.metadata) : {}
        })),
        count: systems.length
      }
    };
  }
}