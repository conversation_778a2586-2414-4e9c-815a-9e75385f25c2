#!/usr/bin/env node

import { spawn } from 'child_process';
import { readFileSync } from 'fs';

console.log('Testing IT Assistant MCP Server...\n');

// Test cases
const tests = [
  {
    name: 'List tools',
    request: {
      jsonrpc: '2.0',
      method: 'tools/list',
      id: 1
    }
  },
  {
    name: 'Add a test server',
    request: {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: 'add_system',
        arguments: {
          name: 'test-server-01',
          type: 'server',
          ip_address: '*************',
          operating_system: 'Ubuntu 22.04',
          location: 'Datacenter A',
          description: 'Test application server',
          ssh_enabled: true,
          credentials: {
            username: 'admin',
            port: 22
          }
        }
      },
      id: 2
    }
  },
  {
    name: 'List all systems',
    request: {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: 'list_systems',
        arguments: {}
      },
      id: 3
    }
  },
  {
    name: 'Search for servers',
    request: {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: 'search_systems',
        arguments: {
          query: 'server datacenter'
        }
      },
      id: 4
    }
  }
];

// Run the server
const server = spawn('node', ['src/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseBuffer = '';

server.stdout.on('data', (data) => {
  responseBuffer += data.toString();
  
  // Try to parse complete JSON responses
  const lines = responseBuffer.split('\n');
  responseBuffer = lines.pop() || '';
  
  lines.forEach(line => {
    if (line.trim()) {
      try {
        const response = JSON.parse(line);
        console.log('Response:', JSON.stringify(response, null, 2));
        console.log('---');
      } catch (e) {
        // Not a complete JSON yet
      }
    }
  });
});

server.stderr.on('data', (data) => {
  console.error('Server:', data.toString().trim());
});

// Send test requests
async function runTests() {
  for (const test of tests) {
    console.log(`\nRunning test: ${test.name}`);
    server.stdin.write(JSON.stringify(test.request) + '\n');
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Clean exit
  setTimeout(() => {
    server.kill();
    process.exit(0);
  }, 2000);
}

// Wait for server to start
setTimeout(runTests, 1000);