import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export class DatabaseManager {
  constructor() {
    this.db = null;
    this.dbPath = process.env.IT_ASSISTANT_DB_PATH || join(__dirname, '..', 'data', 'systems.db');
  }

  async initialize() {
    // Ensure data directory exists
    const dataDir = dirname(this.dbPath);
    await fs.mkdir(dataDir, { recursive: true });

    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) reject(err);
        else {
          console.error(`Database initialized at: ${this.dbPath}`);
          this.setupDatabase().then(resolve).catch(reject);
        }
      });
    });
  }

  async setupDatabase() {
    const run = promisify(this.db.run.bind(this.db));

    // Create systems table
    await run(`
      CREATE TABLE IF NOT EXISTS systems (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        ip_address TEXT,
        operating_system TEXT,
        status TEXT DEFAULT 'unknown',
        location TEXT,
        description TEXT,
        management_url TEXT,
        ssh_enabled BOOLEAN DEFAULT 0,
        ssh_port INTEGER DEFAULT 22,
        ssh_username TEXT,
        cpu_cores INTEGER,
        memory_gb REAL,
        storage_gb REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_seen DATETIME,
        metadata TEXT
      )
    `);

    // Create metrics table
    await run(`
      CREATE TABLE IF NOT EXISTS metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        system_id TEXT NOT NULL,
        metric_type TEXT NOT NULL,
        value REAL,
        unit TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (system_id) REFERENCES systems(id) ON DELETE CASCADE
      )
    `);

    // Create alerts table
    await run(`
      CREATE TABLE IF NOT EXISTS alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        system_id TEXT NOT NULL,
        alert_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        message TEXT,
        resolved BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        resolved_at DATETIME,
        FOREIGN KEY (system_id) REFERENCES systems(id) ON DELETE CASCADE
      )
    `);

    // Create indices
    await run('CREATE INDEX IF NOT EXISTS idx_systems_type ON systems(type)');
    await run('CREATE INDEX IF NOT EXISTS idx_systems_status ON systems(status)');
    await run('CREATE INDEX IF NOT EXISTS idx_metrics_system ON metrics(system_id, metric_type)');
    await run('CREATE INDEX IF NOT EXISTS idx_alerts_system ON alerts(system_id, resolved)');
  }

  async query(sql, params = []) {
    const all = promisify(this.db.all.bind(this.db));
    return await all(sql, params);
  }

  async get(sql, params = []) {
    const get = promisify(this.db.get.bind(this.db));
    return await get(sql, params);
  }

  async run(sql, params = []) {
    const run = promisify(this.db.run.bind(this.db));
    return await run(sql, params);
  }

  async close() {
    if (this.db) {
      const close = promisify(this.db.close.bind(this.db));
      await close();
    }
  }
}