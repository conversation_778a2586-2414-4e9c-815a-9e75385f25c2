#!/bin/bash
echo "🔧 Updating hosts file with correct IPs..."

# Remove old entries
sudo sed -i.bak2 '/# Proxmox Infrastructure Local Domains/,/^$/d' /etc/hosts

# Add correct entries
echo '
# Proxmox Infrastructure Local Domains
*************   proxmox.local pve.local neo4j.local graph.local mcp.local
************    ha.local homeassistant.local hass.local
************    backupserver.local pbs.local backup.local
************    iventoy.local pxe.local
************    dokploy.local paas.local
************    browser.local browser-mcp.local
' | sudo tee -a /etc/hosts > /dev/null

echo "✅ Hosts file updated with correct IPs"
echo ""
echo "Testing connections..."
ping -c 1 proxmox.local > /dev/null 2>&1 && echo "✅ proxmox.local" || echo "❌ proxmox.local"
ping -c 1 dokploy.local > /dev/null 2>&1 && echo "✅ dokploy.local" || echo "❌ dokploy.local"
ping -c 1 browser.local > /dev/null 2>&1 && echo "✅ browser.local" || echo "❌ browser.local"