[{"name": "it-assistant", "type": "local", "path": "/opt/it-assistant-mcp", "inspectorPort": 8001, "description": "IT infrastructure management and monitoring"}, {"name": "browser-use", "type": "docker", "image": "browser-use-mcp:latest", "inspectorPort": 8101, "description": "Browser automation and web scraping"}, {"name": "proxmox-production", "type": "local", "path": "/opt/proxmox-mcp", "inspectorPort": 8201, "description": "Enterprise Proxmox management"}, {"name": "elevenlabs", "type": "docker", "image": "elevenlabs-mcp:latest", "inspectorPort": 8301, "description": "Voice synthesis and audio AI"}, {"name": "linear-mcp", "type": "docker", "image": "linear-mcp:latest", "inspectorPort": 8401, "description": "Linear project management integration"}, {"name": "sequential-thinking", "type": "local", "path": "/opt/sequential-thinking-mcp", "inspectorPort": 8501, "description": "Chain of thought processing"}]