# Proxmox Community Scripts Integration

## Overview

The IT Assistant MCP server integrates with the [Proxmox Community Scripts](https://github.com/community-scripts/ProxmoxVE) repository to provide automated deployment of common IT infrastructure services.

## Available Scripts

### 🔍 Monitoring & Observability

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **prometheus** | Time-series metrics database | 1 CPU, 2GB RAM, 8GB disk | Prometheus UI on port 9090 |
| **grafana** | Metrics visualization | 1 CPU, 2GB RAM, 8GB disk | Web UI on port 3000 |
| **zabbix** | Enterprise monitoring | 2 CPU, 4GB RAM, 20GB disk | Full monitoring suite |
| **graylog** | Log management | 2 CPU, 4GB RAM, 20GB disk | Centralized logging |
| **uptimekuma** | Uptime monitoring | 1 CPU, 1GB RAM, 5GB disk | Beautiful status pages |
| **changedetection** | Website monitoring | 1 CPU, 1GB RAM, 5GB disk | Change notifications |

### 🔒 Security & Networking

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **wireguard** | Modern VPN server | 1 CPU, 512MB RAM, 4GB disk | VPN configuration |
| **nginx-proxy-manager** | Reverse proxy with GUI | 1 CPU, 1GB RAM, 4GB disk | SSL management |
| **traefik** | Cloud-native proxy | 1 CPU, 1GB RAM, 4GB disk | Auto SSL, routing |
| **pihole** | Ad blocking DNS | 1 CPU, 512MB RAM, 4GB disk | Web admin panel |
| **adguard** | Advanced ad blocking | 1 CPU, 1GB RAM, 4GB disk | DNS filtering |
| **cloudflared** | Cloudflare tunnel | 1 CPU, 512MB RAM, 4GB disk | Zero-trust access |

### 🛠️ Development & CI/CD

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **docker** | Container runtime | 2 CPU, 2GB RAM, 16GB disk | Docker CLI/API |
| **podman** | Rootless containers | 2 CPU, 2GB RAM, 16GB disk | Docker-compatible |
| **jenkins** | CI/CD server | 2 CPU, 4GB RAM, 20GB disk | Build automation |
| **gitea** | Git hosting | 1 CPU, 2GB RAM, 10GB disk | Web-based Git |
| **gitlab** | DevOps platform | 4 CPU, 8GB RAM, 50GB disk | Complete CI/CD |

### 🗄️ Databases

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **postgresql** | SQL database | 1 CPU, 2GB RAM, 10GB disk | pgAdmin optional |
| **mysql** | SQL database | 1 CPU, 2GB RAM, 10GB disk | phpMyAdmin optional |
| **mariadb** | MySQL fork | 1 CPU, 2GB RAM, 10GB disk | Web admin tools |
| **redis** | In-memory store | 1 CPU, 1GB RAM, 4GB disk | Redis CLI |
| **influxdb** | Time-series DB | 1 CPU, 2GB RAM, 10GB disk | InfluxDB UI |

### 💼 IT Services

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **nextcloud** | File sharing | 2 CPU, 4GB RAM, 20GB disk | Collaboration suite |
| **vaultwarden** | Password manager | 1 CPU, 1GB RAM, 5GB disk | Bitwarden compatible |
| **paperless-ngx** | Document management | 2 CPU, 2GB RAM, 20GB disk | OCR, search |
| **bookstack** | Wiki/documentation | 1 CPU, 2GB RAM, 10GB disk | Knowledge base |
| **n8n** | Workflow automation | 1 CPU, 2GB RAM, 10GB disk | Visual automation |

### 🏠 Home Automation

| Script | Description | Default Resources | Post-Install Features |
|--------|-------------|-------------------|----------------------|
| **homeassistant** | Smart home hub | 2 CPU, 4GB RAM, 32GB disk | HACS, addons |
| **mqtt** | Message broker | 1 CPU, 512MB RAM, 2GB disk | MQTT Explorer |
| **esphome** | ESP management | 1 CPU, 1GB RAM, 4GB disk | Device flashing |
| **zigbee2mqtt** | Zigbee bridge | 1 CPU, 1GB RAM, 4GB disk | Device pairing |

## Deployment Examples

### Quick Single Container
```bash
# Deploy Grafana
bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/grafana.sh)"
```

### Monitoring Stack
```bash
# Deploy complete monitoring solution
for service in prometheus grafana uptimekuma; do
  bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/$service.sh)"
done
```

### Security Infrastructure
```bash
# Deploy security stack
for service in wireguard nginx-proxy-manager pihole; do
  bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/$service.sh)"
done
```

## Integration with IT Assistant

### Using Prompts
1. "Deploy a monitoring stack with Prometheus and Grafana"
2. "Set up Wireguard VPN server on Proxmox"
3. "Create a complete development environment with GitLab"
4. "Deploy password manager and document management system"

### Automation Examples

#### Schedule Automated Deployments
```javascript
await schedule_task({
  name: "Deploy Monitoring Stack",
  type: "custom",
  schedule: "0 2 * * 1", // Weekly on Monday at 2 AM
  script: `
    for service in prometheus grafana; do
      bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/$service.sh)"
    done
  `,
  targets: ["pve"]
})
```

#### Batch Container Management
```javascript
// List all containers created by community scripts
await list_all_vms({
  filter: {
    tags: ["community-script"]
  }
})

// Create snapshots of all community containers
await batch_execute({
  command: "vzdump --mode snapshot --compress gzip",
  targets: ["100", "101", "102"] // Container IDs
})
```

## Best Practices

### Resource Planning
- **Development**: Start with minimum resources, scale up as needed
- **Production**: Use recommended resources or higher
- **Storage**: Consider growth, especially for databases and logs
- **Network**: Use VLANs to segment services

### Security Considerations
1. Change default passwords immediately
2. Enable firewall rules for each container
3. Use reverse proxy for external access
4. Regular updates via built-in update feature
5. Configure fail2ban where applicable

### Backup Strategy
1. Daily snapshots for critical services
2. Weekly full backups for databases
3. Test restore procedures monthly
4. Off-site backup for disaster recovery

### Monitoring Setup
```
Prometheus → Grafana → Alerts
     ↓           ↓
   Metrics   Dashboards
     ↓           ↓
All Containers   Visualization
```

## Troubleshooting

### Common Issues
1. **Script fails**: Check Proxmox version (8.x required)
2. **Container won't start**: Verify resources available
3. **Network issues**: Check bridge configuration
4. **Storage full**: Expand disk or clean logs

### Debug Commands
```bash
# Check container status
pct status <CTID>

# View container logs
pct enter <CTID>
journalctl -xe

# Check resource usage
pct exec <CTID> -- df -h
pct exec <CTID> -- free -m
```

## Advanced Usage

### Custom Templates
Create custom container templates:
```bash
# Clone and customize
pct clone <template-id> <new-id> --full
pct set <new-id> --memory 4096 --cores 2
```

### Ansible Integration
```yaml
- name: Deploy monitoring stack
  hosts: proxmox
  tasks:
    - name: Deploy Prometheus
      shell: |
        bash -c "$(wget -qLO - https://github.com/community-scripts/ProxmoxVE/raw/main/ct/prometheus.sh)"
```

### API Automation
```javascript
// Use IT Assistant to automate deployments
await run_playbook({
  playbook: "deploy_monitoring",
  targets: ["pve"],
  variables: {
    containers: ["prometheus", "grafana", "alertmanager"]
  }
})
```

## Resources
- GitHub: https://github.com/community-scripts/ProxmoxVE
- Website: https://helper-scripts.com
- Community: Proxmox Forums
- Updates: Built into each container via menu system