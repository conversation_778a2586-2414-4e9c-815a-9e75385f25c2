#!/bin/bash

# Interactive setup tool for IT Assistant MCP Server
# Configures and deploys the containerized MCP server

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="$HOME/.it-assistant-mcp"
CONFIG_FILE="$CONFIG_DIR/config.env"
DATA_DIR="$CONFIG_DIR/data"

# Banner
echo -e "${CYAN}"
echo "╔═══════════════════════════════════════════╗"
echo "║      IT Assistant MCP Server Setup        ║"
echo "║    Infrastructure Management for Ace      ║"
echo "╚═══════════════════════════════════════════╝"
echo -e "${NC}"

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    # Check OS
    if [[ $(uname -s) != "Darwin" ]]; then
        echo -e "${RED}❌ This tool requires macOS${NC}"
        exit 1
    fi
    
    # Check architecture
    if [[ $(uname -m) != "arm64" ]]; then
        echo -e "${YELLOW}⚠️  Warning: This tool is optimized for Apple Silicon${NC}"
        echo "   It may work on Intel Macs with Docker Desktop"
        read -p "Continue anyway? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check for container runtime
    if command -v container &> /dev/null; then
        RUNTIME="container"
        echo -e "${GREEN}✓ Apple container runtime found${NC}"
    elif command -v docker &> /dev/null; then
        RUNTIME="docker"
        echo -e "${YELLOW}⚠️  Using Docker as fallback${NC}"
    else
        echo -e "${RED}❌ No container runtime found${NC}"
        echo "Please install Docker Desktop or upgrade to macOS 15+"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js not found${NC}"
        echo "Please install Node.js 18+ first"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All prerequisites met!${NC}\n"
}

# Function to setup configuration
setup_configuration() {
    echo -e "${BLUE}🔧 Setting up configuration...${NC}"
    
    # Create directories
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"
    
    # Check if config exists
    if [ -f "$CONFIG_FILE" ]; then
        echo -e "${YELLOW}Configuration file already exists at: $CONFIG_FILE${NC}"
        read -p "Do you want to reconfigure? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return
        fi
    fi
    
    # Create configuration
    cat > "$CONFIG_FILE" << EOF
# IT Assistant MCP Server Configuration
# Generated on $(date)

# Database location (inside container)
IT_ASSISTANT_DB_PATH=/app/data/systems.db

# Node environment
NODE_ENV=production

# Monitoring integration (optional)
# PROMETHEUS_ENDPOINT=
# GRAFANA_ENDPOINT=

# Alert webhook (optional)
# ALERT_WEBHOOK_URL=

# SSH Configuration
# Note: SSH keys will be mounted from ~/.ssh
SSH_TIMEOUT=10000
SSH_RETRY_COUNT=3
EOF
    
    echo -e "${GREEN}✓ Configuration created${NC}\n"
}

# Function to build container
build_container() {
    echo -e "${BLUE}🔨 Building container...${NC}"
    
    cd "$SCRIPT_DIR"
    
    if [[ $RUNTIME == "container" ]]; then
        container build -t it-assistant-mcp:latest -f Containerfile .
    else
        docker build -t it-assistant-mcp:latest -f Containerfile .
    fi
    
    echo -e "${GREEN}✓ Container built successfully${NC}\n"
}

# Function to install npm package
install_npm_package() {
    echo -e "${BLUE}📦 Installing npm package...${NC}"
    
    cd "$SCRIPT_DIR"
    npm install
    
    echo -e "${GREEN}✓ Package installed${NC}\n"
}

# Function to setup Ace integration
setup_ace_integration() {
    echo -e "${BLUE}🔗 Setting up Ace integration...${NC}"
    
    ACE_CONFIG="$HOME/ace/configuration/ace.yml"
    
    if [ ! -f "$ACE_CONFIG" ]; then
        echo -e "${YELLOW}Ace configuration not found at: $ACE_CONFIG${NC}"
        read -p "Enter path to ace.yml (or press Enter to skip): " ace_path
        if [ -n "$ace_path" ]; then
            ACE_CONFIG="$ace_path"
        else
            echo -e "${YELLOW}Skipping Ace integration${NC}"
            return
        fi
    fi
    
    # Check if already configured
    if grep -q "it-assistant:" "$ACE_CONFIG" 2>/dev/null; then
        echo -e "${YELLOW}IT Assistant already configured in Ace${NC}"
        return
    fi
    
    echo -e "${CYAN}Add the following to your ace.yml:${NC}"
    echo
    echo "    # IT Infrastructure Management"
    echo "    it-assistant:"
    echo "        type: stdio"
    if [[ $RUNTIME == "container" ]]; then
        echo "        command: container"
    else
        echo "        command: docker"
    fi
    echo "        args:"
    echo "            - exec"
    echo "            - -i"
    echo "            - it-assistant-mcp"
    echo "            - node"
    echo "            - /app/src/index.js"
    echo
    
    read -p "Would you like me to add this automatically? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Backup original
        cp "$ACE_CONFIG" "$ACE_CONFIG.backup"
        
        # Add configuration
        echo "" >> "$ACE_CONFIG"
        echo "    # IT Infrastructure Management" >> "$ACE_CONFIG"
        echo "    it-assistant:" >> "$ACE_CONFIG"
        echo "        type: stdio" >> "$ACE_CONFIG"
        echo "        command: $RUNTIME" >> "$ACE_CONFIG"
        echo "        args:" >> "$ACE_CONFIG"
        echo "            - exec" >> "$ACE_CONFIG"
        echo "            - -i" >> "$ACE_CONFIG"
        echo "            - it-assistant-mcp" >> "$ACE_CONFIG"
        echo "            - node" >> "$ACE_CONFIG"
        echo "            - /app/src/index.js" >> "$ACE_CONFIG"
        
        echo -e "${GREEN}✓ Added to Ace configuration${NC}"
    fi
}

# Function to start container
start_container() {
    echo -e "${BLUE}🚀 Starting container...${NC}"
    
    # Stop if already running
    if $RUNTIME ps | grep -q "it-assistant-mcp"; then
        echo "Stopping existing container..."
        $RUNTIME stop it-assistant-mcp >/dev/null 2>&1 || true
        $RUNTIME rm it-assistant-mcp >/dev/null 2>&1 || true
    fi
    
    # Run container
    if [[ $RUNTIME == "container" ]]; then
        container run \
            --name it-assistant-mcp \
            --detach \
            --restart unless-stopped \
            --volume "$DATA_DIR:/app/data:rw" \
            --volume "$HOME/.ssh:/home/<USER>/.ssh:ro" \
            --env-file "$CONFIG_FILE" \
            --cap-drop ALL \
            --cap-add NET_RAW \
            --security-opt no-new-privileges:true \
            --read-only \
            --tmpfs /tmp:noexec,nosuid,size=10M \
            it-assistant-mcp:latest
    else
        docker run \
            --name it-assistant-mcp \
            --detach \
            --restart unless-stopped \
            --volume "$DATA_DIR:/app/data:rw" \
            --volume "$HOME/.ssh:/home/<USER>/.ssh:ro" \
            --env-file "$CONFIG_FILE" \
            --cap-drop ALL \
            --cap-add NET_RAW \
            --security-opt no-new-privileges:true \
            --read-only \
            --tmpfs /tmp:noexec,nosuid,size=10M \
            it-assistant-mcp:latest
    fi
    
    echo -e "${GREEN}✓ Container started${NC}\n"
}

# Function to test installation
test_installation() {
    echo -e "${BLUE}🧪 Testing installation...${NC}"
    
    # Test with MCP inspector
    echo "Running MCP inspector..."
    cd "$SCRIPT_DIR"
    timeout 5 npx @modelcontextprotocol/inspector src/index.js || true
    
    echo
    echo -e "${GREEN}✅ Setup complete!${NC}"
    echo
    echo -e "${CYAN}📚 Next steps:${NC}"
    echo "1. The IT Assistant MCP server is now running in a container"
    echo "2. Add systems using Ace with commands like:"
    echo "   'Add server web-01 at ************ running Ubuntu'"
    echo "3. Monitor systems with:"
    echo "   'Check status of all servers'"
    echo "   'Show me offline systems'"
    echo
    echo -e "${YELLOW}🔧 Management commands:${NC}"
    echo "  View logs:    $RUNTIME logs -f it-assistant-mcp"
    echo "  Stop server:  $RUNTIME stop it-assistant-mcp"
    echo "  Start server: $RUNTIME start it-assistant-mcp"
    echo
}

# Main setup flow
main() {
    check_prerequisites
    
    echo -e "${PURPLE}Choose setup mode:${NC}"
    echo "1) Full setup (recommended for first time)"
    echo "2) Container only (rebuild and restart)"
    echo "3) Configuration only"
    echo "4) Ace integration only"
    
    read -p "Enter choice (1-4): " choice
    echo
    
    case $choice in
        1)
            setup_configuration
            install_npm_package
            build_container
            start_container
            setup_ace_integration
            test_installation
            ;;
        2)
            build_container
            start_container
            ;;
        3)
            setup_configuration
            ;;
        4)
            setup_ace_integration
            ;;
        *)
            echo -e "${RED}Invalid choice${NC}"
            exit 1
            ;;
    esac
}

# Run main function
main