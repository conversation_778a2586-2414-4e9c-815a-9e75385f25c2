#!/bin/bash

# Batch convert multiple MCP servers to include Inspector support

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
BASE_INSPECTOR_PORT=8001
OUTPUT_BASE_DIR="./mcp-servers-with-inspector"
SERVERS_CONFIG_FILE=""
PARALLEL_JOBS=4

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║          Batch MCP Server Inspector Converter                ║${NC}"
echo -e "${CYAN}║      Convert multiple MCP servers to include Inspector       ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -c, --config <file>     Config file with server list (JSON)"
    echo "  -d, --directory <dir>   Directory containing MCP servers"
    echo "  -o, --output <dir>      Output base directory (default: $OUTPUT_BASE_DIR)"
    echo "  -p, --port <port>       Starting Inspector port (default: $BASE_INSPECTOR_PORT)"
    echo "  -j, --jobs <num>        Parallel conversion jobs (default: $PARALLEL_JOBS)"
    echo "  -h, --help              Show this help message"
    echo
    echo "Config file format:"
    echo '  [
    {
      "name": "it-assistant",
      "type": "local",
      "path": "/path/to/server",
      "inspectorPort": 8001
    },
    {
      "name": "browser-use",
      "type": "docker",
      "image": "browser-use-mcp:latest",
      "inspectorPort": 8002
    }
  ]'
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            SERVERS_CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--directory)
            SCAN_DIRECTORY="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_BASE_DIR="$2"
            shift 2
            ;;
        -p|--port)
            BASE_INSPECTOR_PORT="$2"
            shift 2
            ;;
        -j|--jobs)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Create output directory
mkdir -p "$OUTPUT_BASE_DIR"

# Function to scan directory for MCP servers
scan_for_servers() {
    local dir="$1"
    local servers=()
    
    echo -e "${YELLOW}Scanning directory: $dir${NC}"
    
    # Find all directories with package.json
    while IFS= read -r -d '' pkg_file; do
        local server_dir=$(dirname "$pkg_file")
        local server_name=$(basename "$server_dir")
        
        # Check if it's an MCP server
        if grep -q "@modelcontextprotocol/sdk" "$pkg_file" 2>/dev/null; then
            echo -e "${GREEN}Found MCP server: $server_name${NC}"
            servers+=("$server_dir")
        fi
    done < <(find "$dir" -maxdepth 3 -name "package.json" -print0)
    
    echo "${servers[@]}"
}

# Function to convert a single server
convert_server() {
    local server_info="$1"
    local index="$2"
    
    # Parse server info (could be JSON or path)
    if [[ "$server_info" == /* ]] || [[ "$server_info" == ./* ]]; then
        # It's a path
        local server_path="$server_info"
        local server_name=$(basename "$server_path")
        local server_type="local"
        local inspector_port=$((BASE_INSPECTOR_PORT + index))
    else
        # Parse JSON
        local server_name=$(echo "$server_info" | jq -r '.name')
        local server_type=$(echo "$server_info" | jq -r '.type')
        local server_path=$(echo "$server_info" | jq -r '.path // .image')
        local inspector_port=$(echo "$server_info" | jq -r ".inspectorPort // $((BASE_INSPECTOR_PORT + index))")
    fi
    
    echo -e "${BLUE}[$((index + 1))] Converting: $server_name${NC}"
    echo "  Type: $server_type"
    echo "  Inspector Port: $inspector_port"
    
    # Run converter
    local output_dir="$OUTPUT_BASE_DIR/$server_name"
    
    if [ "$server_type" = "local" ]; then
        ./convert-mcp-to-inspector.sh \
            -t local \
            -p "$inspector_port" \
            -o "$output_dir" \
            "$server_path"
    elif [ "$server_type" = "docker" ]; then
        ./convert-mcp-to-inspector.sh \
            -t docker \
            -p "$inspector_port" \
            -n "$server_name" \
            -o "$output_dir" \
            "$server_path"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $server_name converted successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ $server_name conversion failed${NC}"
        return 1
    fi
}

# Main conversion process
if [ -n "$SERVERS_CONFIG_FILE" ]; then
    # Convert from config file
    echo -e "${YELLOW}Loading servers from config file...${NC}"
    
    if [ ! -f "$SERVERS_CONFIG_FILE" ]; then
        echo -e "${RED}Error: Config file not found: $SERVERS_CONFIG_FILE${NC}"
        exit 1
    fi
    
    # Read servers from JSON
    servers=($(jq -c '.[]' "$SERVERS_CONFIG_FILE"))
    total=${#servers[@]}
    
    echo -e "${GREEN}Found $total servers to convert${NC}"
    
elif [ -n "$SCAN_DIRECTORY" ]; then
    # Scan directory for servers
    if [ ! -d "$SCAN_DIRECTORY" ]; then
        echo -e "${RED}Error: Directory not found: $SCAN_DIRECTORY${NC}"
        exit 1
    fi
    
    # Get server paths
    server_paths=($(scan_for_servers "$SCAN_DIRECTORY"))
    total=${#server_paths[@]}
    
    if [ $total -eq 0 ]; then
        echo -e "${YELLOW}No MCP servers found in $SCAN_DIRECTORY${NC}"
        exit 0
    fi
    
    echo -e "${GREEN}Found $total servers to convert${NC}"
    servers=("${server_paths[@]}")
    
else
    echo -e "${RED}Error: Please specify either -c (config file) or -d (directory)${NC}"
    show_usage
    exit 1
fi

# Create summary file
SUMMARY_FILE="$OUTPUT_BASE_DIR/conversion-summary.md"
cat > "$SUMMARY_FILE" << EOF
# MCP Server Inspector Conversion Summary

Generated: $(date)
Total Servers: $total

## Converted Servers

| Server Name | Type | Inspector Port | Output Directory |
|------------|------|----------------|------------------|
EOF

# Convert servers with progress
echo
echo -e "${CYAN}Starting batch conversion...${NC}"
echo

successful=0
failed=0

# Convert servers (could be parallelized with GNU parallel if available)
for i in "${!servers[@]}"; do
    if convert_server "${servers[$i]}" "$i"; then
        ((successful++))
        
        # Add to summary
        if [[ "${servers[$i]}" == /* ]] || [[ "${servers[$i]}" == ./* ]]; then
            server_name=$(basename "${servers[$i]}")
            echo "| $server_name | local | $((BASE_INSPECTOR_PORT + i)) | $OUTPUT_BASE_DIR/$server_name |" >> "$SUMMARY_FILE"
        else
            server_name=$(echo "${servers[$i]}" | jq -r '.name')
            server_type=$(echo "${servers[$i]}" | jq -r '.type')
            inspector_port=$(echo "${servers[$i]}" | jq -r ".inspectorPort // $((BASE_INSPECTOR_PORT + i))")
            echo "| $server_name | $server_type | $inspector_port | $OUTPUT_BASE_DIR/$server_name |" >> "$SUMMARY_FILE"
        fi
    else
        ((failed++))
    fi
    
    echo
done

# Add deployment instructions to summary
cat >> "$SUMMARY_FILE" << 'EOF'

## Deployment Instructions

### Local Servers

```bash
cd <output-directory>
./start-with-inspector.sh
```

### Docker Servers

```bash
cd <output-directory>
./build.sh
./run.sh
```

### MCP Hub Integration

1. Copy converted servers to MCP Hub server directory
2. Update MCP Hub configuration to include Inspector ports
3. Restart MCP Hub services

### Claude Desktop Configuration

Add to `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "server-name": {
      "command": "/path/to/output/start-with-inspector.sh"
    }
  }
}
```

## Environment Variables

- `INSPECTOR_ENABLED`: Enable/disable Inspector (default: true)
- `INSPECTOR_PORT`: Override Inspector port

## Accessing Inspector

Each server's Inspector UI is available at:
- http://localhost:<inspector-port>

EOF

# Create Docker Compose for all servers
if [ $successful -gt 0 ]; then
    echo -e "${YELLOW}Creating unified docker-compose.yml...${NC}"
    
    cat > "$OUTPUT_BASE_DIR/docker-compose-all.yml" << 'EOF'
version: '3.8'

services:
EOF
    
    # Add each Docker server to compose file
    for i in "${!servers[@]}"; do
        if [[ "${servers[$i]}" == /* ]] || [[ "${servers[$i]}" == ./* ]]; then
            continue  # Skip local servers
        fi
        
        server_info="${servers[$i]}"
        if echo "$server_info" | jq -e '.type == "docker"' >/dev/null 2>&1; then
            server_name=$(echo "$server_info" | jq -r '.name')
            inspector_port=$(echo "$server_info" | jq -r ".inspectorPort // $((BASE_INSPECTOR_PORT + i))")
            
            cat >> "$OUTPUT_BASE_DIR/docker-compose-all.yml" << EOF
  $server_name:
    build: ./$server_name
    container_name: mcp-$server_name
    restart: unless-stopped
    ports:
      - "$((8000 + i)):8000"
      - "$inspector_port:$inspector_port"
    environment:
      - INSPECTOR_ENABLED=true
      - INSPECTOR_PORT=$inspector_port
    networks:
      - mcp-network

EOF
        fi
    done
    
    cat >> "$OUTPUT_BASE_DIR/docker-compose-all.yml" << 'EOF'
networks:
  mcp-network:
    driver: bridge
EOF
fi

# Final summary
echo
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║                   Conversion Complete!                       ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo
echo -e "${GREEN}✅ Successful: $successful${NC}"
echo -e "${RED}❌ Failed: $failed${NC}"
echo
echo -e "Output directory: ${BLUE}$OUTPUT_BASE_DIR${NC}"
echo -e "Summary file: ${BLUE}$SUMMARY_FILE${NC}"
echo
echo "Next steps:"
echo "1. Review the conversion summary"
echo "2. Test each server with Inspector"
echo "3. Deploy to your MCP Hub"
echo
echo -e "${YELLOW}Tip: Use docker-compose-all.yml to run all Docker servers together${NC}"