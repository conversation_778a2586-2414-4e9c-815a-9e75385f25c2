{"name": "@alias/mcp-scaffold", "version": "1.0.0", "description": "CLI tool to scaffold TypeScript MCP servers for the ALIAS ecosystem", "type": "module", "main": "dist/index.js", "bin": {"mcp-scaffold": "./dist/cli.js", "create-mcp-server": "./dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx watch src/cli.ts", "start": "node dist/cli.js", "prepare": "npm run build", "test": "vitest"}, "keywords": ["mcp", "model-context-protocol", "scaffold", "cli", "typescript", "alias"], "author": "ALIAS", "license": "MIT", "dependencies": {"@inquirer/prompts": "^3.3.0", "chalk": "^5.3.0", "commander": "^11.1.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "handlebars": "^4.7.8", "ora": "^8.0.1", "validate-npm-package-name": "^5.0.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^20.10.5", "@types/validate-npm-package-name": "^4.0.2", "tsx": "^4.7.0", "typescript": "^5.3.3", "vitest": "^1.1.0"}, "engines": {"node": ">=18.0.0"}}