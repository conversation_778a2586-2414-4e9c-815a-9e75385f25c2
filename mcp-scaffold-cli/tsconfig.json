{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022"], "moduleResolution": "node", "rootDir": "./src", "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "templates"]}