#!/bin/bash

# MCP Scaffold CLI Publishing Script
# This script handles the publishing process for the MCP scaffold CLI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}MCP Scaffold CLI - Publishing Script${NC}"
echo "===================================="

# Check if we're in the correct directory
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
  echo -e "${RED}Error: Must run from the mcp-scaffold-cli directory${NC}"
  exit 1
fi

# Check if logged into npm
echo -e "\n${YELLOW}Checking npm login status...${NC}"
npm whoami &> /dev/null || {
  echo -e "${RED}Error: Not logged into npm. Please run 'npm login' first${NC}"
  exit 1
}

# Clean and build
echo -e "\n${YELLOW}Cleaning previous build...${NC}"
rm -rf dist/

echo -e "\n${YELLOW}Building TypeScript...${NC}"
npm run build

# Run tests
echo -e "\n${YELLOW}Running tests...${NC}"
npm test

# Update version
echo -e "\n${YELLOW}Current version:${NC} $(node -p "require('./package.json').version")"
echo -e "${YELLOW}Enter new version (or press enter to skip):${NC}"
read -r NEW_VERSION

if [ -n "$NEW_VERSION" ]; then
  npm version "$NEW_VERSION" --no-git-tag-version
  echo -e "${GREEN}Version updated to: $NEW_VERSION${NC}"
fi

# Git operations
echo -e "\n${YELLOW}Committing changes...${NC}"
git add .
git commit -m "Release v$(node -p "require('./package.json').version")" || {
  echo -e "${YELLOW}No changes to commit${NC}"
}

# Create git tag
VERSION=$(node -p "require('./package.json').version")
echo -e "\n${YELLOW}Creating git tag v$VERSION...${NC}"
git tag -a "v$VERSION" -m "Release v$VERSION"

# Publish to npm
echo -e "\n${YELLOW}Publishing to npm...${NC}"
npm publish --access public

# Push to git
echo -e "\n${YELLOW}Pushing to git remote...${NC}"
git push origin main --tags

echo -e "\n${GREEN}✅ Successfully published @alias/mcp-scaffold v$VERSION${NC}"
echo -e "${GREEN}Installation: npm install -g @alias/mcp-scaffold${NC}"