#!/bin/bash

# MCP Scaffold Quick Start Script
# This script provides a quick way to get started with the MCP scaffold CLI

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "╔═══════════════════════════════════════════╗"
echo "║      MCP Scaffold CLI - Quick Start       ║"
echo "║         Create MCP Servers Easily         ║"
echo "╚═══════════════════════════════════════════╝"
echo -e "${NC}"

# Function to display examples
show_examples() {
  echo -e "\n${YELLOW}Examples:${NC}"
  echo -e "${GREEN}1. Basic MCP server:${NC}"
  echo "   mcp-scaffold create my-basic-server"
  
  echo -e "\n${GREEN}2. Server with tools and resources:${NC}"
  echo "   mcp-scaffold create my-tool-server --tools --resources"
  
  echo -e "\n${GREEN}3. Monitoring server template:${NC}"
  echo "   mcp-scaffold create my-monitor -t monitoring"
  
  echo -e "\n${GREEN}4. Full-featured server with Docker:${NC}"
  echo "   mcp-scaffold create my-full-server --tools --resources --prompts --docker --elevenlabs"
  
  echo -e "\n${GREEN}5. IT Assistant style server:${NC}"
  echo "   mcp-scaffold create my-it-assistant --tools --resources --prompts --elevenlabs \\"
  echo "     --integrations proxmox,docker,kubernetes"
}

# Function to create a sample project
create_sample() {
  echo -e "\n${YELLOW}Creating a sample MCP server project...${NC}"
  
  PROJECT_NAME="sample-mcp-server-$(date +%s)"
  
  echo -e "${GREEN}Creating: ${PROJECT_NAME}${NC}"
  npx @alias/mcp-scaffold create "$PROJECT_NAME" \
    --description "A sample MCP server to demonstrate capabilities" \
    --author "MCP Developer" \
    --tools \
    --resources \
    --examples
  
  echo -e "\n${GREEN}✅ Sample project created successfully!${NC}"
  echo -e "${YELLOW}To get started:${NC}"
  echo "  cd $PROJECT_NAME"
  echo "  npm install"
  echo "  npm run dev"
  
  echo -e "\n${YELLOW}To add to Claude Desktop:${NC}"
  echo "  npm run claude:add"
}

# Main menu
echo -e "${YELLOW}What would you like to do?${NC}"
echo "1) View examples"
echo "2) Create a sample project"
echo "3) Show available templates"
echo "4) Exit"

read -p "Enter your choice (1-4): " choice

case $choice in
  1)
    show_examples
    ;;
  2)
    create_sample
    ;;
  3)
    npx @alias/mcp-scaffold list-templates
    ;;
  4)
    echo -e "${GREEN}Goodbye!${NC}"
    exit 0
    ;;
  *)
    echo -e "${YELLOW}Invalid choice. Please run the script again.${NC}"
    exit 1
    ;;
esac

echo -e "\n${BLUE}For more information, visit: https://github.com/alias/mcp-scaffold${NC}"