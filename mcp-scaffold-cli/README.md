# @alias/mcp-scaffold

A powerful CLI tool for scaffolding TypeScript-based Model Context Protocol (MCP) servers with best practices, comprehensive templates, and full ecosystem integration.

## 🚀 Quick Start

```bash
# Install globally
npm install -g @alias/mcp-scaffold

# Create your first MCP server
mcp-scaffold create my-awesome-server

# Or use npx (no installation required)
npx @alias/mcp-scaffold create my-awesome-server
```

## 📋 Features

- **🎯 Interactive CLI**: Guided project setup with smart defaults
- **📦 Multiple Templates**: Default, monitoring, automation, API, IoT templates
- **🔧 Feature Selection**: Choose tools, resources, prompts, and more
- **🔌 Integration Support**: Proxmox, Kubernetes, Docker, PostgreSQL, Redis, Elasticsearch
- **🎤 Voice Integration**: Built-in ElevenLabs support for voice interactions
- **🐳 Docker Ready**: Optional Docker configuration with security best practices
- **📝 TypeScript**: Full TypeScript support with proper typing
- **✅ Testing Setup**: Vitest configuration included
- **🔍 Linting & Formatting**: ESLint and Prettier pre-configured
- **📚 Documentation**: Auto-generated README with usage examples
- **🔄 CI/CD**: GitHub Actions workflow included

## 📖 Usage

### Basic Usage

```bash
# Create with interactive prompts
mcp-scaffold create my-server

# Skip prompts and use defaults
mcp-scaffold create my-server -y

# Use a specific template
mcp-scaffold create my-monitor -t monitoring

# Specify features via flags
mcp-scaffold create my-server --tools --resources --docker
```

### Available Commands

```bash
# Create a new MCP server project
mcp-scaffold create [name] [options]

# List available templates
mcp-scaffold list-templates

# Show version
mcp-scaffold --version

# Show help
mcp-scaffold --help
```

### Create Command Options

- `-t, --template <template>` - Use a specific template (default: "default")
- `-d, --description <desc>` - Project description
- `-a, --author <author>` - Author name
- `--tools` - Include tools capability
- `--resources` - Include resources capability
- `--prompts` - Include prompts capability
- `--sampling` - Include sampling capability
- `--roots` - Include roots capability
- `--docker` - Include Docker configuration
- `--elevenlabs` - Include ElevenLabs voice integration
- `--integrations <list>` - Comma-separated integrations (proxmox,kubernetes,docker,postgres,redis,elasticsearch,websocket)
- `--examples` - Include example implementations
- `-y, --yes` - Skip prompts and use defaults

## 🏗️ Project Structure

The generated project follows this structure:

```
my-mcp-server/
├── src/
│   ├── index.ts          # Main server implementation
│   ├── prompts.ts        # Prompt management (if enabled)
│   ├── voice.ts          # Voice integration (if enabled)
│   └── integrations/     # External service integrations
├── dist/                 # Compiled JavaScript
├── tests/               # Test files
├── .env.example         # Environment variables template
├── .gitignore
├── .eslintrc.json
├── .prettierrc
├── package.json
├── tsconfig.json
├── vitest.config.ts
├── README.md
├── LICENSE
├── Dockerfile           # (if Docker enabled)
└── docker-compose.yml   # (if Docker enabled)
```

## 🎨 Templates

### Default Template
Basic MCP server with customizable features. Perfect for general-purpose servers.

### Monitoring Template
Pre-configured with system monitoring tools:
- System information retrieval
- CPU, memory, disk usage monitoring
- Process management
- Network statistics
- Service health checks

### Automation Template (Coming Soon)
Designed for task automation and workflow management.

### API Template (Coming Soon)
REST/GraphQL API integration focused template.

### IoT Template (Coming Soon)
Internet of Things device management and control.

## 🔌 Integrations

### Proxmox
```typescript
// Automatically configured when selected
const proxmox = new ProxmoxClient({
  host: process.env.PROXMOX_HOST,
  tokenId: process.env.PROXMOX_TOKEN_ID,
  tokenSecret: process.env.PROXMOX_TOKEN_SECRET
});
```

### Kubernetes
```typescript
// K8s client with automatic config detection
const k8s = new KubernetesClient();
await k8s.loadConfig(); // Loads from kubeconfig or in-cluster
```

### Docker
```typescript
// Docker API integration
const docker = new Docker({
  socketPath: '/var/run/docker.sock'
});
```

### PostgreSQL
```typescript
// Database connection pool
const pg = new Pool({
  host: process.env.PG_HOST,
  database: process.env.PG_DATABASE,
  // ... other config
});
```

### Redis
```typescript
// Redis client for caching
const redis = createClient({
  url: process.env.REDIS_URL
});
```

### Elasticsearch
```typescript
// Search and analytics
const elastic = new ElasticsearchClient({
  node: process.env.ELASTIC_URL,
  auth: { apiKey: process.env.ELASTIC_API_KEY }
});
```

## 🎯 Examples

### Create an IT Assistant MCP Server

```bash
mcp-scaffold create it-assistant \
  --description "Comprehensive IT infrastructure management assistant" \
  --tools \
  --resources \
  --prompts \
  --elevenlabs \
  --integrations proxmox,docker,kubernetes \
  --docker
```

### Create a Monitoring Server

```bash
mcp-scaffold create system-monitor \
  --template monitoring \
  --description "System monitoring and alerting server" \
  --elevenlabs
```

### Create a Simple Tool Server

```bash
mcp-scaffold create my-tools \
  --description "Collection of utility tools" \
  --tools \
  --examples
```

## 🧪 Testing Your MCP Server

The generated project includes a complete testing setup:

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 🚀 Deploying Your MCP Server

### Local Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build for production
npm run build

# Run production build
npm start
```

### Adding to Claude Desktop

```bash
# Show configuration to add
npm run claude:add

# Or manually add to Claude Desktop config:
{
  "mcpServers": {
    "my-server": {
      "command": "node",
      "args": ["/absolute/path/to/my-server/dist/index.js"]
    }
  }
}
```

### Docker Deployment

```bash
# Build Docker image
npm run docker:build

# Run with Docker
npm run docker:run

# Or use docker-compose
docker-compose up -d
```

## 🛠️ Advanced Configuration

### Custom Tool Implementation

```typescript
// In your generated server
private async myCustomTool(args: any): Promise<string> {
  // Your implementation here
  return `Result: ${args.input}`;
}
```

### Custom Resource Provider

```typescript
private async getMyResource(): Promise<string> {
  // Fetch or generate resource content
  return "Resource content here";
}
```

### Adding Prompts

```typescript
// In prompts.ts
this.prompts.set('analyze-logs', {
  name: 'analyze-logs',
  description: 'Analyze system logs for issues',
  arguments: [{
    name: 'logPath',
    description: 'Path to log file',
    required: true
  }]
});
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- Built for the ALIAS ecosystem
- Powered by the [Model Context Protocol SDK](https://github.com/modelcontextprotocol/sdk)
- Inspired by the need for rapid MCP server development

## 📞 Support

- GitHub Issues: [Report bugs or request features](https://github.com/alias/mcp-scaffold/issues)
- Documentation: [Full documentation](https://alias.dev/mcp-scaffold)
- Community: Join our Discord server

---

Built with ❤️ by the ALIAS team