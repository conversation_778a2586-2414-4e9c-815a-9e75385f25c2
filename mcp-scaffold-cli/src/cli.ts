#!/usr/bin/env node
import { Command } from 'commander';
import { input, select, checkbox, confirm } from '@inquirer/prompts';
import chalk from 'chalk';
import ora from 'ora';
import path from 'path';
import { fileURLToPath } from 'url';
import { scaffoldProject } from './scaffold.js';
import { validateProjectName } from './utils.js';
import type { ProjectConfig, ToolDefinition, ResourceDefinition } from './types.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const program = new Command();

program
  .name('mcp-scaffold')
  .description('Scaffold a new TypeScript MCP server for the ALIAS ecosystem')
  .version('1.0.0');

program
  .command('create [name]')
  .description('Create a new MCP server project')
  .option('-t, --template <template>', 'Use a specific template', 'default')
  .option('-y, --yes', 'Skip prompts and use defaults')
  .action(async (name, options) => {
    console.log(chalk.cyan('\n🚀 MCP Server Scaffold - ALIAS Edition\n'));

    try {
      // Get project name
      const projectName = name || await input({
        message: 'Project name:',
        default: 'my-mcp-server',
        validate: (value) => {
          const validation = validateProjectName(value);
          return validation.valid || validation.error || 'Invalid project name';
        }
      });

      // Skip prompts if --yes flag is used
      if (options.yes) {
        const config: ProjectConfig = {
          name: projectName,
          description: 'An MCP server for the ALIAS ecosystem',
          author: 'ALIAS',
          template: options.template || 'default',
          features: ['tools', 'resources', 'prompts'],
          integrations: [],
          tools: [],
          resources: [],
          useDocker: false,
          useElevenLabs: false,
          addExamples: true
        };

        const spinner = ora('Creating your MCP server...').start();
        try {
          await scaffoldProject(config);
          spinner.succeed('Project created successfully!');
          
          console.log('\n' + chalk.green('✅ Success!'));
          console.log(chalk.cyan(`\nProject created at: ${path.join(process.cwd(), projectName)}`));
          console.log(chalk.yellow('\nNext steps:'));
          console.log(chalk.white(`  cd ${projectName}`));
          console.log(chalk.white('  npm install'));
          console.log(chalk.white('  npm run dev\n'));
        } catch (error) {
          spinner.fail('Failed to create project');
          throw error;
        }
        return;
      }

      // Interactive prompts
      const description = await input({
        message: 'Project description:',
        default: 'An MCP server for the ALIAS ecosystem'
      });

      const author = await input({
        message: 'Author:',
        default: 'ALIAS'
      });

      const features = await checkbox({
        message: 'Select features to include:',
        choices: [
          { name: 'Tools (executable functions)', value: 'tools', checked: true },
          { name: 'Resources (data/content providers)', value: 'resources', checked: true },
          { name: 'Prompts (AI interaction templates)', value: 'prompts', checked: true },
          { name: 'Sampling (LLM integration)', value: 'sampling', checked: false },
          { name: 'Roots (file system access)', value: 'roots', checked: false }
        ]
      });

      const integrations = await checkbox({
        message: 'Select integrations:',
        choices: [
          { name: 'Proxmox VE', value: 'proxmox' },
          { name: 'Kubernetes', value: 'kubernetes' },
          { name: 'Docker', value: 'docker' },
          { name: 'PostgreSQL', value: 'postgres' },
          { name: 'Redis', value: 'redis' },
          { name: 'Elasticsearch', value: 'elasticsearch' },
          { name: 'REST API Client', value: 'rest' },
          { name: 'WebSocket', value: 'websocket' },
          { name: 'File System', value: 'filesystem' },
          { name: 'Shell Commands', value: 'shell' }
        ]
      });

      const useDocker = await confirm({
        message: 'Include Docker configuration?',
        default: true
      });

      const useElevenLabs = await confirm({
        message: 'Include ElevenLabs voice integration?',
        default: false
      });

      const addExamples = await confirm({
        message: 'Add example implementations?',
        default: true
      });

      // Tool definitions
      let tools: ToolDefinition[] = [];
      if (features.includes('tools')) {
        console.log(chalk.yellow('\n📦 Let\'s define some tools...\n'));
        
        const addMoreTools = await confirm({
          message: 'Would you like to define custom tools?',
          default: true
        });

        if (addMoreTools) {
          let addAnother = true;
          while (addAnother) {
            const toolName = await input({
              message: 'Tool name (e.g., get_status):',
              validate: (value) => value.length > 0 || 'Tool name is required'
            });

            const toolDescription = await input({
              message: 'Tool description:',
              validate: (value) => value.length > 0 || 'Description is required'
            });

            const toolCategory = await select({
              message: 'Tool category:',
              choices: [
                { name: 'Query/Read', value: 'query' },
                { name: 'Create/Write', value: 'create' },
                { name: 'Update/Modify', value: 'update' },
                { name: 'Delete/Remove', value: 'delete' },
                { name: 'Execute/Action', value: 'execute' },
                { name: 'Monitor/Watch', value: 'monitor' }
              ]
            });

            tools.push({
              name: toolName,
              description: toolDescription,
              category: toolCategory
            });

            addAnother = await confirm({
              message: 'Add another tool?',
              default: false
            });
          }
        }
      }

      // Resource definitions
      let resources: ResourceDefinition[] = [];
      if (features.includes('resources')) {
        console.log(chalk.yellow('\n📚 Let\'s define some resources...\n'));
        
        const addMoreResources = await confirm({
          message: 'Would you like to define custom resources?',
          default: true
        });

        if (addMoreResources) {
          let addAnother = true;
          while (addAnother) {
            const resourceName = await input({
              message: 'Resource name (e.g., documentation):',
              validate: (value) => value.length > 0 || 'Resource name is required'
            });

            const resourceDescription = await input({
              message: 'Resource description:',
              validate: (value) => value.length > 0 || 'Description is required'
            });

            const resourceType = await select({
              message: 'Resource type:',
              choices: [
                { name: 'Markdown Documentation', value: 'markdown' },
                { name: 'JSON Data', value: 'json' },
                { name: 'YAML Configuration', value: 'yaml' },
                { name: 'Plain Text', value: 'text' },
                { name: 'HTML Content', value: 'html' },
                { name: 'CSV Data', value: 'csv' }
              ]
            });

            resources.push({
              name: resourceName,
              description: resourceDescription,
              mimeType: resourceType
            });

            addAnother = await confirm({
              message: 'Add another resource?',
              default: false
            });
          }
        }
      }

      const config: ProjectConfig = {
        name: projectName,
        description,
        author,
        template: options.template || 'default',
        features,
        integrations,
        tools,
        resources,
        useDocker,
        useElevenLabs,
        addExamples
      };

      // Scaffold the project
      const spinner = ora('Creating your MCP server...').start();
      
      try {
        await scaffoldProject(config);
        spinner.succeed('Project created successfully!');
        
        console.log(chalk.green(`\n✨ Your MCP server "${projectName}" is ready!\n`));
        console.log(chalk.cyan('Next steps:'));
        console.log(chalk.white(`  cd ${projectName}`));
        console.log(chalk.white('  npm install'));
        console.log(chalk.white('  npm run build'));
        console.log(chalk.white('  npm start\n'));
        
        if (useDocker) {
          console.log(chalk.cyan('Docker commands:'));
          console.log(chalk.white('  npm run docker:build'));
          console.log(chalk.white('  npm run docker:run\n'));
        }
        
        console.log(chalk.yellow('To add to Claude Desktop:'));
        console.log(chalk.white('  npm run claude:add\n'));
        
      } catch (error) {
        spinner.fail('Failed to create project');
        throw error;
      }
      
    } catch (error) {
      console.error(chalk.red('\n❌ Error:'), error instanceof Error ? error.message : error);
      process.exit(1);
    }
  });

program
  .command('templates')
  .description('List available templates')
  .action(() => {
    console.log(chalk.cyan('\n📋 Available Templates:\n'));
    console.log('  • default    - Basic MCP server with tools and resources');
    console.log('  • monitoring - System monitoring and alerting server');
    console.log('  • automation - Task automation and scheduling server');
    console.log('  • data       - Data processing and analytics server');
    console.log('  • api        - REST API integration server');
    console.log('  • iot        - IoT device management server\n');
  });

program.parse();