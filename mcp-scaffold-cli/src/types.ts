export interface ProjectConfig {
  name: string;
  description: string;
  author: string;
  template: string;
  features: string[];
  integrations: string[];
  tools: ToolDefinition[];
  resources: ResourceDefinition[];
  useDocker: boolean;
  useElevenLabs: boolean;
  addExamples: boolean;
}

export interface ToolDefinition {
  name: string;
  description: string;
  category: string;
}

export interface ResourceDefinition {
  name: string;
  description: string;
  mimeType: string;
}

export interface TemplateData extends ProjectConfig {
  packageName: string;
  className: string;
  hasTools: boolean;
  hasResources: boolean;
  hasPrompts: boolean;
  hasSampling: boolean;
  hasRoots: boolean;
  integrationImports: string;
  integrationSetup: string;
  toolImplementations: string;
  resourceImplementations: string;
  year: number;
}