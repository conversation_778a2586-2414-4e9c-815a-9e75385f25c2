import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import Handlebars from 'handlebars';
import { execa } from 'execa';
import type { ProjectConfig, TemplateData } from './types.js';
import { 
  toPascalCase, 
  generateIntegrationImports, 
  generateIntegrationSetup,
  generateToolImplementation,
  generateResourceImplementation 
} from './utils.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Register Handlebars helpers
Handlebars.registerHelper('if_eq', function(this: any, a: any, b: any, options: any) {
  return a === b ? options.fn(this) : options.inverse(this);
});

Handlebars.registerHelper('includes', function(this: any, arr: any[], val: any, options: any) {
  return arr && arr.includes(val) ? options.fn(this) : options.inverse(this);
});

export async function scaffoldProject(config: ProjectConfig): Promise<void> {
  const projectPath = path.join(process.cwd(), config.name);
  
  // Check if directory already exists
  if (await fs.pathExists(projectPath)) {
    throw new Error(`Directory ${config.name} already exists`);
  }
  
  // Create project directory
  await fs.ensureDir(projectPath);
  
  // Prepare template data
  const templateData: TemplateData = {
    ...config,
    packageName: config.name,
    className: toPascalCase(config.name) + 'Server',
    hasTools: config.features.includes('tools'),
    hasResources: config.features.includes('resources'),
    hasPrompts: config.features.includes('prompts'),
    hasSampling: config.features.includes('sampling'),
    hasRoots: config.features.includes('roots'),
    integrationImports: generateIntegrationImports(config.integrations),
    integrationSetup: generateIntegrationSetup(config.integrations),
    toolImplementations: config.tools.map(generateToolImplementation).join(''),
    resourceImplementations: config.resources.map(generateResourceImplementation).join(''),
    year: new Date().getFullYear()
  };
  
  // Copy and process templates
  const templateDir = path.join(__dirname, '..', 'templates', 'default');
  await processDirectory(templateDir, projectPath, templateData);
  
  // Create additional files based on features
  if (config.features.includes('prompts')) {
    await createPromptsFile(projectPath, templateData);
  }
  
  if (config.integrations.length > 0) {
    await createIntegrationFiles(projectPath, config.integrations);
  }
  
  if (config.useDocker) {
    await createDockerFiles(projectPath, templateData);
  }
  
  if (config.useElevenLabs) {
    await createElevenLabsIntegration(projectPath);
  }
  
  // Initialize git repository
  try {
    await execa('git', ['init'], { cwd: projectPath });
    await execa('git', ['add', '.'], { cwd: projectPath });
    await execa('git', ['commit', '-m', 'Initial commit'], { cwd: projectPath });
  } catch (error) {
    // Git initialization is optional, don't fail if it doesn't work
  }
}

async function processDirectory(
  sourceDir: string, 
  targetDir: string, 
  data: TemplateData
): Promise<void> {
  const files = await fs.readdir(sourceDir);
  
  for (const file of files) {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file.replace('.hbs', ''));
    
    const stats = await fs.stat(sourcePath);
    
    if (stats.isDirectory()) {
      await fs.ensureDir(targetPath);
      await processDirectory(sourcePath, targetPath, data);
    } else if (file.endsWith('.hbs')) {
      const template = await fs.readFile(sourcePath, 'utf-8');
      const compiled = Handlebars.compile(template);
      const result = compiled(data);
      await fs.writeFile(targetPath, result);
    } else {
      await fs.copy(sourcePath, targetPath);
    }
  }
}

async function createPromptsFile(projectPath: string, data: TemplateData): Promise<void> {
  const promptsContent = `import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js';

export interface Prompt {
  name: string;
  description: string;
  arguments?: Array<{
    name: string;
    description: string;
    required?: boolean;
  }>;
}

export class PromptsManager {
  private prompts: Map<string, Prompt> = new Map();
  
  constructor() {
    this.registerDefaultPrompts();
  }
  
  private registerDefaultPrompts(): void {
    // Example prompts
    this.prompts.set('analyze', {
      name: 'analyze',
      description: 'Analyze data and provide insights',
      arguments: [
        {
          name: 'data',
          description: 'The data to analyze',
          required: true
        },
        {
          name: 'format',
          description: 'Output format (summary, detailed, report)',
          required: false
        }
      ]
    });
    
    this.prompts.set('generate', {
      name: 'generate',
      description: 'Generate content based on template',
      arguments: [
        {
          name: 'template',
          description: 'Template name or content',
          required: true
        },
        {
          name: 'variables',
          description: 'Variables to inject into template',
          required: false
        }
      ]
    });
  }
  
  getPrompt(name: string): Prompt | undefined {
    return this.prompts.get(name);
  }
  
  listPrompts(): Prompt[] {
    return Array.from(this.prompts.values());
  }
  
  async generatePrompt(name: string, args: Record<string, any>): Promise<string> {
    const prompt = this.prompts.get(name);
    if (!prompt) {
      throw new McpError(ErrorCode.InvalidRequest, \`Unknown prompt: \${name}\`);
    }
    
    // Validate required arguments
    if (prompt.arguments) {
      for (const arg of prompt.arguments) {
        if (arg.required && !(arg.name in args)) {
          throw new McpError(
            ErrorCode.InvalidRequest, 
            \`Missing required argument: \${arg.name}\`
          );
        }
      }
    }
    
    // Generate prompt based on template and arguments
    // This is a simple example - you can make this more sophisticated
    return \`[\${prompt.name}] \${JSON.stringify(args)}\`;
  }
}
`;
  
  await fs.writeFile(
    path.join(projectPath, 'src', 'prompts.ts'),
    promptsContent
  );
}

async function createIntegrationFiles(projectPath: string, integrations: string[]): Promise<void> {
  const integrationsDir = path.join(projectPath, 'src', 'integrations');
  await fs.ensureDir(integrationsDir);
  
  if (integrations.includes('proxmox')) {
    await fs.writeFile(
      path.join(integrationsDir, 'proxmox.ts'),
      `import axios, { AxiosInstance } from 'axios';

export interface ProxmoxConfig {
  host: string;
  username?: string;
  password?: string;
  tokenId?: string;
  tokenSecret?: string;
}

export class ProxmoxClient {
  private client: AxiosInstance;
  private config: ProxmoxConfig;
  
  constructor(config: ProxmoxConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: \`https://\${config.host}:8006/api2/json\`,
      headers: config.tokenId && config.tokenSecret ? {
        'Authorization': \`PVEAPIToken=\${config.tokenId}=\${config.tokenSecret}\`
      } : {},
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false
      })
    });
  }
  
  async getNodes(): Promise<any[]> {
    const response = await this.client.get('/nodes');
    return response.data.data;
  }
  
  async getVMs(node: string): Promise<any[]> {
    const response = await this.client.get(\`/nodes/\${node}/qemu\`);
    return response.data.data;
  }
  
  async getVMStatus(node: string, vmid: string): Promise<any> {
    const response = await this.client.get(\`/nodes/\${node}/qemu/\${vmid}/status/current\`);
    return response.data.data;
  }
}
`
    );
  }
  
  if (integrations.includes('kubernetes')) {
    await fs.writeFile(
      path.join(integrationsDir, 'kubernetes.ts'),
      `import * as k8s from '@kubernetes/client-node';

export class KubernetesClient {
  private kc: k8s.KubeConfig;
  private k8sApi: k8s.CoreV1Api;
  
  constructor() {
    this.kc = new k8s.KubeConfig();
  }
  
  async loadConfig(): Promise<void> {
    try {
      this.kc.loadFromDefault();
    } catch (error) {
      // Try in-cluster config if default fails
      this.kc.loadFromCluster();
    }
    
    this.k8sApi = this.kc.makeApiClient(k8s.CoreV1Api);
  }
  
  async listPods(namespace: string = 'default'): Promise<any[]> {
    const response = await this.k8sApi.listNamespacedPod(namespace);
    return response.body.items;
  }
  
  async getPodLogs(name: string, namespace: string = 'default'): Promise<string> {
    const response = await this.k8sApi.readNamespacedPodLog(name, namespace);
    return response.body;
  }
}
`
    );
  }
}

async function createDockerFiles(projectPath: string, data: TemplateData): Promise<void> {
  const dockerfileContent = `FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose MCP stdio
EXPOSE 3000

# Start the server
CMD ["node", "dist/index.js"]
`;
  
  const dockerignoreContent = `node_modules
dist
.git
.env.local
*.log
.DS_Store
`;
  
  const dockerComposeContent = `version: '3.8'

services:
  ${data.packageName}:
    build: .
    container_name: ${data.packageName}
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    volumes:
      - ./data:/app/data
    networks:
      - mcp-network

networks:
  mcp-network:
    external: true
`;
  
  await fs.writeFile(path.join(projectPath, 'Dockerfile'), dockerfileContent);
  await fs.writeFile(path.join(projectPath, '.dockerignore'), dockerignoreContent);
  await fs.writeFile(path.join(projectPath, 'docker-compose.yml'), dockerComposeContent);
}

async function createElevenLabsIntegration(projectPath: string): Promise<void> {
  const voiceContent = `import { ElevenLabsClient } from 'elevenlabs';
import { createWriteStream } from 'fs';
import { mkdir } from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class VoiceService {
  private client: ElevenLabsClient;
  private outputDir: string;
  
  constructor() {
    this.client = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY
    });
    
    this.outputDir = path.join(
      process.env.HOME || '', 
      'Music', 
      'MCP-Voice-Output'
    );
  }
  
  async initialize(): Promise<void> {
    await mkdir(this.outputDir, { recursive: true });
  }
  
  async generateSpeech(text: string, voiceId: string = 'EXAVITQu4vr4xnSDxMaL'): Promise<string> {
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const filename = \`alert-\${timestamp}.mp3\`;
    const filepath = path.join(this.outputDir, filename);
    
    const audio = await this.client.generate({
      voice: voiceId,
      text,
      model_id: 'eleven_multilingual_v2'
    });
    
    const writeStream = createWriteStream(filepath);
    audio.pipe(writeStream);
    
    return new Promise((resolve, reject) => {
      writeStream.on('finish', async () => {
        // Play the audio file
        try {
          await execAsync(\`open -a Music "\${filepath}"\`);
        } catch (error) {
          console.error('Failed to play audio:', error);
        }
        resolve(filepath);
      });
      writeStream.on('error', reject);
    });
  }
  
  async sendAlert(message: string, severity: 'info' | 'warning' | 'critical' = 'info'): Promise<void> {
    const voiceMap = {
      'info': 'EXAVITQu4vr4xnSDxMaL',      // Sarah
      'warning': 'ErXwobaYiN019PkySvjV',   // Antoni 
      'critical': '21m00Tcm4TlvDq8ikWAM'    // Rachel
    };
    
    await this.generateSpeech(message, voiceMap[severity]);
  }
}
`;
  
  await fs.writeFile(
    path.join(projectPath, 'src', 'voice.ts'),
    voiceContent
  );
}