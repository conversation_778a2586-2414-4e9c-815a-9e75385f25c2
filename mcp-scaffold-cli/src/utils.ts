import validateNpmPackageName from 'validate-npm-package-name';
import type { ToolDefinition, ResourceDefinition } from './types.js';

export function validateProjectName(name: string): { valid: boolean; error?: string } {
  const result = validateNpmPackageName(name);
  
  if (result.validForNewPackages) {
    return { valid: true };
  }
  
  const errors = [...(result.errors || []), ...(result.warnings || [])];
  return {
    valid: false,
    error: errors.join(', ')
  };
}

export function toPascalCase(str: string): string {
  return str
    .split(/[-_\s]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

export function toKebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

export function generateIntegrationImports(integrations: string[]): string {
  const imports: string[] = [];
  
  if (integrations.includes('proxmox')) {
    imports.push("import { ProxmoxClient } from './integrations/proxmox.js';");
  }
  if (integrations.includes('kubernetes')) {
    imports.push("import { KubernetesClient } from './integrations/kubernetes.js';");
  }
  if (integrations.includes('docker')) {
    imports.push("import Docker from 'dockerode';");
  }
  if (integrations.includes('postgres')) {
    imports.push("import { Pool } from 'pg';");
  }
  if (integrations.includes('redis')) {
    imports.push("import { createClient } from 'redis';");
  }
  if (integrations.includes('elasticsearch')) {
    imports.push("import { Client as ElasticsearchClient } from '@elastic/elasticsearch';");
  }
  if (integrations.includes('rest')) {
    imports.push("import axios from 'axios';");
  }
  if (integrations.includes('websocket')) {
    imports.push("import WebSocket from 'ws';");
  }
  if (integrations.includes('filesystem')) {
    imports.push("import { promises as fs } from 'fs';\nimport path from 'path';");
  }
  if (integrations.includes('shell')) {
    imports.push("import { exec } from 'child_process';\nimport { promisify } from 'util';\nconst execAsync = promisify(exec);");
  }
  
  return imports.join('\n');
}

export function generateIntegrationSetup(integrations: string[]): string {
  const setups: string[] = [];
  
  if (integrations.includes('proxmox')) {
    setups.push(`
    // Proxmox integration
    this.proxmox = new ProxmoxClient({
      host: process.env.PROXMOX_HOST || 'localhost',
      username: process.env.PROXMOX_USERNAME || 'root@pam',
      password: process.env.PROXMOX_PASSWORD || '',
      tokenId: process.env.PROXMOX_TOKEN_ID,
      tokenSecret: process.env.PROXMOX_TOKEN_SECRET
    });`);
  }
  
  if (integrations.includes('kubernetes')) {
    setups.push(`
    // Kubernetes integration
    this.k8s = new KubernetesClient();
    await this.k8s.loadConfig();`);
  }
  
  if (integrations.includes('docker')) {
    setups.push(`
    // Docker integration
    this.docker = new Docker({
      socketPath: process.env.DOCKER_SOCKET || '/var/run/docker.sock'
    });`);
  }
  
  if (integrations.includes('postgres')) {
    setups.push(`
    // PostgreSQL integration
    this.pg = new Pool({
      host: process.env.PG_HOST || 'localhost',
      port: parseInt(process.env.PG_PORT || '5432'),
      database: process.env.PG_DATABASE || 'mcp',
      user: process.env.PG_USER || 'postgres',
      password: process.env.PG_PASSWORD || ''
    });`);
  }
  
  if (integrations.includes('redis')) {
    setups.push(`
    // Redis integration
    this.redis = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    await this.redis.connect();`);
  }
  
  if (integrations.includes('elasticsearch')) {
    setups.push(`
    // Elasticsearch integration
    this.elastic = new ElasticsearchClient({
      node: process.env.ELASTIC_URL || 'http://localhost:9200',
      auth: process.env.ELASTIC_API_KEY ? {
        apiKey: process.env.ELASTIC_API_KEY
      } : undefined
    });`);
  }
  
  return setups.join('\n');
}

export function generateToolImplementation(tool: ToolDefinition): string {
  const paramName = tool.name.replace(/_/g, '');
  
  return `
        case '${tool.name}':
          return {
            content: [{
              type: 'text',
              text: await this.${paramName}(args)
            }]
          };`;
}

export function generateResourceImplementation(resource: ResourceDefinition): string {
  const uriName = toKebabCase(resource.name);
  const methodName = `get${toPascalCase(resource.name)}`;
  
  return `
        case '${uriName}':
          return {
            contents: [{
              uri,
              mimeType: '${getMimeType(resource.mimeType)}',
              text: await this.${methodName}()
            }]
          };`;
}

function getMimeType(type: string): string {
  const mimeTypes: Record<string, string> = {
    'markdown': 'text/markdown',
    'json': 'application/json',
    'yaml': 'application/x-yaml',
    'text': 'text/plain',
    'html': 'text/html',
    'csv': 'text/csv'
  };
  
  return mimeTypes[type] || 'text/plain';
}