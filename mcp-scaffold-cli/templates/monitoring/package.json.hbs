{"name": "{{packageName}}", "version": "1.0.0", "description": "{{description}}", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "vitest", "inspector": "npx @modelcontextprotocol/inspector dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "zod": "^3.22.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "systeminformation": "^5.21.20", "node-os-utils": "^1.3.7", "@prometheus/client": "^0.3.0", "axios": "^1.6.5"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.3.3", "tsx": "^4.7.0", "vitest": "^1.1.0"}}