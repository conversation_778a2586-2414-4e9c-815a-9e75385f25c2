#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import si from 'systeminformation';
import osu from 'node-os-utils';
import dotenv from 'dotenv';
import winston from 'winston';

dotenv.config();

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

class {{className}} {
  private server: Server;
  
  constructor() {
    this.server = new Server(
      {
        name: '{{packageName}}',
        version: '1.0.0',
        description: '{{description}}'
      },
      {
        capabilities: {
          tools: {},
          resources: {}
        }
      }
    );
    
    this.setupHandlers();
  }
  
  private setupHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_system_info',
          description: 'Get comprehensive system information',
          inputSchema: { type: 'object', properties: {} }
        },
        {
          name: 'get_cpu_usage',
          description: 'Get current CPU usage and statistics',
          inputSchema: { type: 'object', properties: {} }
        },
        {
          name: 'get_memory_usage',
          description: 'Get memory usage statistics',
          inputSchema: { type: 'object', properties: {} }
        },
        {
          name: 'get_disk_usage',
          description: 'Get disk usage for all mounted drives',
          inputSchema: { type: 'object', properties: {} }
        },
        {
          name: 'get_network_stats',
          description: 'Get network interface statistics',
          inputSchema: { type: 'object', properties: {} }
        },
        {
          name: 'get_process_list',
          description: 'Get list of running processes',
          inputSchema: {
            type: 'object',
            properties: {
              sortBy: {
                type: 'string',
                enum: ['cpu', 'memory', 'pid', 'name'],
                description: 'Sort processes by this field'
              },
              limit: {
                type: 'number',
                description: 'Limit number of processes returned'
              }
            }
          }
        },
        {
          name: 'check_service_health',
          description: 'Check health of a service via HTTP endpoint',
          inputSchema: {
            type: 'object',
            properties: {
              url: {
                type: 'string',
                description: 'Health check endpoint URL'
              },
              timeout: {
                type: 'number',
                description: 'Timeout in milliseconds'
              }
            },
            required: ['url']
          }
        }
      ]
    }));
    
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        switch (name) {
          case 'get_system_info':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getSystemInfo(), null, 2)
              }]
            };
            
          case 'get_cpu_usage':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getCpuUsage(), null, 2)
              }]
            };
            
          case 'get_memory_usage':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getMemoryUsage(), null, 2)
              }]
            };
            
          case 'get_disk_usage':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getDiskUsage(), null, 2)
              }]
            };
            
          case 'get_network_stats':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getNetworkStats(), null, 2)
              }]
            };
            
          case 'get_process_list':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.getProcessList(args), null, 2)
              }]
            };
            
          case 'check_service_health':
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(await this.checkServiceHealth(args), null, 2)
              }]
            };
            
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        logger.error(`Error executing tool ${name}:`, error);
        return {
          content: [{
            type: 'text',
            text: `Error: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    });
  }
  
  private async getSystemInfo(): Promise<any> {
    const [system, os, cpu, time] = await Promise.all([
      si.system(),
      si.osInfo(),
      si.cpu(),
      si.time()
    ]);
    
    return { system, os, cpu, time };
  }
  
  private async getCpuUsage(): Promise<any> {
    const [currentLoad, cpuTemperature] = await Promise.all([
      si.currentLoad(),
      si.cpuTemperature()
    ]);
    
    return { currentLoad, cpuTemperature };
  }
  
  private async getMemoryUsage(): Promise<any> {
    const mem = await si.mem();
    return {
      total: mem.total,
      free: mem.free,
      used: mem.used,
      active: mem.active,
      available: mem.available,
      usagePercent: (mem.used / mem.total) * 100
    };
  }
  
  private async getDiskUsage(): Promise<any> {
    const disks = await si.fsSize();
    return disks.map(disk => ({
      filesystem: disk.fs,
      size: disk.size,
      used: disk.used,
      available: disk.available,
      usePercent: disk.use,
      mount: disk.mount
    }));
  }
  
  private async getNetworkStats(): Promise<any> {
    const [interfaces, stats] = await Promise.all([
      si.networkInterfaces(),
      si.networkStats()
    ]);
    
    return { interfaces, stats };
  }
  
  private async getProcessList(args: any): Promise<any> {
    const processes = await si.processes();
    let list = processes.list;
    
    // Sort if requested
    if (args.sortBy) {
      list.sort((a, b) => {
        switch (args.sortBy) {
          case 'cpu': return b.cpu - a.cpu;
          case 'memory': return b.mem - a.mem;
          case 'pid': return a.pid - b.pid;
          case 'name': return a.name.localeCompare(b.name);
          default: return 0;
        }
      });
    }
    
    // Limit if requested
    if (args.limit) {
      list = list.slice(0, args.limit);
    }
    
    return {
      total: processes.all,
      running: processes.running,
      blocked: processes.blocked,
      sleeping: processes.sleeping,
      processes: list
    };
  }
  
  private async checkServiceHealth(args: any): Promise<any> {
    const axios = require('axios');
    const startTime = Date.now();
    
    try {
      const response = await axios.get(args.url, {
        timeout: args.timeout || 5000,
        validateStatus: () => true
      });
      
      return {
        healthy: response.status >= 200 && response.status < 300,
        status: response.status,
        statusText: response.statusText,
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        healthy: false,
        error: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  async start(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    logger.info('{{packageName}} monitoring server is running');
  }
}

const server = new {{className}}();
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});