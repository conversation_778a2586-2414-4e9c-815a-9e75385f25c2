{
  "name": "{{packageName}}",
  "version": "1.0.0",
  "description": "{{description}}",
  "type": "module",
  "main": "dist/index.js",
  "scripts": {
    "build": "tsc",
    "start": "node dist/index.js",
    "dev": "tsx watch src/index.ts",
    "test": "vitest",
    "inspector": "npx @modelcontextprotocol/inspector dist/index.js",
    "claude:add": "echo 'Add to Claude Desktop configuration:' && echo '{{packageName}}: { command: \"node\", args: [\"{{../projectPath}}/dist/index.js\"] }'",
    {{#if useDocker}}
    "docker:build": "docker build -t {{packageName}} .",
    "docker:run": "docker run --rm -it {{packageName}}",
    {{/if}}
    "lint": "eslint src --ext .ts",
    "format": "prettier --write 'src/**/*.ts'"
  },
  "keywords": [
    "mcp",
    "model-context-protocol",
    "ai",
    "assistant",
    "alias"
  ],
  "author": "{{author}}",
  "license": "MIT",
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.5.0",
    "zod": "^3.22.4",
    "dotenv": "^16.3.1",
    {{#includes integrations "proxmox"}}
    "axios": "^1.6.5",
    {{/includes}}
    {{#includes integrations "kubernetes"}}
    "@kubernetes/client-node": "^0.20.0",
    {{/includes}}
    {{#includes integrations "docker"}}
    "dockerode": "^4.0.2",
    {{/includes}}
    {{#includes integrations "postgres"}}
    "pg": "^8.11.3",
    {{/includes}}
    {{#includes integrations "redis"}}
    "redis": "^4.6.12",
    {{/includes}}
    {{#includes integrations "elasticsearch"}}
    "@elastic/elasticsearch": "^8.11.0",
    {{/includes}}
    {{#includes integrations "websocket"}}
    "ws": "^8.16.0",
    {{/includes}}
    {{#if useElevenLabs}}
    "elevenlabs": "^0.2.2",
    {{/if}}
    "winston": "^3.11.0"
  },
  "devDependencies": {
    "@types/node": "^20.10.5",
    "@typescript-eslint/eslint-plugin": "^6.15.0",
    "@typescript-eslint/parser": "^6.15.0",
    "eslint": "^8.56.0",
    "prettier": "^3.1.1",
    "tsx": "^4.7.0",
    "typescript": "^5.3.3",
    "vitest": "^1.1.0",
    {{#includes integrations "docker"}}
    "@types/dockerode": "^3.3.23",
    {{/includes}}
    {{#includes integrations "postgres"}}
    "@types/pg": "^8.10.9",
    {{/includes}}
    {{#includes integrations "websocket"}}
    "@types/ws": "^8.5.10"
    {{else}}
    "@types/ws": "^8.5.10"
    {{/includes}}
  },
  "engines": {
    "node": ">=18.0.0"
  }
}