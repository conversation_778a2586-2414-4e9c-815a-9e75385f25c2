#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
{{#if hasResources}}
import { ListResourcesRequestSchema, ReadResourceRequestSchema } from '@modelcontextprotocol/sdk/types.js';
{{/if}}
{{#if hasPrompts}}
import { ListPromptsRequestSchema, GetPromptRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { PromptsManager } from './prompts.js';
{{/if}}
import dotenv from 'dotenv';
import { z } from 'zod';
import winston from 'winston';
{{#if useElevenLabs}}
import { VoiceService } from './voice.js';
{{/if}}
{{{integrationImports}}}

// Load environment variables
dotenv.config();

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

class {{className}} {
  private server: Server;
  {{#if hasPrompts}}
  private prompts: PromptsManager;
  {{/if}}
  {{#if useElevenLabs}}
  private voice: VoiceService;
  {{/if}}
  {{#includes integrations "proxmox"}}
  private proxmox: ProxmoxClient;
  {{/includes}}
  {{#includes integrations "kubernetes"}}
  private k8s: KubernetesClient;
  {{/includes}}
  {{#includes integrations "docker"}}
  private docker: Docker;
  {{/includes}}
  {{#includes integrations "postgres"}}
  private pg: Pool;
  {{/includes}}
  {{#includes integrations "redis"}}
  private redis: ReturnType<typeof createClient>;
  {{/includes}}
  {{#includes integrations "elasticsearch"}}
  private elastic: ElasticsearchClient;
  {{/includes}}
  
  constructor() {
    this.server = new Server(
      {
        name: '{{packageName}}',
        version: '1.0.0',
        description: '{{description}}'
      },
      {
        capabilities: {
          {{#if hasTools}}
          tools: {},
          {{/if}}
          {{#if hasResources}}
          resources: {},
          {{/if}}
          {{#if hasPrompts}}
          prompts: {},
          {{/if}}
          {{#if hasSampling}}
          sampling: {},
          {{/if}}
          {{#if hasRoots}}
          roots: {
            listChanged: true
          }
          {{/if}}
        }
      }
    );
    
    {{#if hasPrompts}}
    this.prompts = new PromptsManager();
    {{/if}}
    {{#if useElevenLabs}}
    this.voice = new VoiceService();
    {{/if}}
    
    this.setupHandlers();
    this.setupErrorHandling();
  }
  
  private async initialize(): Promise<void> {
    logger.info('Initializing {{packageName}}...');
    
    {{#if useElevenLabs}}
    await this.voice.initialize();
    {{/if}}
    {{{integrationSetup}}}
    
    logger.info('{{packageName}} initialized successfully');
  }
  
  private setupErrorHandling(): void {
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });
    
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });
  }
  
  private setupHandlers(): void {
    {{#if hasTools}}
    // Tool handlers
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {{#each tools}}
        {
          name: '{{name}}',
          description: '{{description}}',
          inputSchema: {
            type: 'object',
            properties: {
              // TODO: Define input parameters
            }
          }
        },
        {{/each}}
        {{#if addExamples}}
        {
          name: 'example_tool',
          description: 'An example tool that demonstrates basic functionality',
          inputSchema: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                description: 'A message to process'
              }
            },
            required: ['message']
          }
        }
        {{/if}}
      ]
    }));
    
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        switch (name) {
          {{#each tools}}
          case '{{name}}':
            return {
              content: [{
                type: 'text',
                text: await this.{{name}}(args)
              }]
            };
          {{/each}}
          {{#if addExamples}}
          case 'example_tool':
            return {
              content: [{
                type: 'text',
                text: `Processed message: ${args.message}`
              }]
            };
          {{/if}}
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        logger.error(`Error executing tool ${name}:`, error);
        return {
          content: [{
            type: 'text',
            text: `Error: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    });
    {{/if}}
    
    {{#if hasResources}}
    // Resource handlers
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => ({
      resources: [
        {{#each resources}}
        {
          uri: '{{../packageName}}://{{name}}',
          name: '{{name}}',
          description: '{{description}}',
          mimeType: '{{mimeType}}'
        },
        {{/each}}
        {{#if addExamples}}
        {
          uri: '{{packageName}}://example',
          name: 'Example Resource',
          description: 'An example resource',
          mimeType: 'text/plain'
        }
        {{/if}}
      ]
    }));
    
    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const { uri } = request.params;
      
      try {
        switch (uri) {
          {{#each resources}}
          case '{{../packageName}}://{{name}}':
            return {
              contents: [{
                uri,
                mimeType: '{{mimeType}}',
                text: await this.get{{name}}()
              }]
            };
          {{/each}}
          {{#if addExamples}}
          case '{{packageName}}://example':
            return {
              contents: [{
                uri,
                mimeType: 'text/plain',
                text: 'This is example content'
              }]
            };
          {{/if}}
          default:
            throw new Error(`Unknown resource: ${uri}`);
        }
      } catch (error) {
        logger.error(`Error reading resource ${uri}:`, error);
        throw error;
      }
    });
    {{/if}}
    
    {{#if hasPrompts}}
    // Prompt handlers
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => ({
      prompts: this.prompts.listPrompts().map(prompt => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.arguments
      }))
    }));
    
    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        const messages = await this.prompts.generatePrompt(name, args || {});
        return { messages };
      } catch (error) {
        logger.error(`Error getting prompt ${name}:`, error);
        throw error;
      }
    });
    {{/if}}
  }
  
  {{#each tools}}
  private async {{name}}(args: any): Promise<string> {
    // TODO: Implement {{name}}
    logger.info('Executing {{name}}', args);
    return `Result of {{name}}`;
  }
  
  {{/each}}
  {{#each resources}}
  private async get{{name}}(): Promise<string> {
    // TODO: Implement get{{name}}
    logger.info('Getting resource {{name}}');
    return `Content of {{name}}`;
  }
  
  {{/each}}
  async start(): Promise<void> {
    await this.initialize();
    
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    
    logger.info('{{packageName}} MCP server is running');
  }
}

// Start the server
const server = new {{className}}();
server.start().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});