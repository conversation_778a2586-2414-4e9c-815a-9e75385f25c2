{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "eqeqeq": ["error", "always"], "curly": ["error", "all"], "brace-style": ["error", "1tbs"], "indent": ["error", 2, {"SwitchCase": 1}], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "comma-dangle": ["error", "never"], "no-trailing-spaces": "error", "eol-last": ["error", "always"], "max-len": ["warn", {"code": 120, "ignoreUrls": true, "ignoreStrings": true, "ignoreTemplateLiterals": true}]}, "env": {"node": true, "es2022": true}, "ignorePatterns": ["dist/", "node_modules/", "coverage/", "*.js"]}