# Logging Configuration
LOG_LEVEL=info

# Proxmox Configuration (if using Proxmox integration)
PROXMOX_HOST=your-proxmox-host.example.com
PROXMOX_TOKEN_ID=root@pam!your-token
PROXMOX_TOKEN_SECRET=your-token-secret

# PostgreSQL Configuration (if using PostgreSQL integration)
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=mcp_db
PG_USER=postgres
PG_PASSWORD=your-password

# Redis Configuration (if using Redis integration)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Elasticsearch Configuration (if using Elasticsearch integration)
ELASTIC_URL=http://localhost:9200
ELASTIC_API_KEY=your-api-key

# Kubernetes Configuration (if using Kubernetes integration)
# Leave empty to use default kubeconfig
KUBECONFIG=

# Docker Configuration (if using Docker integration)
DOCKER_HOST=
DOCKER_TLS_VERIFY=
DOCKER_CERT_PATH=

# ElevenLabs Configuration (if using voice features)
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_VOICE_ID=EXAVITQu4vr4xnSDxMaL

# WebSocket Configuration (if using WebSocket integration)
WS_PORT=8080
WS_PATH=/ws

# MCP Server Configuration
MCP_SERVER_NAME={{name}}
MCP_SERVER_VERSION=1.0.0

# Feature Flags
ENABLE_METRICS=false
ENABLE_TRACING=false
ENABLE_HEALTH_CHECK=false

# Security
API_KEY=
JWT_SECRET=
ALLOWED_ORIGINS=*

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Rate Limiting
RATE_LIMIT_ENABLED=false
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100