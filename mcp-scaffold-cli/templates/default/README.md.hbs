# {{name}}

{{description}}

## Overview

This MCP (Model Context Protocol) server provides {{description}}. It is part of the ALIAS ecosystem and can be integrated with AI assistants like <PERSON>.

## Features

{{#if hasTools}}
### Tools
The server provides the following tools:
{{#each tools}}
- **{{name}}**: {{description}}
{{/each}}
{{#if addExamples}}
- **example_tool**: An example tool that demonstrates basic functionality
{{/if}}
{{/if}}

{{#if hasResources}}
### Resources
The server provides access to the following resources:
{{#each resources}}
- **{{name}}**: {{description}} ({{mimeType}})
{{/each}}
{{#if addExamples}}
- **example**: An example resource
{{/if}}
{{/if}}

{{#if hasPrompts}}
### Prompts
The server includes AI interaction prompts for enhanced functionality.
{{/if}}

## Installation

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd {{name}}
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the project:
   ```bash
   npm run build
   ```

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# Logging
LOG_LEVEL=info

{{#includes integrations "proxmox"}}
# Proxmox Configuration
PROXMOX_HOST=your-proxmox-host
PROXMOX_TOKEN_ID=your-token-id
PROXMOX_TOKEN_SECRET=your-token-secret
{{/includes}}

{{#includes integrations "postgres"}}
# PostgreSQL Configuration
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=mcp
PG_USER=postgres
PG_PASSWORD=your-password
{{/includes}}

{{#includes integrations "redis"}}
# Redis Configuration
REDIS_URL=redis://localhost:6379
{{/includes}}

{{#includes integrations "elasticsearch"}}
# Elasticsearch Configuration
ELASTIC_URL=http://localhost:9200
ELASTIC_API_KEY=your-api-key
{{/includes}}

{{#if useElevenLabs}}
# ElevenLabs Configuration
ELEVENLABS_API_KEY=your-api-key
{{/if}}
```

## Usage

### Running the Server

```bash
# Production
npm start

# Development (with auto-reload)
npm run dev
```

### Testing with MCP Inspector

```bash
npm run inspector
```

### Adding to Claude Desktop

Add the following to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "{{name}}": {
      "command": "node",
      "args": ["/path/to/{{name}}/dist/index.js"]
    }
  }
}
```

{{#if useDocker}}
### Docker Usage

Build the Docker image:
```bash
npm run docker:build
```

Run the container:
```bash
npm run docker:run
```

Or use Docker Compose:
```bash
docker-compose up -d
```
{{/if}}

## Development

### Project Structure

```
{{name}}/
├── src/
│   ├── index.ts          # Main server implementation
{{#if hasPrompts}}
│   ├── prompts.ts        # Prompt management
{{/if}}
{{#if useElevenLabs}}
│   ├── voice.ts          # Voice integration
{{/if}}
{{#if integrations}}
│   └── integrations/     # External service integrations
{{/if}}
├── dist/                 # Compiled JavaScript
├── package.json
├── tsconfig.json
{{#if useDocker}}
├── Dockerfile
├── docker-compose.yml
{{/if}}
└── README.md
```

### Adding New Tools

1. Add the tool definition to the `tools` array in `src/index.ts`
2. Implement the tool handler method
3. Update the README with the new tool documentation

### Adding New Resources

1. Add the resource definition to the `resources` array
2. Implement the resource getter method
3. Update the README with the new resource documentation

## API Reference

{{#if hasTools}}
### Tools

{{#each tools}}
#### {{name}}

{{description}}

**Parameters:**
- TODO: Document parameters

**Returns:**
- TODO: Document return value

{{/each}}
{{/if}}

{{#if hasResources}}
### Resources

{{#each resources}}
#### {{name}}

{{description}}

**URI:** `{{../name}}://{{name}}`
**Type:** {{mimeType}}

{{/each}}
{{/if}}

## Troubleshooting

### Common Issues

1. **Server won't start**
   - Check that all required environment variables are set
   - Ensure Node.js 18+ is installed
   - Run `npm run build` before starting

2. **Connection errors**
   - Verify network connectivity to external services
   - Check firewall settings
   - Ensure API credentials are valid

3. **TypeScript errors**
   - Run `npm install` to ensure all dependencies are installed
   - Check that `tsconfig.json` is properly configured

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- Built with the [Model Context Protocol SDK](https://github.com/modelcontextprotocol/sdk)
- Part of the ALIAS ecosystem
- Created by {{author}}

---

*Generated with @alias/mcp-scaffold*