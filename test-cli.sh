#!/bin/bash

# Test script for MCP Scaffold CLI

echo "Testing MCP Scaffold CLI..."

# Create test directory
mkdir -p test-projects
cd test-projects

# Test 1: Create basic project with defaults
echo -e "\n🧪 Test 1: Creating basic project with defaults..."
node ../mcp-scaffold-cli/dist/cli.js create test-basic -y

# Check if project was created
if [ -d "test-basic" ]; then
  echo "✅ Basic project created successfully"
  cd test-basic
  echo "📁 Project structure:"
  find . -type f -name "*.ts" -o -name "*.json" | head -10
  cd ..
else
  echo "❌ Failed to create basic project"
fi

# Test 2: List templates
echo -e "\n🧪 Test 2: Listing templates..."
node ../mcp-scaffold-cli/dist/cli.js templates

# Cleanup
cd ..
echo -e "\n🧹 Cleaning up test projects..."
rm -rf test-projects

echo -e "\n✅ Tests completed!"