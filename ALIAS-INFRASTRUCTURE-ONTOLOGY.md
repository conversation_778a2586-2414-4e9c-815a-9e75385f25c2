# ALIAS Infrastructure Ontology & Knowledge Base

## Table of Contents

1. [Infrastructure Hierarchy](#infrastructure-hierarchy)
2. [Configuration Repository](#configuration-repository)
3. [Command Reference](#command-reference)
4. [Script Library](#script-library)
5. [Automation Playbooks](#automation-playbooks)
6. [Emergency Procedures](#emergency-procedures)
7. [Integration Patterns](#integration-patterns)
8. [Security Configurations](#security-configurations)

---

## Infrastructure Hierarchy

```
ALIAS Infrastructure/
├── Physical Layer/
│   ├── Proxmox Host (*************)
│   ├── Network Infrastructure/
│   │   ├── VLANs (10, 20, 30, 40, 50, 60, 70, 90)
│   │   ├── Bridges (vmbr0-vmbr90)
│   │   └── Firewall Zones/
│   └── Storage/
│       ├── Local-LVM
│       ├── PBS Backup Storage
│       └── Future: Ceph/GlusterFS
│
├── Virtualization Layer/
│   ├── VM Templates/
│   │   ├── ubuntu-2204-cloudinit (ID: 9000)
│   │   └── Future: windows-server-2022
│   ├── Resource Pools/
│   │   ├── production/
│   │   ├── staging/
│   │   └── development/
│   └── Existing VMs/
│       ├── VM 102: Home Assistant
│       ├── VM 103: Windows 11
│       ├── LXC 100: PBS
│       └── LXC 101: iVentoy
│
├── Container Platform/
│   ├── Kubernetes Clusters/
│   │   ├── Production (K8s-prod-*)
│   │   ├── Staging (K8s-stage-*)
│   │   └── Development (K8s-dev-*)
│   └── Container Registries/
│       └── Harbor (registry.alias.local)
│
├── Core Services/
│   ├── CI/CD: GitLab
│   ├── IAM: Keycloak
│   ├── Secrets: Vault
│   └── Registry: Harbor
│
├── Monitoring Stack/
│   ├── Metrics: Prometheus
│   ├── Visualization: Grafana
│   ├── Logs: Elasticsearch
│   └── Analysis: Kibana
│
└── Security Layer/
    ├── Firewall: pve-firewall
    ├── IDS/IPS: Suricata
    ├── SIEM: Wazuh
    └── Secrets: HashiCorp Vault
```

---

## Configuration Repository

### 1. Terraform Configurations

#### Main Configuration
```hcl
# Location: /infrastructure-as-code/terraform/main.tf
# Purpose: Root module orchestrating all infrastructure
# Key Variables:
#   - proxmox_token_id: API authentication
#   - proxmox_token_secret: API secret (sensitive)
#   - domain: Base domain (default: alias.local)
```

#### Module Configurations
```yaml
Modules:
  network:
    path: ./modules/network
    purpose: VLAN and network configuration
    outputs:
      - network_bridges
      - network_gateways
  
  kubernetes:
    path: ./modules/kubernetes
    purpose: K8s/K3s cluster deployment
    outputs:
      - cluster_ips
      - kubeconfig_paths
  
  core-services:
    path: ./modules/core-services
    purpose: Essential platform services
    outputs:
      - service_endpoints
      - admin_urls
  
  monitoring:
    path: ./modules/monitoring
    purpose: Observability stack
    outputs:
      - dashboard_urls
      - api_endpoints
```

### 2. Ansible Configurations

#### Inventory Structure
```yaml
# Location: /infrastructure-as-code/ansible/inventory/
production:
  hosts:
    proxmox:
      ansible_host: *************
    k8s-prod-master-[1-3]:
      ansible_host: 10.10.20.[11-13]
    k8s-prod-worker-[1-3]:
      ansible_host: 10.10.20.[21-23]

staging:
  hosts:
    k8s-stage-master:
      ansible_host: ***********
    k8s-stage-worker-[1-2]:
      ansible_host: 10.10.30.[21-22]

development:
  hosts:
    k8s-dev-node:
      ansible_host: ***********
```

#### Playbook Repository
```yaml
# Location: /infrastructure-as-code/ansible/playbooks/
playbooks:
  - site.yml                 # Main orchestration playbook
  - proxmox-setup.yml       # Proxmox host configuration
  - kubernetes-deploy.yml   # Kubernetes cluster setup
  - monitoring-stack.yml    # Deploy monitoring
  - security-hardening.yml  # Security baseline
  - backup-configure.yml    # Backup job setup
  - disaster-recovery.yml   # DR procedures
```

### 3. Kubernetes Manifests

#### Directory Structure
```yaml
# Location: /kubernetes/
base/
  ├── namespaces/
  ├── rbac/
  ├── network-policies/
  └── storage-classes/

overlays/
  ├── production/
  ├── staging/
  └── development/

applications/
  ├── ingress-nginx/
  ├── cert-manager/
  ├── external-dns/
  ├── metrics-server/
  └── cluster-autoscaler/
```

---

## Command Reference

### Proxmox Commands

#### VM Management
```bash
# List all VMs
qm list

# Create VM from template
qm clone 9000 <new-vmid> --name <vm-name> --full

# Start/Stop/Restart VM
qm start <vmid>
qm stop <vmid>
qm restart <vmid>

# VM Configuration
qm set <vmid> --cores 4 --memory 8192
qm resize <vmid> scsi0 +50G

# Snapshot Management
qm snapshot <vmid> <snapname>
qm rollback <vmid> <snapname>
qm delsnapshot <vmid> <snapname>

# Cloud-init Configuration
qm set <vmid> --ciuser ubuntu --sshkeys ~/.ssh/id_ed25519.pub
qm set <vmid> --ipconfig0 ip=dhcp
```

#### Container Management
```bash
# List containers
pct list

# Create container
pct create <vmid> <template> --hostname <name> --storage local-lvm

# Start/Stop container
pct start <vmid>
pct stop <vmid>

# Enter container
pct enter <vmid>
pct exec <vmid> -- <command>
```

#### Storage Commands
```bash
# List storage
pvesm status

# Add storage
pvesm add dir <storage-name> --path /mnt/storage
pvesm add nfs <storage-name> --server <nfs-server> --export <path>

# Remove storage
pvesm remove <storage-name>
```

#### Backup Commands
```bash
# Manual backup
vzdump <vmid> --compress zstd --storage pbs-backup --mode snapshot

# List backups
pvesm list pbs-backup

# Restore backup
qmrestore /var/lib/vz/dump/vzdump-qemu-<vmid>-*.vma.zst <new-vmid>
```

### Kubernetes Commands

#### Cluster Management
```bash
# Context switching
kubectl config get-contexts
kubectl config use-context production

# Node management
kubectl get nodes -o wide
kubectl drain <node-name> --ignore-daemonsets
kubectl uncordon <node-name>

# Namespace operations
kubectl create namespace <name>
kubectl get namespaces
kubectl delete namespace <name>
```

#### Deployment Commands
```bash
# Apply configurations
kubectl apply -f <file.yaml>
kubectl apply -k <kustomization-dir>/

# Rollout management
kubectl rollout status deployment/<name> -n <namespace>
kubectl rollout restart deployment/<name> -n <namespace>
kubectl rollout undo deployment/<name> -n <namespace>

# Scaling
kubectl scale deployment/<name> --replicas=3 -n <namespace>
kubectl autoscale deployment/<name> --min=2 --max=10 --cpu-percent=80
```

#### Troubleshooting
```bash
# Pod debugging
kubectl describe pod <pod-name> -n <namespace>
kubectl logs <pod-name> -n <namespace> -f
kubectl exec -it <pod-name> -n <namespace> -- /bin/bash

# Events
kubectl get events -n <namespace> --sort-by='.lastTimestamp'

# Resource usage
kubectl top nodes
kubectl top pods -n <namespace>
```

### GitLab CI/CD Commands

```bash
# GitLab administration
sudo gitlab-ctl status
sudo gitlab-ctl reconfigure
sudo gitlab-ctl restart

# Backup/Restore
sudo gitlab-backup create
sudo gitlab-backup restore BACKUP=<timestamp>

# Rails console
sudo gitlab-rails console

# Reset root password
sudo gitlab-rake "gitlab:password:reset[root]"
```

### Harbor Registry Commands

```bash
# Docker login
docker login registry.alias.local

# Push image
docker tag <image>:<tag> registry.alias.local/<project>/<image>:<tag>
docker push registry.alias.local/<project>/<image>:<tag>

# Harbor CLI
harbor project create <project-name>
harbor project list
harbor repo list <project-name>
```

### Monitoring Commands

#### Prometheus
```bash
# Reload configuration
curl -X POST http://prometheus.alias.local:9090/-/reload

# Query metrics
curl http://prometheus.alias.local:9090/api/v1/query?query=up

# Check targets
curl http://prometheus.alias.local:9090/api/v1/targets
```

#### Elasticsearch
```bash
# Cluster health
curl -X GET "elasticsearch.alias.local:9200/_cluster/health?pretty"

# Index management
curl -X GET "elasticsearch.alias.local:9200/_cat/indices?v"
curl -X DELETE "elasticsearch.alias.local:9200/<index-name>"

# Create index lifecycle policy
curl -X PUT "elasticsearch.alias.local:9200/_ilm/policy/logs-policy" -H 'Content-Type: application/json' -d'
{
  "policy": {
    "phases": {
      "hot": {
        "actions": {
          "rollover": {
            "max_size": "50GB",
            "max_age": "7d"
          }
        }
      },
      "delete": {
        "min_age": "30d",
        "actions": {
          "delete": {}
        }
      }
    }
  }
}'
```

---

## Script Library

### Infrastructure Scripts

#### Network Setup Script
```bash
#!/bin/bash
# Location: /scripts/setup-networks.sh
# Purpose: Configure VLANs and bridges on Proxmox

VLANS=(10 20 30 40 50 60 70 90)
NETWORKS=(
  "**********/24"  # Management
  "**********/24"  # Production
  "**********/24"  # Staging
  "**********/24"  # Development
  "**********/24"  # DMZ
  "**********/24"  # Storage
  "**********/24"  # Monitoring
  "**********/24"  # Guest
)

for i in ${!VLANS[@]}; do
  vlan=${VLANS[$i]}
  network=${NETWORKS[$i]}
  gateway=$(echo $network | cut -d'/' -f1 | sed 's/\.0$/.1/')
  
  cat >> /etc/network/interfaces <<EOF

auto vmbr${vlan}
iface vmbr${vlan} inet static
    address ${gateway}/24
    bridge-ports eno1.${vlan}
    bridge-stp off
    bridge-fd 0
    bridge-vlan-aware yes
EOF
done

systemctl restart networking
```

#### Kubernetes Bootstrap Script
```bash
#!/bin/bash
# Location: /scripts/bootstrap-k8s.sh
# Purpose: Bootstrap Kubernetes cluster

CLUSTER_TYPE=${1:-development}

if [ "$CLUSTER_TYPE" == "development" ]; then
  # Install K3s
  curl -sfL https://get.k3s.io | sh -s - server \
    --cluster-init \
    --disable traefik \
    --write-kubeconfig-mode 644
else
  # Install RKE2
  curl -sfL https://get.rke2.io | sh -
  systemctl enable rke2-server
  systemctl start rke2-server
  
  # Wait for node to be ready
  export KUBECONFIG=/etc/rancher/rke2/rke2.yaml
  kubectl wait --for=condition=Ready node --all --timeout=300s
fi
```

#### Backup Automation Script
```bash
#!/bin/bash
# Location: /scripts/automated-backup.sh
# Purpose: Automated backup with retention management

BACKUP_STORAGE="pbs-backup"
RETENTION_DAYS=30
EMAIL="<EMAIL>"

# Get all VMs
VMS=$(qm list | awk 'NR>1 {print $1}')

# Backup each VM
for VM in $VMS; do
  echo "Backing up VM $VM..."
  vzdump $VM \
    --compress zstd \
    --storage $BACKUP_STORAGE \
    --mode snapshot \
    --mailto $EMAIL
done

# Clean old backups
find /var/lib/vz/dump/ -name "*.vma.zst" -mtime +$RETENTION_DAYS -delete
```

### Monitoring Scripts

#### Health Check Script
```bash
#!/bin/bash
# Location: /scripts/health-check.sh
# Purpose: Comprehensive health check

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Check Proxmox services
echo "Checking Proxmox Services..."
for service in pve-cluster pvedaemon pveproxy pvestatd; do
  if systemctl is-active --quiet $service; then
    echo -e "${GREEN}✓${NC} $service is running"
  else
    echo -e "${RED}✗${NC} $service is not running"
  fi
done

# Check storage usage
echo -e "\nChecking Storage Usage..."
df -h | grep -E '^/dev/' | while read line; do
  usage=$(echo $line | awk '{print $5}' | sed 's/%//')
  mount=$(echo $line | awk '{print $6}')
  
  if [ $usage -gt 80 ]; then
    echo -e "${RED}✗${NC} $mount is $usage% full"
  elif [ $usage -gt 60 ]; then
    echo -e "${YELLOW}!${NC} $mount is $usage% full"
  else
    echo -e "${GREEN}✓${NC} $mount is $usage% full"
  fi
done

# Check cluster health
echo -e "\nChecking Kubernetes Clusters..."
for context in production staging development; do
  if kubectl --context $context get nodes &>/dev/null; then
    ready=$(kubectl --context $context get nodes -o json | jq '.items[].status.conditions[] | select(.type=="Ready") | .status' | grep -c True)
    total=$(kubectl --context $context get nodes -o json | jq '.items | length')
    
    if [ "$ready" -eq "$total" ]; then
      echo -e "${GREEN}✓${NC} $context: $ready/$total nodes ready"
    else
      echo -e "${YELLOW}!${NC} $context: $ready/$total nodes ready"
    fi
  else
    echo -e "${RED}✗${NC} $context: cluster unreachable"
  fi
done
```

### Security Scripts

#### Security Audit Script
```bash
#!/bin/bash
# Location: /scripts/security-audit.sh
# Purpose: Automated security audit

# Check for failed login attempts
echo "Recent failed SSH attempts:"
journalctl -u ssh --since "24 hours ago" | grep -i failed | tail -10

# Check open ports
echo -e "\nOpen ports:"
ss -tuln | grep LISTEN

# Check for unauthorized SSH keys
echo -e "\nAuthorized SSH keys:"
find /home -name authorized_keys -exec sh -c 'echo "File: {}"; cat {}' \;

# Check sudo access
echo -e "\nUsers with sudo access:"
grep -Po '^sudo:.\K.*$' /etc/group

# Check for world-writable files
echo -e "\nWorld-writable files:"
find / -xdev -type f -perm -0002 -ls 2>/dev/null | head -20
```

---

## Automation Playbooks

### Daily Operations Playbook

```yaml
# Location: /playbooks/daily-operations.yml
---
- name: Daily Operations Playbook
  hosts: all
  tasks:
    - name: Update package cache
      apt:
        update_cache: yes
      when: ansible_os_family == "Debian"
    
    - name: Check disk usage
      shell: df -h | awk '$5+0 > 80 {print $0}'
      register: disk_usage
      changed_when: false
    
    - name: Alert on high disk usage
      mail:
        to: <EMAIL>
        subject: "Disk Usage Alert"
        body: "{{ disk_usage.stdout }}"
      when: disk_usage.stdout != ""
    
    - name: Verify backup completion
      shell: find /var/log/vzdump -name "*.log" -mtime -1 | grep -c "SUCCESS"
      register: backup_count
      changed_when: false
    
    - name: Report backup status
      debug:
        msg: "{{ backup_count.stdout }} successful backups in last 24 hours"
```

### Disaster Recovery Playbook

```yaml
# Location: /playbooks/disaster-recovery.yml
---
- name: Disaster Recovery Playbook
  hosts: proxmox
  vars:
    backup_location: /mnt/backup
    recovery_vmid: 999
  
  tasks:
    - name: List available backups
      find:
        paths: "{{ backup_location }}"
        patterns: "*.vma.zst"
      register: backups
    
    - name: Display recovery options
      debug:
        msg: "Found {{ backups.files | length }} backups"
    
    - name: Restore selected backup
      command: |
        qmrestore {{ selected_backup }} {{ recovery_vmid }} \
          --storage local-lvm \
          --force
      when: selected_backup is defined
```

---

## Emergency Procedures

### System Recovery

#### 1. Proxmox Host Recovery
```bash
# Boot from Proxmox ISO
# Select "Install Proxmox VE (Debug mode)"

# Mount existing installation
mkdir /mnt/pve
mount /dev/mapper/pve-root /mnt/pve

# Backup configuration
cp -R /mnt/pve/etc/pve /root/pve-backup

# Repair installation
proxmox-boot-tool refresh
update-grub
```

#### 2. Kubernetes Cluster Recovery
```bash
# Backup etcd
ETCDCTL_API=3 etcdctl \
  --endpoints=https://127.0.0.1:2379 \
  --cacert=/etc/kubernetes/pki/etcd/ca.crt \
  --cert=/etc/kubernetes/pki/etcd/server.crt \
  --key=/etc/kubernetes/pki/etcd/server.key \
  snapshot save /backup/etcd-snapshot.db

# Restore etcd
ETCDCTL_API=3 etcdctl \
  snapshot restore /backup/etcd-snapshot.db \
  --data-dir=/var/lib/etcd-restore
```

### Network Emergencies

#### VLAN Isolation Breach
```bash
# Immediate isolation
iptables -I FORWARD -i vmbr20 -o vmbr10 -j DROP
iptables -I FORWARD -i vmbr10 -o vmbr20 -j DROP

# Investigate
tcpdump -i vmbr20 -w breach-capture.pcap
```

#### DDoS Mitigation
```bash
# Rate limiting
iptables -A INPUT -p tcp --dport 443 -m limit --limit 100/minute --limit-burst 200 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j DROP

# Block source
iptables -A INPUT -s <attacker-ip> -j DROP
```

---

## Integration Patterns

### CI/CD Integration

```yaml
# GitLab CI Pipeline
# Location: .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

variables:
  REGISTRY: registry.alias.local
  IMAGE: $REGISTRY/$CI_PROJECT_PATH

build:
  stage: build
  script:
    - docker build -t $IMAGE:$CI_COMMIT_SHA .
    - docker push $IMAGE:$CI_COMMIT_SHA

deploy-staging:
  stage: deploy
  script:
    - kubectl --context=staging set image deployment/app app=$IMAGE:$CI_COMMIT_SHA
  only:
    - develop

deploy-production:
  stage: deploy
  script:
    - kubectl --context=production set image deployment/app app=$IMAGE:$CI_COMMIT_SHA
  only:
    - main
  when: manual
```

### Monitoring Integration

```yaml
# Prometheus ServiceMonitor
# Location: /kubernetes/monitoring/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: app-metrics
  namespace: monitoring
spec:
  selector:
    matchLabels:
      metrics: enabled
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### Secret Management Integration

```yaml
# Vault Integration
# Location: /kubernetes/vault/vault-injector.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  template:
    metadata:
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/role: "app"
        vault.hashicorp.com/agent-inject-secret-db: "secret/data/app/database"
        vault.hashicorp.com/agent-inject-template-db: |
          {{- with secret "secret/data/app/database" -}}
          export DB_USER="{{ .Data.data.username }}"
          export DB_PASS="{{ .Data.data.password }}"
          {{- end }}
```

---

## Security Configurations

### Firewall Rules Template

```bash
# Location: /etc/pve/firewall/cluster.fw
[OPTIONS]
enable: 1
log_level_in: info
log_level_out: info
policy_in: DROP
policy_out: ACCEPT

[ALIASES]
management_network **********/24
production_network **********/24
staging_network **********/24

[IPSET management]
**********/24 # Management network

[IPSET production] 
**********/24 # Production network

[RULES]
# Management access
IN ACCEPT -source +management -p tcp -dport 8006 -log nolog # Proxmox UI
IN ACCEPT -source +management -p tcp -dport 22 -log nolog   # SSH

# Kubernetes API
IN ACCEPT -source +management -dest +production -p tcp -dport 6443 -log nolog
IN ACCEPT -source +management -dest +staging -p tcp -dport 6443 -log nolog

# Block inter-VLAN by default
IN DROP -source +production -dest +management -log nolog
IN DROP -source +staging -dest +production -log nolog
```

### SSL/TLS Configuration

```yaml
# Cert-manager ClusterIssuer
# Location: /kubernetes/cert-manager/cluster-issuer.yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

### RBAC Templates

```yaml
# Developer Role
# Location: /kubernetes/rbac/developer-role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: developer
rules:
- apiGroups: ["", "apps", "batch"]
  resources: ["pods", "services", "deployments", "jobs"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["pods/log", "pods/exec"]
  verbs: ["get", "list"]
```

---

## Quick Reference Card

### Most Used Commands

```bash
# VM Operations
qm list                          # List VMs
qm start/stop/restart <vmid>     # VM control
vzdump <vmid> --mode snapshot    # Backup VM

# Kubernetes
kubectl get pods -A              # All pods
kubectl logs -f <pod> -n <ns>    # Follow logs
kubectl exec -it <pod> -n <ns> -- /bin/bash

# Docker
docker ps -a                     # List containers
docker logs -f <container>       # Follow logs
docker system prune -a           # Clean up

# System
df -h                            # Disk usage
htop                             # Process monitor
journalctl -f -u <service>       # Service logs
```

### Important URLs

```yaml
Proxmox UI: https://*************:8006
GitLab: https://gitlab.alias.local
Harbor: https://registry.alias.local  
Vault: http://vault.alias.local:8200
Keycloak: https://keycloak.alias.local
Grafana: http://grafana.alias.local:3000
Kibana: http://kibana.alias.local:5601
```

### Emergency Contacts

```yaml
Infrastructure Team: <EMAIL>
Security Team: <EMAIL>
On-Call: +1-XXX-XXX-XXXX
Vendor Support:
  - Proxmox: <EMAIL>
  - GitLab: <EMAIL>
```

---

*This ontology serves as the single source of truth for all ALIAS infrastructure configurations, scripts, and procedures. Keep it updated as the infrastructure evolves.*