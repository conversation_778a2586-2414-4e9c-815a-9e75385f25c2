#!/bin/bash
# This is a shell script that creates the MCP Hub source files
# Run this on the Proxmox container after deployment

mkdir -p /opt/mcp-hub/src

# Create registry.ts
cat > /opt/mcp-hub/src/registry.ts << 'EOF'
import { Pool } from 'pg';
import Redis from 'redis';
import fs from 'fs/promises';
import path from 'path';

export interface MCPServer {
  id: string;
  name: string;
  description: string;
  version: string;
  type: 'docker' | 'native' | 'remote';
  transport: 'stdio' | 'http' | 'websocket';
  connection: {
    command?: string;
    args?: string[];
    dockerImage?: string;
    port?: number;
    url?: string;
    env?: Record<string, string>;
  };
  capabilities: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    sampling?: boolean;
  };
  metadata: {
    author?: string;
    homepage?: string;
    tags?: string[];
    icon?: string;
  };
  health: {
    status: 'online' | 'offline' | 'error';
    lastCheck: Date;
    uptime?: number;
  };
}

export class MCPRegistry {
  private servers: Map<string, MCPServer> = new Map();
  private db?: Pool;
  private redis?: Redis.RedisClientType;

  constructor() {
    this.loadServers();
  }

  async loadServers() {
    try {
      // Load from config file
      const configPath = path.join(process.cwd(), 'config', 'servers.json');
      const data = await fs.readFile(configPath, 'utf-8');
      const servers = JSON.parse(data) as MCPServer[];
      
      servers.forEach(server => {
        server.health = {
          status: 'offline',
          lastCheck: new Date()
        };
        this.servers.set(server.id, server);
      });
    } catch (error) {
      console.error('Failed to load servers:', error);
    }
  }

  async getServer(id: string): Promise<MCPServer | undefined> {
    return this.servers.get(id);
  }

  async listServers(): Promise<MCPServer[]> {
    return Array.from(this.servers.values());
  }

  async addServer(server: MCPServer): Promise<void> {
    this.servers.set(server.id, server);
    await this.saveServers();
  }

  async updateServer(id: string, updates: Partial<MCPServer>): Promise<void> {
    const server = this.servers.get(id);
    if (server) {
      Object.assign(server, updates);
      await this.saveServers();
    }
  }

  async removeServer(id: string): Promise<void> {
    this.servers.delete(id);
    await this.saveServers();
  }

  private async saveServers(): Promise<void> {
    const configPath = path.join(process.cwd(), 'config', 'servers.json');
    const servers = Array.from(this.servers.values());
    await fs.writeFile(configPath, JSON.stringify(servers, null, 2));
  }

  // Express router
  get router() {
    const { Router } = require('express');
    const router = Router();

    router.get('/servers', async (req, res) => {
      const servers = await this.listServers();
      res.json(servers);
    });

    router.get('/servers/:id', async (req, res) => {
      const server = await this.getServer(req.params.id);
      if (server) {
        res.json(server);
      } else {
        res.status(404).json({ error: 'Server not found' });
      }
    });

    router.post('/servers', async (req, res) => {
      try {
        await this.addServer(req.body);
        res.status(201).json({ success: true });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    router.put('/servers/:id', async (req, res) => {
      try {
        await this.updateServer(req.params.id, req.body);
        res.json({ success: true });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    router.delete('/servers/:id', async (req, res) => {
      await this.removeServer(req.params.id);
      res.json({ success: true });
    });

    return router;
  }
}
EOF

# Create proxy.ts
cat > /opt/mcp-hub/src/proxy.ts << 'EOF'
import { WebSocket } from 'ws';
import { spawn } from 'child_process';
import Docker from 'dockerode';
import axios from 'axios';
import { MCPRegistry, MCPServer } from './registry';

const docker = new Docker();

interface MCPConnection {
  send(message: any): Promise<any>;
  close(): void;
}

class StdioConnection implements MCPConnection {
  private process: any;
  private pending: Map<number, { resolve: Function; reject: Function }> = new Map();
  private messageId = 0;

  constructor(command: string, args: string[] = []) {
    this.process = spawn(command, args);
    
    this.process.stdout.on('data', (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString());
        if (message.id && this.pending.has(message.id)) {
          const { resolve } = this.pending.get(message.id)!;
          this.pending.delete(message.id);
          resolve(message);
        }
      } catch (error) {
        console.error('Failed to parse message:', error);
      }
    });
  }

  async send(message: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = ++this.messageId;
      message.id = id;
      
      this.pending.set(id, { resolve, reject });
      this.process.stdin.write(JSON.stringify(message) + '\n');
      
      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.pending.has(id)) {
          this.pending.delete(id);
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }

  close(): void {
    this.process.kill();
  }
}

class HttpConnection implements MCPConnection {
  constructor(private url: string) {}

  async send(message: any): Promise<any> {
    const response = await axios.post(this.url, message);
    return response.data;
  }

  close(): void {
    // No-op for HTTP connections
  }
}

export class MCPProxy {
  private connections: Map<string, MCPConnection> = new Map();

  constructor(private registry: MCPRegistry) {}

  async handleRequest(serverId: string, request: any): Promise<any> {
    const server = await this.registry.getServer(serverId);
    if (!server) {
      throw new Error('Server not found');
    }

    let connection = this.connections.get(serverId);
    if (!connection) {
      connection = await this.createConnection(server);
      this.connections.set(serverId, connection);
    }

    return connection.send(request);
  }

  async createConnection(server: MCPServer): Promise<MCPConnection> {
    switch (server.type) {
      case 'native':
        if (!server.connection.command) {
          throw new Error('Command not specified for native server');
        }
        return new StdioConnection(
          server.connection.command,
          server.connection.args || []
        );

      case 'docker':
        // Start docker container if needed
        const containers = await docker.listContainers({
          all: true,
          filters: { name: [`mcp-${server.id}`] }
        });
        
        if (containers.length === 0) {
          // Create and start container
          const container = await docker.createContainer({
            Image: server.connection.dockerImage,
            name: `mcp-${server.id}`,
            Env: Object.entries(server.connection.env || {}).map(
              ([k, v]) => `${k}=${v}`
            ),
            HostConfig: {
              NetworkMode: 'mcp-network'
            }
          });
          await container.start();
        }
        
        // Connect via HTTP to container
        return new HttpConnection(
          `http://mcp-${server.id}:${server.connection.port || 8000}`
        );

      case 'remote':
        if (!server.connection.url) {
          throw new Error('URL not specified for remote server');
        }
        return new HttpConnection(server.connection.url);

      default:
        throw new Error(`Unknown server type: ${server.type}`);
    }
  }

  handleWebSocket(ws: WebSocket, req: any): void {
    const serverId = req.url?.split('/')[2];
    if (!serverId) {
      ws.close(1002, 'Invalid server ID');
      return;
    }

    ws.on('message', async (data) => {
      try {
        const request = JSON.parse(data.toString());
        const response = await this.handleRequest(serverId, request);
        ws.send(JSON.stringify(response));
      } catch (error) {
        ws.send(JSON.stringify({
          error: error.message,
          code: -32603
        }));
      }
    });
  }

  // Express handler
  get handler() {
    return async (req: any, res: any) => {
      try {
        const { serverId } = req.params;
        const response = await this.handleRequest(serverId, req.body);
        res.json(response);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    };
  }
}
EOF

# Create routes.ts
cat > /opt/mcp-hub/src/routes.ts << 'EOF'
import { Express } from 'express';
import { MCPRegistry } from './registry';
import { MCPProxy } from './proxy';

export function setupRoutes(
  app: Express,
  registry: MCPRegistry,
  proxy: MCPProxy
): void {
  // API routes
  app.use('/api', registry.router);
  
  // MCP proxy routes
  app.post('/mcp/:serverId', proxy.handler);
  
  // Health check
  app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
  });
  
  // Server discovery endpoint
  app.get('/api/discover', async (req, res) => {
    try {
      // Auto-discover MCP servers
      const discovered = await discoverServers();
      res.json(discovered);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

async function discoverServers(): Promise<any[]> {
  const servers = [];
  
  // TODO: Implement auto-discovery
  // - Scan Docker containers
  // - Check local installations
  // - Query network services
  
  return servers;
}
EOF

echo "Source files created in /opt/mcp-hub/src/"
echo "Run 'npm install' and 'npm run build' to compile"