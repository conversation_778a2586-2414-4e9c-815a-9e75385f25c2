# MCP Hub Client Integration Guide

## Overview
This guide shows how to connect various MCP clients to your centralized MCP Hub.

## Client Configurations

### 1. <PERSON>op

Add to `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "mcp-hub": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/remote-client", "http://mcp-hub.local:3100/mcp"]
    }
  }
}
```

Or use individual servers through the hub:

```json
{
  "mcpServers": {
    "it-assistant": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/remote-client", "http://mcp-hub.local:3100/mcp/it-assistant"]
    },
    "browser-use": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/remote-client", "http://mcp-hub.local:3100/mcp/browser-use"]
    }
  }
}
```

### 2. <PERSON> Code CLI

```bash
# Add MCP Hub
claude mcp add mcp-hub "npx -y @modelcontextprotocol/remote-client http://mcp-hub.local:3100/mcp" -s user

# Or add specific servers
claude mcp add it-assistant "npx -y @modelcontextprotocol/remote-client http://mcp-hub.local:3100/mcp/it-assistant" -s user
```

### 3. Remote Access Script

Create `mcp-remote.sh`:

```bash
#!/bin/bash
# MCP Remote Access Script

MCP_HUB_URL="${MCP_HUB_URL:-http://mcp-hub.local:3100}"
MCP_TOKEN="${MCP_TOKEN:-your-auth-token}"

# Function to list available servers
list_servers() {
    curl -s "$MCP_HUB_URL/api/servers" | jq -r '.[] | "\(.id): \(.name) - \(.description)"'
}

# Function to connect to a server
connect_server() {
    local server_id="$1"
    npx -y @modelcontextprotocol/remote-client \
        --url "$MCP_HUB_URL/mcp/$server_id" \
        --token "$MCP_TOKEN"
}

# Main script
case "$1" in
    list)
        echo "Available MCP Servers:"
        list_servers
        ;;
    connect)
        if [ -z "$2" ]; then
            echo "Usage: $0 connect <server-id>"
            exit 1
        fi
        connect_server "$2"
        ;;
    *)
        echo "Usage: $0 {list|connect <server-id>}"
        echo ""
        echo "Examples:"
        echo "  $0 list                    # List available servers"
        echo "  $0 connect it-assistant    # Connect to IT Assistant"
        ;;
esac
```

### 4. Python Client

```python
#!/usr/bin/env python3
# mcp_hub_client.py

import asyncio
import json
import websockets
import requests

class MCPHubClient:
    def __init__(self, hub_url="http://mcp-hub.local:3100", token=None):
        self.hub_url = hub_url
        self.token = token
        self.ws_url = hub_url.replace("http", "ws")
    
    def list_servers(self):
        """List all available MCP servers"""
        response = requests.get(f"{self.hub_url}/api/servers")
        return response.json()
    
    async def connect_server(self, server_id):
        """Connect to a specific MCP server via WebSocket"""
        url = f"{self.ws_url}/mcp/{server_id}"
        
        async with websockets.connect(url) as websocket:
            # Send initialization
            await websocket.send(json.dumps({
                "jsonrpc": "2.0",
                "method": "initialize",
                "params": {
                    "clientInfo": {
                        "name": "Python MCP Client",
                        "version": "1.0.0"
                    }
                },
                "id": 1
            }))
            
            # Receive response
            response = await websocket.recv()
            print(f"Connected to {server_id}:", response)
            
            # Keep connection alive
            while True:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30)
                    print(f"Received: {message}")
                except asyncio.TimeoutError:
                    # Send ping to keep connection alive
                    await websocket.ping()

# Example usage
if __name__ == "__main__":
    client = MCPHubClient()
    
    # List servers
    servers = client.list_servers()
    for server in servers:
        print(f"{server['id']}: {server['name']}")
    
    # Connect to a server
    # asyncio.run(client.connect_server("it-assistant"))
```

### 5. JavaScript/TypeScript Client

```typescript
// mcp-hub-client.ts
import { WebSocket } from 'ws';
import axios from 'axios';

class MCPHubClient {
  private hubUrl: string;
  private token?: string;

  constructor(hubUrl = 'http://mcp-hub.local:3100', token?: string) {
    this.hubUrl = hubUrl;
    this.token = token;
  }

  async listServers() {
    const response = await axios.get(`${this.hubUrl}/api/servers`);
    return response.data;
  }

  async callTool(serverId: string, toolName: string, args: any) {
    const response = await axios.post(`${this.hubUrl}/mcp/${serverId}`, {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args
      },
      id: Date.now()
    });
    
    return response.data;
  }

  connectWebSocket(serverId: string): WebSocket {
    const wsUrl = this.hubUrl.replace('http', 'ws');
    const ws = new WebSocket(`${wsUrl}/mcp/${serverId}`);
    
    ws.on('open', () => {
      console.log(`Connected to ${serverId}`);
      
      // Send initialization
      ws.send(JSON.stringify({
        jsonrpc: '2.0',
        method: 'initialize',
        params: {
          clientInfo: {
            name: 'JS MCP Client',
            version: '1.0.0'
          }
        },
        id: 1
      }));
    });
    
    ws.on('message', (data) => {
      console.log('Received:', data.toString());
    });
    
    return ws;
  }
}

// Example usage
const client = new MCPHubClient();

// List servers
client.listServers().then(servers => {
  servers.forEach(server => {
    console.log(`${server.id}: ${server.name}`);
  });
});

// Call a tool
client.callTool('it-assistant', 'get_system_info', {})
  .then(result => console.log(result));
```

## Authentication

### API Key Authentication

```bash
# Set API key in environment
export MCP_HUB_API_KEY="your-api-key-here"

# Use in requests
curl -H "Authorization: Bearer $MCP_HUB_API_KEY" \
  http://mcp-hub.local:3100/api/servers
```

### JWT Token Authentication

```javascript
// Login to get token
const response = await fetch('http://mcp-hub.local:3100/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'your-username',
    password: 'your-password'
  })
});

const { token } = await response.json();

// Use token in requests
const servers = await fetch('http://mcp-hub.local:3100/api/servers', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Remote Access Over Internet

### 1. Using Tailscale (Recommended)

```bash
# On Proxmox server
tailscale up

# Get Tailscale IP
tailscale ip -4

# Access from anywhere
http://100.x.x.x:3100
```

### 2. Using Cloudflare Tunnel

```bash
# Install cloudflared
curl -L https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64 -o cloudflared
chmod +x cloudflared

# Create tunnel
./cloudflared tunnel create mcp-hub

# Configure tunnel
cat > config.yml << EOF
tunnel: YOUR_TUNNEL_ID
credentials-file: /root/.cloudflared/YOUR_TUNNEL_ID.json

ingress:
  - hostname: mcp-hub.yourdomain.com
    service: http://localhost:3100
  - service: http_status:404
EOF

# Run tunnel
./cloudflared tunnel run mcp-hub
```

### 3. Using Nginx Proxy Manager

Add to your existing Nginx Proxy Manager:
- Domain: mcp-hub.yourdomain.com
- Forward to: 192.168.1.X:3100
- Enable WebSocket support
- Add SSL certificate

## Troubleshooting

### Connection Issues

```bash
# Test connectivity
curl http://mcp-hub.local:3100/health

# Check specific server
curl http://mcp-hub.local:3100/api/servers/it-assistant

# Test MCP call
curl -X POST http://mcp-hub.local:3100/mcp/it-assistant \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/list",
    "id": 1
  }'
```

### Debug Mode

```bash
# Enable debug logging
export DEBUG=mcp:*
export MCP_HUB_DEBUG=true

# Run client with debug
DEBUG=* npx @modelcontextprotocol/remote-client \
  http://mcp-hub.local:3100/mcp/it-assistant
```

## Advanced Usage

### Load Balancing Multiple Instances

```nginx
upstream mcp-hub {
    server *************:3100;
    server *************:3100;
    server *************:3100;
}

server {
    listen 80;
    server_name mcp-hub.local;
    
    location / {
        proxy_pass http://mcp-hub;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Monitoring and Metrics

```bash
# Prometheus metrics endpoint
curl http://mcp-hub.local:3100/metrics

# Health checks for each server
curl http://mcp-hub.local:3100/api/servers/it-assistant/health
```

## Security Best Practices

1. **Use HTTPS in Production**
   ```bash
   # Generate SSL certificate
   certbot --nginx -d mcp-hub.yourdomain.com
   ```

2. **Implement Rate Limiting**
   ```nginx
   limit_req_zone $binary_remote_addr zone=mcp:10m rate=10r/s;
   
   location /mcp/ {
       limit_req zone=mcp burst=20;
       proxy_pass http://localhost:3101;
   }
   ```

3. **IP Whitelisting**
   ```nginx
   location /mcp/ {
       allow ***********/24;
       allow 10.0.0.0/8;
       deny all;
       proxy_pass http://localhost:3101;
   }
   ```

4. **API Key Rotation**
   ```bash
   # Rotate API keys monthly
   curl -X POST http://mcp-hub.local:3100/api/auth/rotate-key \
     -H "Authorization: Bearer $CURRENT_KEY"
   ```