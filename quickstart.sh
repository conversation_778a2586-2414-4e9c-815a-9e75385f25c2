#!/bin/bash

# Quick start script for IT Assistant MCP

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}IT Assistant MCP - Quick Start${NC}"
echo "=============================="
echo ""

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed${NC}"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if .env exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✓ Created .env file${NC}"
    echo ""
    echo -e "${YELLOW}Please edit .env and add your configuration:${NC}"
    echo "  - PROXMOX_HOST (your Proxmox server IP)"
    echo "  - PROXMOX_TOKEN (your API token)"
    echo "  - ELEVENLABS_API_KEY (optional, for voice alerts)"
    echo ""
    read -p "Press Enter after updating .env to continue..."
fi

# Create directories
echo -e "${YELLOW}Creating data directories...${NC}"
mkdir -p data/{reports,playbooks,templates}
mkdir -p custom/{playbooks,templates}
echo -e "${GREEN}✓ Directories created${NC}"

# Build image
echo ""
echo -e "${YELLOW}Building Docker image...${NC}"
docker build -f Dockerfile.production -t it-assistant-mcp:latest .
echo -e "${GREEN}✓ Image built successfully${NC}"

# Start with Docker Compose
echo ""
echo -e "${YELLOW}Starting IT Assistant...${NC}"
docker-compose up -d
echo -e "${GREEN}✓ IT Assistant started${NC}"

# Wait for health
echo ""
echo -e "${YELLOW}Waiting for service to be healthy...${NC}"
sleep 5

if docker-compose ps | grep -q "healthy"; then
    echo -e "${GREEN}✓ Service is healthy${NC}"
else
    echo -e "${RED}⚠ Service might not be healthy yet${NC}"
    echo "Check logs with: docker-compose logs -f"
fi

# Show how to add to Claude
echo ""
echo -e "${BLUE}Setup Complete!${NC}"
echo ""
echo "To add to Claude Code:"
echo -e "${GREEN}claude mcp add it-assistant \"docker run -i --rm --env-file $(pwd)/.env it-assistant-mcp:latest\" -s user${NC}"
echo ""
echo "Or for development:"
echo -e "${GREEN}claude mcp add it-assistant \"node $(pwd)/src/index.js\" -s user${NC}"
echo ""
echo "Useful commands:"
echo "  View logs:    docker-compose logs -f"
echo "  Stop:         docker-compose down"
echo "  Restart:      docker-compose restart"
echo "  Update:       docker-compose pull && docker-compose up -d"
echo ""
echo -e "${BLUE}Happy automating! 🚀${NC}"