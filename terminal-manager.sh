#!/bin/bash

# Terminal Manager - Launch and monitor external terminals

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to launch in new terminal tab
launch_in_tab() {
    local cmd="$1"
    local title="$2"
    
    osascript << EOF
tell application "Terminal"
    activate
    tell application "System Events" to keystroke "t" using command down
    delay 0.5
    do script "echo -e '${GREEN}$title${NC}' && echo '============================' && $cmd" in front window
end tell
EOF
}

# Function to launch in new terminal window
launch_in_window() {
    local cmd="$1"
    local title="$2"
    
    osascript << EOF
tell application "Terminal"
    activate
    do script "echo -e '${GREEN}$title${NC}' && echo '============================' && $cmd"
end tell
EOF
}

case "$1" in
    "monitor")
        echo "Launching service monitor..."
        launch_in_window "/Users/<USER>/it-assistant-mcp/monitor-services.command" "Service Monitor"
        ;;
        
    "logs")
        echo "Launching log monitors..."
        launch_in_window "ssh root@************* 'tail -f /var/log/pveproxy/access.log'" "Proxmox Access Log"
        sleep 1
        launch_in_tab "ssh root@************* 'journalctl -f -u pve-firewall'" "Firewall Log"
        ;;
        
    "containers")
        echo "Launching container shells..."
        launch_in_window "ssh root@************* 'pct enter 105'" "Dokploy Container"
        sleep 1
        launch_in_tab "ssh root@************* 'pct enter 106'" "Browser-MCP Container"
        ;;
        
    "test-all")
        echo "Testing all services in parallel terminals..."
        
        # Test each service in its own tab
        launch_in_window "while true; do curl -k https://*************:8006 >/dev/null 2>&1 && echo '[$(date +%H:%M:%S)] ✅ Proxmox OK' || echo '[$(date +%H:%M:%S)] ❌ Proxmox DOWN'; sleep 5; done" "Proxmox Monitor"
        
        sleep 0.5
        launch_in_tab "while true; do curl http://192.168.1.22:3000 >/dev/null 2>&1 && echo '[$(date +%H:%M:%S)] ✅ Dokploy OK' || echo '[$(date +%H:%M:%S)] ❌ Dokploy DOWN'; sleep 5; done" "Dokploy Monitor"
        
        sleep 0.5
        launch_in_tab "while true; do curl http://************:8000 >/dev/null 2>&1 && echo '[$(date +%H:%M:%S)] ✅ Browser-MCP OK' || echo '[$(date +%H:%M:%S)] ❌ Browser-MCP DOWN'; sleep 5; done" "Browser-MCP Monitor"
        ;;
        
    "dashboard")
        echo "Creating terminal dashboard..."
        
        # Create a dashboard layout with multiple panes
        osascript << 'EOF'
tell application "Terminal"
    activate
    
    -- Create main window
    do script "watch -n 5 'echo \"=== PROXMOX DASHBOARD ===\"; echo \"\"; ssh root@************* \"pct list && echo && qm list\"'"
    set mainWindow to front window
    
    -- Wait and create new window for services
    delay 1
    do script "watch -n 10 '/Users/<USER>/it-assistant-mcp/check-services.sh'"
    
    -- Create another window for network status
    delay 1
    do script "watch -n 5 'echo \"=== NETWORK STATUS ===\"; for i in 100 17 22 23 29; do printf \"192.168.1.$i: \"; ping -c 1 -W 1 192.168.1.$i >/dev/null 2>&1 && echo \"UP\" || echo \"DOWN\"; done'"
end tell
EOF
        ;;
        
    *)
        echo "Terminal Manager"
        echo "==============="
        echo ""
        echo "Usage: $0 {monitor|logs|containers|test-all|dashboard}"
        echo ""
        echo "Commands:"
        echo "  monitor    - Launch service monitoring dashboard"
        echo "  logs       - Open log monitoring terminals"
        echo "  containers - Open shells to containers"
        echo "  test-all   - Test all services in parallel"
        echo "  dashboard  - Create multi-window dashboard"
        ;;
esac