# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Data and logs
data/
logs/
*.db
*.db-journal
*.log

# Environment and config
.env
.env.local
.env.*.local
config.env
!.env.example

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Build outputs
build/
dist/
*.pid

# Test coverage
coverage/
.nyc_output/

# Container runtime
.dockerignore
container.lock

# Temporary files
tmp/
temp/
*.tmp
*.temp

# SSH keys (never commit these!)
*.pem
*.key
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*

# OS specific
Thumbs.db
Desktop.ini