# Checkmate Customization Guide

## Development Environment Overview

The development setup provides a complete environment for customizing Checkmate with:
- **Hot-reload** for both frontend and backend
- **Docker-based** development for consistency
- **Pre-configured integrations** for your infrastructure
- **Custom theme** matching ALIAS branding
- **Plugin architecture** for extending functionality

## Quick Start

```bash
# Run the setup script
cd /Users/<USER>/it-assistant-mcp
./checkmate-dev-setup.sh

# Navigate to project
cd ~/Projects/checkmate-custom

# Start development environment
./dev.sh start
```

## Customization Areas

### 1. UI/UX Customization

#### Custom Theme
The ALIAS theme is pre-created at `customizations/themes/alias-theme.css`:
```css
/* Apply in Client/src/index.css */
@import '../customizations/themes/alias-theme.css';
```

#### Dashboard Layouts
Create custom dashboard layouts in `Client/src/components/dashboards/`:
```jsx
// AliasInfrastructureDashboard.jsx
import React from 'react';
import { Grid, Card, Typography } from '@mui/material';
import ProxmoxStatus from './widgets/ProxmoxStatus';
import ContainerGrid from './widgets/ContainerGrid';
import StorageMetrics from './widgets/StorageMetrics';

export default function AliasInfrastructureDashboard() {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h4">ALIAS Infrastructure</Typography>
      </Grid>
      <Grid item xs={12} md={8}>
        <ProxmoxStatus />
      </Grid>
      <Grid item xs={12} md={4}>
        <StorageMetrics />
      </Grid>
      <Grid item xs={12}>
        <ContainerGrid />
      </Grid>
    </Grid>
  );
}
```

#### Custom Status Page
Create branded status page in `Client/src/pages/StatusPage/`:
```jsx
// Custom status page with ALIAS branding
const AliasStatusPage = () => {
  return (
    <div className="alias-status-page">
      <header className="gradient-header">
        <h1>ALIAS Infrastructure Status</h1>
        <p>Real-time status of all systems</p>
      </header>
      <StatusGrid />
      <IncidentTimeline />
    </div>
  );
};
```

### 2. Backend Customizations

#### Proxmox Integration
Add Proxmox monitoring in `Server/integrations/proxmox.js`:
```javascript
const ProxmoxClient = require('proxmoxer');

class ProxmoxMonitor {
  constructor() {
    this.client = new ProxmoxClient({
      host: process.env.PROXMOX_HOST,
      tokenID: process.env.PROXMOX_TOKEN_ID,
      tokenSecret: process.env.PROXMOX_TOKEN_SECRET
    });
  }

  async checkVMStatus(vmid) {
    const status = await this.client.nodes('pve').qemu(vmid).status.get();
    return {
      online: status.status === 'running',
      cpu: status.cpu,
      memory: status.mem / status.maxmem * 100,
      uptime: status.uptime
    };
  }

  async autoDiscoverVMs() {
    const vms = await this.client.nodes('pve').qemu.get();
    return vms.map(vm => ({
      name: vm.name,
      id: vm.vmid,
      type: 'proxmox-vm',
      target: `pve/${vm.vmid}`
    }));
  }
}
```

#### Voice Alerts Integration
Connect to IT Assistant MCP in `Server/integrations/voice-alerts.js`:
```javascript
class ITAssistantIntegration {
  async sendVoiceAlert(monitor, incident) {
    const severity = this.calculateSeverity(monitor, incident);
    
    await fetch('http://localhost:3001/tools/send_alert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: `${monitor.name} is down. ${incident.error}`,
        severity: severity,
        voice: 'Lily' // British accent as configured
      })
    });
  }

  calculateSeverity(monitor, incident) {
    if (monitor.tags.includes('critical')) return 'critical';
    if (incident.duration > 300) return 'warning';
    return 'info';
  }
}
```

#### Custom Monitor Types
Add infrastructure-specific monitors in `Server/monitors/`:
```javascript
// Docker container monitor
class DockerMonitor extends BaseMonitor {
  async check() {
    const container = await docker.getContainer(this.target);
    const stats = await container.stats({ stream: false });
    
    return {
      status: container.State.Running ? 'up' : 'down',
      responseTime: 1,
      metrics: {
        cpu: this.calculateCPUPercent(stats),
        memory: this.calculateMemoryPercent(stats),
        network: stats.networks
      }
    };
  }
}

// LXC container monitor
class LXCMonitor extends BaseMonitor {
  async check() {
    const result = await ssh.exec(`pct status ${this.target}`);
    return {
      status: result.includes('running') ? 'up' : 'down',
      responseTime: result.executionTime
    };
  }
}
```

### 3. Feature Additions

#### Infrastructure Auto-Discovery
Create `Server/features/auto-discovery.js`:
```javascript
class InfrastructureDiscovery {
  async discoverAll() {
    const discoveries = await Promise.all([
      this.discoverProxmoxVMs(),
      this.discoverDockerContainers(),
      this.discoverNetworkServices()
    ]);
    
    return discoveries.flat();
  }

  async discoverProxmoxVMs() {
    // Auto-discover all VMs and containers
    const nodes = await proxmox.getNodes();
    const monitors = [];
    
    for (const node of nodes) {
      const vms = await proxmox.getNodeVMs(node.node);
      monitors.push(...vms.map(vm => ({
        name: `${vm.name} (${node.node})`,
        type: 'proxmox-vm',
        target: `${node.node}/${vm.vmid}`,
        interval: 60,
        tags: ['auto-discovered', 'infrastructure']
      })));
    }
    
    return monitors;
  }
}
```

#### Automated Remediation
Add self-healing capabilities in `Server/features/remediation.js`:
```javascript
class AutoRemediation {
  constructor() {
    this.strategies = new Map();
    this.setupStrategies();
  }

  setupStrategies() {
    // Restart stopped containers
    this.strategies.set('container-down', async (monitor) => {
      if (monitor.metadata.autoRestart) {
        await this.restartContainer(monitor.target);
        return { action: 'restarted', success: true };
      }
    });

    // Clear disk space
    this.strategies.set('disk-full', async (monitor) => {
      if (monitor.metrics.disk > 90) {
        await this.cleanupDisk(monitor.target);
        return { action: 'cleaned', success: true };
      }
    });
  }

  async remediate(monitor, incident) {
    const strategy = this.strategies.get(incident.type);
    if (strategy) {
      const result = await strategy(monitor, incident);
      await this.logRemediation(monitor, incident, result);
      return result;
    }
  }
}
```

#### Advanced Analytics
Add analytics in `Server/features/analytics.js`:
```javascript
class PerformanceAnalytics {
  async analyzePatterns(monitorId, timeRange) {
    const data = await this.getHistoricalData(monitorId, timeRange);
    
    return {
      patterns: this.detectPatterns(data),
      anomalies: this.detectAnomalies(data),
      predictions: this.predictFailures(data),
      recommendations: this.generateRecommendations(data)
    };
  }

  detectAnomalies(data) {
    // Use statistical analysis to find anomalies
    const mean = this.calculateMean(data);
    const stdDev = this.calculateStdDev(data);
    
    return data.filter(point => 
      Math.abs(point.value - mean) > 2 * stdDev
    );
  }

  predictFailures(data) {
    // Simple linear regression for trend prediction
    const trend = this.calculateTrend(data);
    const prediction = this.extrapolateTrend(trend, 24); // 24 hours
    
    return {
      likelihood: prediction.failureProbability,
      estimatedTime: prediction.timeToFailure,
      confidence: prediction.confidence
    };
  }
}
```

### 4. API Extensions

#### Custom REST Endpoints
Add to `Server/routes/custom.js`:
```javascript
// Infrastructure overview endpoint
router.get('/api/infrastructure/overview', async (req, res) => {
  const overview = {
    proxmox: await getProxmoxSummary(),
    containers: await getContainersSummary(),
    storage: await getStorageSummary(),
    network: await getNetworkSummary(),
    health: calculateOverallHealth()
  };
  
  res.json(overview);
});

// Bulk operations endpoint
router.post('/api/monitors/bulk', async (req, res) => {
  const { action, monitorIds } = req.body;
  
  const results = await Promise.allSettled(
    monitorIds.map(id => performAction(action, id))
  );
  
  res.json({ results });
});
```

#### GraphQL API
Add GraphQL support in `Server/graphql/schema.js`:
```graphql
type Query {
  infrastructure: InfrastructureStatus!
  monitorsByTag(tag: String!): [Monitor!]!
  incidentAnalysis(id: ID!): IncidentAnalysis!
  performanceTrends(timeRange: TimeRange!): PerformanceTrends!
}

type Mutation {
  createMonitorFromTemplate(template: MonitorTemplate!): Monitor!
  triggerRemediation(monitorId: ID!): RemediationResult!
  scheduleMaintenanceWindow(input: MaintenanceInput!): Maintenance!
}

type Subscription {
  monitorUpdates(filter: MonitorFilter): MonitorUpdate!
  alertStream(severity: Severity): Alert!
}
```

### 5. Deployment Customizations

#### Custom Docker Image
Create `Dockerfile.custom`:
```dockerfile
# Multi-stage build for custom Checkmate
FROM node:20-alpine AS frontend-build
WORKDIR /app/client
COPY Client/package*.json ./
RUN npm ci
COPY Client/ ./
COPY customizations/themes/ ./src/themes/
RUN npm run build

FROM node:20-alpine AS backend-build
WORKDIR /app/server
COPY Server/package*.json ./
RUN npm ci
COPY Server/ ./
COPY customizations/ ./customizations/

FROM node:20-alpine
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache curl

# Copy built applications
COPY --from=backend-build /app/server ./
COPY --from=frontend-build /app/client/build ./public

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000
CMD ["node", "index.js"]
```

#### Kubernetes Deployment
Create `k8s/deployment.yaml`:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: checkmate-custom
  namespace: monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: checkmate
  template:
    metadata:
      labels:
        app: checkmate
    spec:
      containers:
      - name: checkmate
        image: your-registry/checkmate-custom:latest
        ports:
        - containerPort: 3000
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: checkmate-secrets
              key: mongodb-uri
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## Testing Your Customizations

### Unit Tests
Add tests for custom features:
```javascript
// Server/tests/proxmox-monitor.test.js
describe('ProxmoxMonitor', () => {
  it('should detect VM status correctly', async () => {
    const monitor = new ProxmoxMonitor();
    const status = await monitor.checkVMStatus('102');
    
    expect(status).toHaveProperty('online');
    expect(status.online).toBe(true);
  });
});
```

### Integration Tests
Test full workflows:
```javascript
// tests/integration/auto-discovery.test.js
describe('Auto Discovery', () => {
  it('should discover all infrastructure components', async () => {
    const discovery = new InfrastructureDiscovery();
    const monitors = await discovery.discoverAll();
    
    expect(monitors).toContain(
      expect.objectContaining({
        type: 'proxmox-vm',
        tags: expect.arrayContaining(['auto-discovered'])
      })
    );
  });
});
```

## Debugging Tips

1. **Frontend Debugging**:
   - Use React DevTools
   - Check browser console for errors
   - Use Redux DevTools if using Redux

2. **Backend Debugging**:
   - Use `DEBUG=checkmate:*` environment variable
   - Attach debugger: `node --inspect index.js`
   - Check MongoDB queries with Compass

3. **Docker Debugging**:
   ```bash
   # View logs
   ./dev.sh logs backend
   
   # Shell into container
   ./dev.sh shell backend
   
   # Check network
   docker network inspect checkmate-dev
   ```

## Performance Optimization

1. **Database Indexes**:
   ```javascript
   // Add indexes for better query performance
   db.monitors.createIndex({ tags: 1, status: 1 });
   db.incidents.createIndex({ monitorId: 1, createdAt: -1 });
   ```

2. **Caching Strategy**:
   ```javascript
   // Redis caching for expensive operations
   const cachedData = await redis.get(`monitor:${id}:status`);
   if (cachedData) return JSON.parse(cachedData);
   ```

3. **Query Optimization**:
   ```javascript
   // Use aggregation pipeline for complex queries
   const stats = await Monitor.aggregate([
     { $match: { tags: 'critical' } },
     { $group: { _id: '$status', count: { $sum: 1 } } }
   ]);
   ```

## Security Hardening

1. **API Authentication**:
   ```javascript
   // Add API key validation
   app.use('/api', (req, res, next) => {
     const apiKey = req.headers['x-api-key'];
     if (!apiKey || !isValidApiKey(apiKey)) {
       return res.status(401).json({ error: 'Invalid API key' });
     }
     next();
   });
   ```

2. **Rate Limiting**:
   ```javascript
   const rateLimit = require('express-rate-limit');
   app.use('/api', rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100 // limit each IP to 100 requests per windowMs
   }));
   ```

## Next Steps

1. Run the development setup script
2. Explore the codebase structure
3. Apply the ALIAS theme
4. Add Proxmox integration
5. Test with your infrastructure
6. Deploy custom version