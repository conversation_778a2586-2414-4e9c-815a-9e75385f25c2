# IT Assistant MCP Docker Deployment Guide

## Overview

The IT Assistant MCP server is containerized for easy deployment across different environments. This guide covers building, publishing, and deploying the Docker image.

## Building the Docker Image

### Local Build

```bash
# Build production image
./scripts/build.sh

# Build development image
./scripts/build.sh dev

# Build both
./scripts/build.sh all

# Build multi-architecture image
./scripts/build.sh multi
```

### Manual Build

```bash
# Production build
docker build -f Dockerfile.production -t it-assistant-mcp:latest .

# Development build
docker build -f Dockerfile -t it-assistant-mcp:dev .
```

## Registry Options

### 1. Docker Hub

```bash
# Tag for Docker Hub
docker tag it-assistant-mcp:latest yourusername/it-assistant-mcp:latest

# Push to Docker Hub
docker push yourusername/it-assistant-mcp:latest
```

### 2. GitHub Container Registry

```bash
# Login to GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin

# Tag for GHCR
docker tag it-assistant-mcp:latest ghcr.io/yourusername/it-assistant-mcp:latest

# Push to GHCR
docker push ghcr.io/yourusername/it-assistant-mcp:latest
```

### 3. Private Registry

```bash
# Tag for private registry
docker tag it-assistant-mcp:latest registry.company.com/it-assistant-mcp:latest

# Push to private registry
docker push registry.company.com/it-assistant-mcp:latest
```

## Deployment Methods

### 1. Docker Compose (Development/Small Scale)

```bash
# Deploy with Docker Compose
./scripts/deploy.sh compose

# Or manually
docker-compose up -d

# View logs
docker-compose logs -f it-assistant
```

### 2. Kubernetes (Production)

```bash
# Deploy to Kubernetes
./scripts/deploy.sh kubernetes

# Or manually
kubectl apply -f deploy/kubernetes/

# Check deployment
kubectl get pods -n it-operations
kubectl logs -f deployment/it-assistant-mcp -n it-operations
```

### 3. Docker Swarm (Medium Scale)

```bash
# Initialize Swarm (if needed)
docker swarm init

# Deploy stack
./scripts/deploy.sh swarm

# Or manually
docker stack deploy -c deploy/docker-swarm/stack.yml it-assistant

# Check services
docker service ls
docker service logs -f it-assistant_it-assistant
```

### 4. Systemd (Single Server)

```bash
# Deploy as systemd service
./scripts/deploy.sh systemd

# Check status
sudo systemctl status it-assistant-mcp

# View logs
sudo journalctl -u it-assistant-mcp -f
```

## Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Required
PROXMOX_HOST=*************
PROXMOX_TOKEN=your-token-here

# Optional
ELEVENLABS_API_KEY=your-key-here
MONITOR_INTERVAL=60
ALERT_THRESHOLD_CPU=80
```

### Docker Secrets (Swarm/Kubernetes)

```bash
# Create Docker secrets
echo -n "your-token" | docker secret create proxmox_token -
echo -n "your-key" | docker secret create elevenlabs_key -

# Or for Kubernetes
kubectl create secret generic it-assistant-secrets \
  --from-literal=proxmox-token=your-token \
  --from-literal=elevenlabs-api-key=your-key \
  -n it-operations
```

### Volumes

The container uses several volumes:

- `/app/reports` - Generated reports
- `/app/playbooks` - Automation playbooks
- `/app/templates` - Report templates
- `/home/<USER>/.ssh` - SSH keys (read-only)

## Running the Container

### Basic Run

```bash
docker run -it --rm \
  -e PROXMOX_HOST=************* \
  -e PROXMOX_TOKEN=your-token \
  it-assistant-mcp:latest
```

### With Volumes

```bash
docker run -it --rm \
  -v $(pwd)/data/reports:/app/reports \
  -v ~/.ssh:/home/<USER>/.ssh:ro \
  --env-file .env \
  it-assistant-mcp:latest
```

### As MCP Server

```bash
# For Claude Code integration
docker run -it --rm \
  --name it-assistant \
  -v $(pwd)/data:/app/data \
  --env-file .env \
  it-assistant-mcp:latest
```

## Integration with Claude Code

### Using Docker Container as MCP Server

```bash
# Add Docker-based MCP server
claude mcp add it-assistant-docker \
  "docker run -i --rm --env-file /path/to/.env it-assistant-mcp:latest" \
  -s user
```

### Using Remote Deployment

If deployed on a remote server, use SSH tunneling:

```bash
# Create SSH tunnel
ssh -L 3000:localhost:3000 user@remote-server

# Add MCP server pointing to tunnel
claude mcp add it-assistant-remote \
  "nc localhost 3000" \
  -s user
```

## Production Considerations

### 1. Security

- Always use secrets management (not plain env vars)
- Run as non-root user (already configured)
- Use read-only root filesystem where possible
- Implement network policies in Kubernetes

### 2. High Availability

```yaml
# Kubernetes: Set replicas
spec:
  replicas: 3
  
# Swarm: Update service
docker service update --replicas 3 it-assistant_it-assistant
```

### 3. Resource Limits

Already configured in deployment files:
- Memory: 256Mi (request) / 512Mi (limit)
- CPU: 250m (request) / 1000m (limit)

### 4. Monitoring

```bash
# Prometheus metrics (if enabled)
curl http://localhost:9090/metrics

# Health check
docker exec it-assistant node -e "console.log('healthy')"
```

### 5. Backup

```bash
# Backup volumes
docker run --rm \
  -v it-assistant_reports:/data \
  -v $(pwd)/backup:/backup \
  alpine tar czf /backup/reports-$(date +%Y%m%d).tar.gz -C /data .
```

## Troubleshooting

### Container Won't Start

```bash
# Check logs
docker logs it-assistant

# Debug mode
docker run -it --rm \
  --entrypoint /bin/sh \
  it-assistant-mcp:latest
```

### Permission Issues

```bash
# Fix volume permissions
docker exec it-assistant chown -R nodejs:nodejs /app/reports
```

### Network Issues

```bash
# Test connectivity
docker exec it-assistant ping -c 4 *************
docker exec it-assistant curl -k https://*************:8006
```

## CI/CD Integration

The repository includes GitHub Actions workflow for automated builds:

1. Push to `main` → Build and push `latest` tag
2. Push tag `v1.0.0` → Build and push version tags
3. Pull request → Build only (no push)

## Updates and Rollbacks

### Updating

```bash
# Pull latest image
docker pull it-assistant-mcp:latest

# Recreate container
docker-compose up -d --force-recreate

# Or for Kubernetes
kubectl set image deployment/it-assistant-mcp \
  it-assistant=it-assistant-mcp:v1.1.0 \
  -n it-operations
```

### Rollback

```bash
# Docker Compose
docker-compose down
docker run it-assistant-mcp:previous-version

# Kubernetes
kubectl rollout undo deployment/it-assistant-mcp -n it-operations

# Swarm
docker service update --rollback it-assistant_it-assistant
```

## Best Practices

1. **Always use specific version tags in production**
   ```yaml
   image: it-assistant-mcp:v1.0.0  # Good
   image: it-assistant-mcp:latest   # Bad for production
   ```

2. **Use health checks**
   Already configured in all deployment files

3. **Implement proper logging**
   Configure log drivers and centralized logging

4. **Regular updates**
   Schedule regular updates for security patches

5. **Test deployments**
   Always test in staging before production