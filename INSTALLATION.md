# IT Assistant MCP Installation Guide

## Prerequisites

1. Node.js 18+ installed
2. <PERSON> installed
3. Access to Proxmox server (optional)
4. ElevenLabs API key (optional for voice features)

## Installation Steps

### 1. <PERSON><PERSON> or <PERSON><PERSON> the IT Assistant

```bash
# Navigate to your preferred directory
cd /Users/<USER>/it-assistant-mcp

# Install dependencies
npm install
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your settings
nano .env
```

Required configurations:
- `PROXMOX_HOST`: Your Proxmox server IP (e.g., *************)
- `PROXMOX_TOKEN`: Your Proxmox API token

Optional configurations:
- `ELEVENLABS_API_KEY`: For voice alerts
- SMTP settings: For email notifications

### 3. Test the Server

```bash
# Run the test script
node test-server.js

# Or use the MCP inspector
npm run inspector
```

### 4. Add to <PERSON>

```bash
# Add as a user-scoped MCP server (available in all projects)
claude mcp add it-assistant "node /Users/<USER>/it-assistant-mcp/src/index.js" -s user

# Or add with environment variables
claude mcp add it-assistant "node /Users/<USER>/it-assistant-mcp/src/index.js" -s user \
  -e PROXMOX_HOST=************* \
  -e PROXMOX_TOKEN="root@pam!claude-mcp=c3e06607-8eac-48c2-9f07-eff795f94e02"
```

### 5. Restart Claude Code

After adding the MCP server, restart Claude Code to load the new server.

### 6. Verify Installation

In Claude Code, type:
```
List all available MCP tools
```

You should see the IT Assistant tools like:
- check_system_health
- list_all_vms
- send_voice_alert
- generate_report

## Using with Existing Proxmox Setup

Since you already have the `proxmox-production` MCP server configured, the IT Assistant will work alongside it to provide additional automation and monitoring capabilities.

### Example Integration

```javascript
// Use IT Assistant for high-level operations
await check_system_health({
  include_vms: true,
  nodes: ['pve']
})

// Use proxmox-production for direct API calls
await proxmox_api({
  method: 'GET',
  path: '/nodes/pve/status'
})
```

## Troubleshooting

### Server won't start
1. Check Node.js version: `node --version` (should be 18+)
2. Check dependencies: `npm install`
3. Check error logs: `npm start`

### Can't connect to Proxmox
1. Verify PROXMOX_HOST is correct
2. Check Proxmox API token is valid
3. Ensure port 8006 is accessible

### Tools not appearing in Claude
1. Restart Claude Code
2. Check MCP server list: `claude mcp list`
3. Remove and re-add if needed: `claude mcp remove it-assistant -s user`

## Next Steps

1. Configure automated tasks:
   ```javascript
   await schedule_task({
     name: "Daily Health Check",
     type: "custom",
     schedule: "0 9 * * *",
     script: "check_system_health"
   })
   ```

2. Set up voice alerts:
   ```javascript
   await send_voice_alert({
     severity: "info",
     message: "IT Assistant successfully configured"
   })
   ```

3. Generate your first report:
   ```javascript
   await generate_report({
     type: "daily",
     format: "markdown"
   })
   ```

## Support

For issues or questions:
1. Check the logs in Claude Code
2. Review the examples in `/examples`
3. Check the test output: `node test-server.js`