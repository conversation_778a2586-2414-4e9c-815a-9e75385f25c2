# Multi-stage build for production
# Stage 1: Build stage
FROM node:20-alpine AS builder

# Install build dependencies
RUN apk add --no-cache python3 make g++

WORKDIR /build

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm ci

# Copy source code
COPY src/ ./src/
COPY README.md ./

# Run any build steps (if needed)
# RUN npm run build

# Stage 2: Production stage
FROM node:20-alpine

# Install runtime dependencies only
RUN apk add --no-cache \
    curl \
    openssh-client \
    ca-certificates \
    tzdata \
    tini

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /build/src ./src
COPY --from=builder /build/README.md ./

# Create necessary directories
RUN mkdir -p /app/reports /app/playbooks /app/templates /app/logs

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Change ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Use tini as entrypoint for proper signal handling
ENTRYPOINT ["/sbin/tini", "--"]

# Start the MCP server
CMD ["node", "src/index.js"]

# Labels
LABEL org.opencontainers.image.title="IT Assistant MCP Server" \
      org.opencontainers.image.description="Comprehensive IT infrastructure management MCP server" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.authors="IT Assistant Team" \
      org.opencontainers.image.source="https://github.com/yourusername/it-assistant-mcp"