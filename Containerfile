# IT Assistant MCP Server Container
# Built for Apple's containerization framework

FROM alpine:3.20

# Install Node.js and system dependencies
RUN apk add --no-cache \
    nodejs \
    npm \
    sqlite \
    openssh-client \
    iputils \
    bash \
    git \
    python3 \
    make \
    g++ \
    && npm install -g npm@latest

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application source
COPY src/ ./src/

# Create data directory
RUN mkdir -p /app/data /app/logs

# Make the main script executable
RUN chmod +x /app/src/index.js

# Environment variables
ENV NODE_ENV=production
ENV IT_ASSISTANT_DB_PATH=/app/data/systems.db

# Create a non-root user
RUN addgroup -g 1001 -S itassistant && \
    adduser -S -u 1001 -G itassistant itassistant && \
    chown -R itassistant:itassistant /app

# Switch to non-root user
USER itassistant

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD echo '{"jsonrpc":"2.0","method":"tools/list","id":1}' | node /app/src/index.js | grep -q "list_systems" || exit 1

# Labels
LABEL org.opencontainers.image.title="IT Assistant MCP Server"
LABEL org.opencontainers.image.description="MCP server for IT infrastructure management"
LABEL org.opencontainers.image.version="1.0.0"
LABEL com.apple.container.framework="true"

# Default command
CMD ["node", "/app/src/index.js"]