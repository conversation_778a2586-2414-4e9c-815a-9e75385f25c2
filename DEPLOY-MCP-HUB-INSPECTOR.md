# Deploy MCP Hub with Inspector Support - Quick Guide

## Prerequisites
- Proxmox server at *************
- Scripts created:
  - `deploy-mcp-hub.sh` - Main deployment script
  - `convert-mcp-to-inspector.sh` - Single server converter
  - `batch-convert-mcp-servers.sh` - Batch converter
  - `mcp-servers-config.json` - Server configuration

## Step 1: Prepare the Scripts

```bash
# Make scripts executable
chmod +x convert-mcp-to-inspector.sh
chmod +x batch-convert-mcp-servers.sh

# Copy scripts to Proxmox server
scp convert-mcp-to-inspector.sh root@*************:/tmp/
scp batch-convert-mcp-servers.sh root@*************:/tmp/
scp mcp-servers-config.json root@*************:/tmp/
scp deploy-mcp-hub.sh root@*************:/tmp/
scp mcp-hub-with-inspector.md root@*************:/tmp/
```

## Step 2: Deploy MCP Hub Base

```bash
# SSH to Proxmox
ssh root@*************

# Run deployment script
cd /tmp
chmod +x deploy-mcp-hub.sh
./deploy-mcp-hub.sh
```

This will:
- Create LXC container 109
- Install Docker, Node.js, and dependencies
- Set up MCP Hub application
- Configure web UI and API

## Step 3: Add Inspector Support to MCP Hub

```bash
# Enter the MCP Hub container
pct enter 109

# Update the MCP Hub to support Inspector
cd /opt/mcp-hub

# Update Docker Compose with Inspector support
cat > docker-compose-inspector.yml << 'EOF'
version: "3.8"

services:
  mcp-hub:
    build: .
    container_name: mcp-hub-server
    restart: unless-stopped
    ports:
      - "3100:3100"  # Web Catalog
      - "3101:3101"  # MCP Proxy
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/mcphub
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=change-this-secret-key
      - INSPECTOR_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
      - redis
    networks:
      - mcp-network

  # IT Assistant MCP with Inspector
  it-assistant:
    build:
      context: ./servers/it-assistant
      dockerfile: Dockerfile.inspector
    container_name: mcp-it-assistant
    restart: unless-stopped
    ports:
      - "8000:8000"  # MCP Server
      - "8001:8001"  # Inspector UI
    environment:
      - MCP_MODE=server
      - INSPECTOR_PORT=8001
      - INSPECTOR_ENABLED=true
      - PROXMOX_HOST=*************
    volumes:
      - ./servers/it-assistant/config:/app/config
    networks:
      - mcp-network

  # Browser Use MCP with Inspector
  browser-use:
    build:
      context: ./servers/browser-use
      dockerfile: Dockerfile.inspector
    container_name: mcp-browser-use
    restart: unless-stopped
    ports:
      - "8100:8100"  # MCP Server
      - "8101:8101"  # Inspector UI
    environment:
      - MCP_MODE=server
      - INSPECTOR_PORT=8101
      - INSPECTOR_ENABLED=true
      - DISPLAY=:99
    volumes:
      - ./servers/browser-use/config:/app/config
    networks:
      - mcp-network

  postgres:
    image: postgres:15
    container_name: mcp-hub-postgres
    environment:
      - POSTGRES_DB=mcphub
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mcp-network

  redis:
    image: redis:7-alpine
    container_name: mcp-hub-redis
    volumes:
      - redis-data:/data
    networks:
      - mcp-network

volumes:
  postgres-data:
  redis-data:

networks:
  mcp-network:
    driver: bridge
EOF

# Create the base Dockerfile.inspector template
mkdir -p servers
cat > servers/Dockerfile.inspector << 'EOF'
# Base Dockerfile for MCP servers with Inspector
FROM node:20-alpine AS builder

WORKDIR /app

# Install MCP SDK and Inspector
RUN npm install -g @modelcontextprotocol/sdk @modelcontextprotocol/inspector

# Copy server files
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:20-alpine

WORKDIR /app

# Install runtime dependencies
RUN npm install -g @modelcontextprotocol/sdk @modelcontextprotocol/inspector
COPY package*.json ./
RUN npm ci --only=production

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public 2>/dev/null || true

# Create Inspector wrapper script
RUN cat > /app/start-with-inspector.sh << 'SCRIPT'
#!/bin/sh

# Start MCP Inspector in background
if [ "$INSPECTOR_ENABLED" = "true" ]; then
  echo "Starting MCP Inspector on port ${INSPECTOR_PORT:-8001}..."
  npx @modelcontextprotocol/inspector \
    --port ${INSPECTOR_PORT:-8001} \
    --server "node dist/index.js" &
  INSPECTOR_PID=$!
fi

# Start the MCP server
echo "Starting MCP Server..."
node dist/index.js &
SERVER_PID=$!

# Handle shutdown
trap "kill $SERVER_PID $INSPECTOR_PID" SIGTERM SIGINT

# Wait for processes
wait $SERVER_PID $INSPECTOR_PID
SCRIPT

RUN chmod +x /app/start-with-inspector.sh

EXPOSE 8000 8001

CMD ["/app/start-with-inspector.sh"]
EOF
```

## Step 4: Convert Existing MCP Servers

```bash
# Still in container 109
cd /opt/mcp-hub

# Copy converter scripts
cp /tmp/convert-mcp-to-inspector.sh .
cp /tmp/batch-convert-mcp-servers.sh .
cp /tmp/mcp-servers-config.json .
chmod +x *.sh

# Create servers directory
mkdir -p servers

# Convert servers (example for IT Assistant)
./convert-mcp-to-inspector.sh \
  -t local \
  -p 8001 \
  -o ./servers/it-assistant \
  /path/to/original/it-assistant-mcp

# Or batch convert using config
./batch-convert-mcp-servers.sh \
  -c mcp-servers-config.json \
  -o ./servers
```

## Step 5: Update Web UI with Inspector Support

```bash
# Update the web UI to show Inspector buttons
cd /opt/mcp-hub/public

# Add Inspector functionality to existing UI
# (The UI already has Inspector support in the mcp-hub-with-inspector.md design)
```

## Step 6: Configure Nginx for Inspector Access

```bash
# Update Nginx configuration
cat > /etc/nginx/sites-available/mcp-hub << 'EOF'
server {
    listen 80;
    server_name mcp-hub.local;
    
    # Main MCP Hub
    location / {
        proxy_pass http://localhost:3100;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    # MCP Proxy
    location /mcp/ {
        proxy_pass http://localhost:3101/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Inspector proxies for each server
    location /inspector/it-assistant/ {
        proxy_pass http://localhost:8001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /inspector/browser-use/ {
        proxy_pass http://localhost:8101/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF

# Enable and restart Nginx
ln -sf /etc/nginx/sites-available/mcp-hub /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

## Step 7: Start Services with Inspector

```bash
# Start all services
cd /opt/mcp-hub
docker-compose -f docker-compose-inspector.yml up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

## Step 8: Test Inspector Access

```bash
# Get container IP
CONTAINER_IP=$(ip -4 addr show eth0 | grep inet | awk '{print $2}' | cut -d/ -f1)

echo "Services available at:"
echo "  MCP Hub UI: http://$CONTAINER_IP:3100"
echo "  IT Assistant Inspector: http://$CONTAINER_IP:8001"
echo "  Browser Use Inspector: http://$CONTAINER_IP:8101"
```

## Step 9: Configure Claude Desktop

Update `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "mcp-hub": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/remote-client", "http://mcp-hub.local:3100/mcp"]
    },
    "it-assistant-inspector": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/remote-client", "http://mcp-hub.local:3100/mcp/it-assistant"]
    }
  }
}
```

## Accessing Inspector Features

1. **Web Catalog**: http://mcp-hub.local:3100
   - Browse all MCP servers
   - Click "Open Inspector" button for each server

2. **Direct Inspector Access**:
   - IT Assistant: http://mcp-hub.local:8001
   - Browser Use: http://mcp-hub.local:8101
   - Other servers: Check the port mapping

3. **Inspector Features**:
   - Browse tools, resources, and prompts
   - Execute tools with real-time responses
   - Monitor MCP protocol messages
   - Test server capabilities

## Troubleshooting

### Check Inspector Status
```bash
# In container
docker exec mcp-it-assistant ps aux | grep inspector

# Check logs
docker logs mcp-it-assistant
```

### Test MCP Server
```bash
# Test without Inspector
curl -X POST http://localhost:8000 \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"tools/list","id":1}'

# Test Inspector UI
curl http://localhost:8001
```

### Common Issues

1. **Inspector not starting**: Check INSPECTOR_ENABLED=true in environment
2. **Port conflicts**: Ensure unique Inspector ports for each server
3. **Cannot access Inspector**: Check firewall rules and container networking

## Next Steps

1. Add more MCP servers with Inspector support
2. Configure authentication for remote access
3. Set up SSL certificates for production use
4. Create monitoring dashboards for all servers
5. Document custom tools for each MCP server

## Security Notes

- Inspector provides full access to server capabilities
- Use authentication in production environments
- Consider VPN or SSH tunnels for remote access
- Regularly update Inspector and MCP SDK versions