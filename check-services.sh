#!/bin/bash
echo "=== SERVICE STATUS ==="
echo ""
printf "%-20s %-15s %s\n" "SERVICE" "IP" "STATUS"
printf "%-20s %-15s %s\n" "-------" "--" "------"

# Check services
curl -k -s -o /dev/null -w "%-20s %-15s " "Proxmox" "*************" https://*************:8006 && echo "✅ OK" || echo "❌ DOWN"
curl -k -s -o /dev/null -w "%-20s %-15s " "Backup Server" "************" https://************:8007 && echo "✅ OK" || echo "❌ DOWN"
curl -s -o /dev/null -w "%-20s %-15s " "Dokploy" "************" http://************:3000 && echo "✅ OK" || echo "❌ DOWN"
curl -s -o /dev/null -w "%-20s %-15s " "Browser-MCP" "************" http://************:8000 && echo "✅ OK" || echo "❌ DOWN"
curl -s -o /dev/null -w "%-20s %-15s " "Home Assistant" "************" http://************:8123 && echo "✅ OK" || echo "❌ DOWN"